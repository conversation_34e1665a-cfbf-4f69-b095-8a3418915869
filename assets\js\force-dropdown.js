/**
 * Force Dropdown Display
 * This script forcefully ensures the dropdown menu displays correctly
 */

// Execute when DOM is ready
document.addEventListener('DOMContentLoaded', setupForceDropdown);
window.addEventListener('load', setupForceDropdown);

// Also execute now in case page is already loaded
if (document.readyState !== 'loading') {
    setupForceDropdown();
}

function setupForceDropdown() {
    console.log('🔧 Setting up forced dropdown handler');
    
    // Find the dropdown toggle button and content
    const moreButton = document.querySelector('.dropdown-toggle button, #dropdownToggle button');
    
    if (moreButton) {
        console.log('Found more button, adding robust click handler');
        
        // Remove existing handlers
        moreButton.removeAttribute('onclick');
        
        // Create a fresh range of event handlers to ensure it works
        moreButton.addEventListener('click', forceShowDropdown);
        moreButton.addEventListener('mousedown', forceShowDropdown);
        moreButton.addEventListener('mouseup', forceShowDropdown);
        
        // Also make it available globally
        window.forceShowDropdown = forceShowDropdown;
        
        // Update button to use our forced method
        moreButton.setAttribute('onclick', 'forceShowDropdown(event); return false;');
    } else {
        console.warn('More button not found, will retry later');
        setTimeout(setupForceDropdown, 500);
    }
}

// Function to forcefully show dropdown
function forceShowDropdown(event) {
    // Stop event propagation to prevent other handlers
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }
    
    console.log('🚀 Force-showing dropdown menu');
    
    // Find dropdown elements
    const dropdownToggle = document.getElementById('dropdownToggle');
    const dropdownContent = document.getElementById('dropdownContent');
    
    if (!dropdownContent) {
        console.error('Dropdown content element not found');
        return;
    }
    
    // Determine current state (for toggling)
    const isCurrentlyVisible = dropdownContent.classList.contains('show') && 
                              window.getComputedStyle(dropdownContent).display !== 'none';
    
    console.log('Current dropdown visibility:', isCurrentlyVisible);
    
    if (isCurrentlyVisible) {
        // If visible, hide it
        dropdownContent.classList.remove('show');
        dropdownContent.style.display = 'none';
        dropdownContent.style.visibility = 'hidden';
        dropdownContent.style.opacity = '0';
        console.log('Dropdown hidden');
    } else {
        // If hidden, FORCE show it with critical styles
        dropdownContent.classList.add('show');
        
        // Force critical styles
        const criticalStyles = {
            'display': 'block',
            'visibility': 'visible',
            'opacity': '1',
            'z-index': '9999999',
            'position': 'absolute',
            'top': '45px',
            'right': '0',
            'background-color': 'rgba(0, 0, 0, 0.95)',
            'border': '2px solid #00d4ff',
            'min-width': '200px',
            'border-radius': '8px',
            'box-shadow': '0 0 20px rgba(0, 212, 255, 0.3)',
            'pointer-events': 'auto'
        };
        
        // Apply all critical styles
        Object.keys(criticalStyles).forEach(property => {
            dropdownContent.style.setProperty(property, criticalStyles[property], 'important');
        });
        
        // Add a strong indicator for debugging
        console.log('Dropdown forcefully shown');
        
        // Check content and add if empty
        if (dropdownContent.children.length === 0) {
            console.warn('Dropdown content is empty, adding visible indicator');
            dropdownContent.innerHTML = `
                <li style="padding: 10px; color: white; text-align: center;">
                    <strong style="color: #00d4ff;">Menu Items</strong>
                    <div style="margin-top: 5px; font-size: 0.9em;">No additional items found</div>
                </li>
            `;
        }
    }
    
    // Return false to prevent default action
    return false;
}

// Document click handler to close dropdown when clicking outside
document.addEventListener('click', function(event) {
    const dropdownToggle = document.getElementById('dropdownToggle');
    const dropdownContent = document.getElementById('dropdownContent');
    
    // Close if clicking outside the dropdown
    if (dropdownToggle && dropdownContent && 
        !dropdownToggle.contains(event.target) &&
        dropdownContent.classList.contains('show')) {
        
        dropdownContent.classList.remove('show');
        dropdownContent.style.display = 'none';
    }
});