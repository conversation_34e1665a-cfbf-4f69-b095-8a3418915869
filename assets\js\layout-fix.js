/**
 * Game layout fixes for iframe and content
 */
document.addEventListener('DOMContentLoaded', function() {
    // Fix for iframe sizing issues
    const gameFrame = document.querySelector('.game-frame');
    const gameIframe = document.querySelector('.game-iframe');
    
    if (gameFrame && gameIframe) {
        // Ensure iframe is properly sized
        gameIframe.style.width = '100%';
        gameIframe.style.height = '100%';
        
        // Prevent any scrollbars
        gameIframe.style.overflow = 'hidden';
        
        // Fix any existing display issues
        if (gameIframe.style.display === 'none') {
            // Check if the game should be shown (if play was clicked)
            const playButton = document.querySelector('.play-button');
            if (playButton && window.getComputedStyle(playButton).display === 'none') {
                gameIframe.style.display = 'block';
            }
        }
    }
    
    // Fix duplicate content if still present
    const gameContent = document.querySelector('.game-content');
    if (gameContent) {
        // Find all h2 elements within the content
        const headings = gameContent.querySelectorAll('h2');
        
        // If there are multiple h2s with the same text, remove duplicates
        if (headings.length > 1) {
            const firstHeadingText = headings[0].textContent.trim();
            for (let i = 1; i < headings.length; i++) {
                if (headings[i].textContent.trim() === firstHeadingText) {
                    // Remove this duplicate heading and the next paragraph
                    let nextElement = headings[i].nextElementSibling;
                    headings[i].remove();
                    if (nextElement && nextElement.tagName === 'P') {
                        nextElement.remove();
                    }
                }
            }
        }
    }
});