<?php
/**
 * D27 Gaming Theme Security Enhancements
 * 
 * @package D27_Gaming_Theme
 * @since 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize security enhancements
 */
function d27_init_security_enhancements() {
    // Input validation and sanitization
    add_action('init', 'd27_setup_input_validation');
    
    // AJAX security
    add_action('wp_ajax_submit_game_rating', 'd27_secure_submit_rating');
    add_action('wp_ajax_nopriv_submit_game_rating', 'd27_secure_submit_rating');
    add_action('wp_ajax_increment_play_count', 'd27_secure_increment_play_count');
    add_action('wp_ajax_nopriv_increment_play_count', 'd27_secure_increment_play_count');
    
    // Security headers
    add_action('send_headers', 'd27_add_security_headers');
    
    // Login security
    add_action('wp_login_failed', 'd27_log_failed_login');
    add_filter('authenticate', 'd27_limit_login_attempts', 30, 3);
    
    // File upload security
    add_filter('upload_mimes', 'd27_secure_upload_mimes');
    add_filter('wp_handle_upload_prefilter', 'd27_validate_file_upload');
    
    // Error handling
    add_action('wp_head', 'd27_setup_error_handling');
    
    // Content Security Policy
    add_action('wp_head', 'd27_add_csp_header', 1);
}
add_action('after_setup_theme', 'd27_init_security_enhancements');

/**
 * Setup input validation
 */
function d27_setup_input_validation() {
    // Sanitize all GET and POST data
    if (!empty($_GET)) {
        $_GET = d27_sanitize_input_array($_GET);
    }
    
    if (!empty($_POST)) {
        $_POST = d27_sanitize_input_array($_POST);
    }
}

/**
 * Recursively sanitize input arrays
 */
function d27_sanitize_input_array($array) {
    $sanitized = array();
    
    foreach ($array as $key => $value) {
        $key = sanitize_key($key);
        
        if (is_array($value)) {
            $sanitized[$key] = d27_sanitize_input_array($value);
        } else {
            $sanitized[$key] = sanitize_text_field($value);
        }
    }
    
    return $sanitized;
}

/**
 * Secure game rating submission
 */
function d27_secure_submit_rating() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'd27_nonce') && 
        !wp_verify_nonce($_POST['nonce'], 'd27_ajax_nonce')) {
        wp_die('Security check failed', 'Security Error', array('response' => 403));
    }
    
    // Rate limiting
    if (!d27_check_rate_limit('rating_submission', 5, 300)) { // 5 submissions per 5 minutes
        wp_send_json_error('Too many rating submissions. Please wait before submitting again.');
        return;
    }
    
    // Validate input
    $game_id = intval($_POST['game_id']);
    $rating = intval($_POST['rating']);
    
    if (!$game_id || $rating < 1 || $rating > 5) {
        wp_send_json_error('Invalid input data');
        return;
    }
    
    // Verify game exists
    if (get_post_type($game_id) !== 'game') {
        wp_send_json_error('Invalid game ID');
        return;
    }
    
    try {
        // Process rating
        $result = d27_process_game_rating($game_id, $rating);
        
        if ($result) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error('Failed to submit rating');
        }
    } catch (Exception $e) {
        error_log('D27 Rating Error: ' . $e->getMessage());
        wp_send_json_error('An error occurred while processing your rating');
    }
}

/**
 * Secure play count increment
 */
function d27_secure_increment_play_count() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'd27_nonce') && 
        !wp_verify_nonce($_POST['nonce'], 'd27_ajax_nonce')) {
        wp_die('Security check failed', 'Security Error', array('response' => 403));
    }
    
    // Rate limiting
    if (!d27_check_rate_limit('play_count', 10, 60)) { // 10 plays per minute max
        wp_send_json_error('Rate limit exceeded');
        return;
    }
    
    // Validate input
    $game_id = intval($_POST['game_id']);
    
    if (!$game_id || get_post_type($game_id) !== 'game') {
        wp_send_json_error('Invalid game ID');
        return;
    }
    
    try {
        // Increment play count
        $current_count = (int) get_post_meta($game_id, '_play_count', true);
        $new_count = $current_count + 1;
        
        update_post_meta($game_id, '_play_count', $new_count);
        
        wp_send_json_success(array('play_count' => $new_count));
    } catch (Exception $e) {
        error_log('D27 Play Count Error: ' . $e->getMessage());
        wp_send_json_error('Failed to update play count');
    }
}

/**
 * Rate limiting function
 */
function d27_check_rate_limit($action, $limit, $window) {
    $ip = d27_get_client_ip();
    $key = 'rate_limit_' . $action . '_' . md5($ip);
    
    $attempts = get_transient($key);
    
    if ($attempts === false) {
        set_transient($key, 1, $window);
        return true;
    }
    
    if ($attempts >= $limit) {
        return false;
    }
    
    set_transient($key, $attempts + 1, $window);
    return true;
}

/**
 * Get client IP address securely
 */
function d27_get_client_ip() {
    $ip_keys = array(
        'HTTP_CF_CONNECTING_IP',
        'HTTP_X_FORWARDED_FOR',
        'HTTP_X_FORWARDED',
        'HTTP_X_CLUSTER_CLIENT_IP',
        'HTTP_FORWARDED_FOR',
        'HTTP_FORWARDED',
        'REMOTE_ADDR'
    );
    
    foreach ($ip_keys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            $ip = $_SERVER[$key];
            
            if (strpos($ip, ',') !== false) {
                $ip = explode(',', $ip)[0];
            }
            
            $ip = trim($ip);
            
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}

/**
 * Add security headers
 */
function d27_add_security_headers() {
    if (!is_admin()) {
        // Prevent clickjacking
        header('X-Frame-Options: SAMEORIGIN');
        
        // Prevent MIME type sniffing
        header('X-Content-Type-Options: nosniff');
        
        // XSS Protection
        header('X-XSS-Protection: 1; mode=block');
        
        // Referrer Policy
        header('Referrer-Policy: strict-origin-when-cross-origin');
        
        // Feature Policy
        header('Permissions-Policy: geolocation=(), microphone=(), camera=()');
    }
}

/**
 * Add Content Security Policy
 */
function d27_add_csp_header() {
    if (!is_admin()) {
        $csp = "default-src 'self'; ";
        $csp .= "script-src 'self' 'unsafe-inline' 'unsafe-eval' *.googleapis.com *.gstatic.com; ";
        $csp .= "style-src 'self' 'unsafe-inline' *.googleapis.com; ";
        $csp .= "img-src 'self' data: *.gravatar.com *.wp.com; ";
        $csp .= "font-src 'self' *.googleapis.com *.gstatic.com; ";
        $csp .= "connect-src 'self'; ";
        $csp .= "frame-src 'self' *.youtube.com *.vimeo.com; ";
        $csp .= "object-src 'none'; ";
        $csp .= "base-uri 'self';";
        
        echo '<meta http-equiv="Content-Security-Policy" content="' . esc_attr($csp) . '">' . "\n";
    }
}

/**
 * Log failed login attempts
 */
function d27_log_failed_login($username) {
    $ip = d27_get_client_ip();
    $timestamp = current_time('mysql');
    
    error_log("Failed login attempt for user '{$username}' from IP {$ip} at {$timestamp}");
    
    // Track failed attempts
    $key = 'failed_login_' . md5($ip);
    $attempts = get_transient($key) ?: 0;
    set_transient($key, $attempts + 1, 3600); // Track for 1 hour
}

/**
 * Limit login attempts
 */
function d27_limit_login_attempts($user, $username, $password) {
    if (is_wp_error($user)) {
        return $user;
    }
    
    $ip = d27_get_client_ip();
    $key = 'failed_login_' . md5($ip);
    $attempts = get_transient($key) ?: 0;
    
    if ($attempts >= 5) {
        return new WP_Error('too_many_attempts', 'Too many failed login attempts. Please try again later.');
    }
    
    return $user;
}

/**
 * Secure file upload MIME types
 */
function d27_secure_upload_mimes($mimes) {
    // Remove potentially dangerous file types
    unset($mimes['exe']);
    unset($mimes['php']);
    unset($mimes['js']);
    unset($mimes['swf']);
    
    // Add safe image formats
    $mimes['webp'] = 'image/webp';
    $mimes['avif'] = 'image/avif';
    
    return $mimes;
}

/**
 * Validate file uploads
 */
function d27_validate_file_upload($file) {
    // Check file size (max 5MB for images)
    if ($file['size'] > 5 * 1024 * 1024) {
        $file['error'] = 'File size too large. Maximum allowed size is 5MB.';
        return $file;
    }
    
    // Validate file extension
    $allowed_extensions = array('jpg', 'jpeg', 'png', 'gif', 'webp', 'avif');
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    if (!in_array($file_extension, $allowed_extensions)) {
        $file['error'] = 'File type not allowed. Only image files are permitted.';
        return $file;
    }
    
    // Validate MIME type
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mime_type = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);
    
    $allowed_mimes = array(
        'image/jpeg',
        'image/png', 
        'image/gif',
        'image/webp',
        'image/avif'
    );
    
    if (!in_array($mime_type, $allowed_mimes)) {
        $file['error'] = 'Invalid file type detected.';
        return $file;
    }
    
    return $file;
}

/**
 * Setup error handling
 */
function d27_setup_error_handling() {
    if (defined('WP_DEBUG') && WP_DEBUG) {
        // Development error handling
        echo '<script>
            window.addEventListener("error", function(e) {
                console.error("JavaScript Error:", e.error);
            });
            
            window.addEventListener("unhandledrejection", function(e) {
                console.error("Unhandled Promise Rejection:", e.reason);
            });
        </script>';
    }
}

/**
 * Process game rating with error handling
 */
function d27_process_game_rating($game_id, $rating) {
    try {
        // Get current stats
        $total_votes = (int) get_post_meta($game_id, '_total_votes', true);
        $total_score = (int) get_post_meta($game_id, '_total_score', true);
        
        // Update stats
        $new_total_votes = $total_votes + 1;
        $new_total_score = $total_score + $rating;
        $new_avg_rating = round($new_total_score / $new_total_votes, 1);
        
        // Save to database
        update_post_meta($game_id, '_total_votes', $new_total_votes);
        update_post_meta($game_id, '_total_score', $new_total_score);
        update_post_meta($game_id, '_cached_rating', $new_avg_rating);
        
        return array(
            'average_rating' => $new_avg_rating,
            'total_votes' => $new_total_votes
        );
    } catch (Exception $e) {
        error_log('D27 Rating Processing Error: ' . $e->getMessage());
        return false;
    }
}
