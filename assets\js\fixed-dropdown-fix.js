/**
 * Fixed Dropdown Solution
 * This script creates a completely separate dropdown that attaches to the body
 * to ensure it's not affected by any other element's positioning or z-index
 */

document.addEventListener('DOMContentLoaded', initFixedDropdown);
window.addEventListener('load', initFixedDropdown);

function initFixedDropdown() {
    console.log('Initializing fixed dropdown solution');
    
    // Find the "More" button
    const moreButton = document.querySelector('#dropdownToggle button');
    if (!moreButton) {
        console.warn('More button not found, will retry');
        setTimeout(initFixedDropdown, 500);
        return;
    }
    
    // Create a completely separate dropdown container that attaches to body
    let fixedDropdown = document.getElementById('fixed-dropdown-container');
    
    if (!fixedDropdown) {
        // Create container
        fixedDropdown = document.createElement('div');
        fixedDropdown.id = 'fixed-dropdown-container';
        
        // Style container
        fixedDropdown.style.display = 'none';
        fixedDropdown.style.position = 'fixed';
        fixedDropdown.style.top = '0';
        fixedDropdown.style.left = '0';
        fixedDropdown.style.width = '200px';
        fixedDropdown.style.backgroundColor = 'rgba(0, 0, 0, 0.95)';
        fixedDropdown.style.border = '2px solid #00d4ff';
        fixedDropdown.style.borderRadius = '8px';
        fixedDropdown.style.boxShadow = '0 0 20px rgba(0, 212, 255, 0.6)';
        fixedDropdown.style.padding = '5px';
        fixedDropdown.style.zIndex = '9999999';
        
        // Create list
        const dropdownList = document.createElement('ul');
        dropdownList.id = 'fixed-dropdown-content';
        dropdownList.style.listStyle = 'none';
        dropdownList.style.margin = '0';
        dropdownList.style.padding = '0';
        
        fixedDropdown.appendChild(dropdownList);
        document.body.appendChild(fixedDropdown);
    }
    
    // Remove default onclick and add our own
    moreButton.removeAttribute('onclick');
    moreButton.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        toggleFixedDropdown();
        return false;
    });
    
    // Find and populate hidden menu items
    populateFixedDropdown();
}

function populateFixedDropdown() {
    // Find menu
    const mainMenu = document.querySelector('.nav-menu-dynamic') || document.getElementById('primary-menu');
    if (!mainMenu) return;
    
    // Get all items
    const menuItems = Array.from(mainMenu.querySelectorAll('li:not(#dropdownToggle)'));
    
    // Apply 6-item limit
    const MAX_VISIBLE = 6;
    
    // Get the dropdown content
    const dropdownList = document.getElementById('fixed-dropdown-content');
    if (!dropdownList) return;
    
    // Clear the list
    dropdownList.innerHTML = '';
    
    // Check if we need the dropdown
    if (menuItems.length > MAX_VISIBLE) {
        // Show toggle button
        const toggleButton = document.getElementById('dropdownToggle');
        if (toggleButton) {
            toggleButton.style.display = 'flex';
        }
        
        // Add overflow items to dropdown
        menuItems.forEach((item, index) => {
            if (index >= MAX_VISIBLE) {
                // Get the link
                const link = item.querySelector('a');
                if (link) {
                    // Create list item
                    const listItem = document.createElement('li');
                    listItem.style.margin = '0';
                    listItem.style.padding = '0';
                    listItem.style.borderBottom = '1px solid rgba(0, 212, 255, 0.2)';
                    
                    // Create link
                    const newLink = document.createElement('a');
                    newLink.href = link.href;
                    newLink.textContent = link.textContent;
                    newLink.style.display = 'block';
                    newLink.style.padding = '10px 15px';
                    newLink.style.color = 'white';
                    newLink.style.textDecoration = 'none';
                    newLink.style.fontSize = '14px';
                    
                    // Hover effect
                    newLink.addEventListener('mouseover', function() {
                        this.style.backgroundColor = 'rgba(0, 212, 255, 0.2)';
                        this.style.color = '#00d4ff';
                    });
                    
                    newLink.addEventListener('mouseout', function() {
                        this.style.backgroundColor = 'transparent';
                        this.style.color = 'white';
                    });
                    
                    // Add to list
                    listItem.appendChild(newLink);
                    dropdownList.appendChild(listItem);
                    
                    // Hide the original item
                    item.style.display = 'none';
                }
            } else {
                // Show item
                item.style.display = 'flex';
            }
        });
    }
}

function toggleFixedDropdown() {
    const dropdown = document.getElementById('fixed-dropdown-container');
    if (!dropdown) return;
    
    if (dropdown.style.display === 'none') {
        // Show dropdown
        dropdown.style.display = 'block';
        
        // Get button position
        const button = document.querySelector('#dropdownToggle button');
        if (button) {
            const rect = button.getBoundingClientRect();
            dropdown.style.top = (rect.bottom + 5) + 'px';
            dropdown.style.left = rect.left + 'px';
        }
        
        // Set up close handler
        setTimeout(() => {
            document.addEventListener('click', closeFixedDropdown);
        }, 10);
    } else {
        // Hide dropdown
        dropdown.style.display = 'none';
        document.removeEventListener('click', closeFixedDropdown);
    }
}

function closeFixedDropdown(e) {
    const dropdown = document.getElementById('fixed-dropdown-container');
    const button = document.querySelector('#dropdownToggle button');
    
    if (dropdown && button && 
        !dropdown.contains(e.target) && 
        !button.contains(e.target)) {
        
        dropdown.style.display = 'none';
        document.removeEventListener('click', closeFixedDropdown);
    }
}

// Export for global use
window.toggleFixedDropdown = toggleFixedDropdown;