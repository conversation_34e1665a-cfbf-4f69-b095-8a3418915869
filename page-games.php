<?php
/**
 * Template for Games Page with Clean URLs
 * This template handles all game listing with clean URL parameters
 */

get_header(); ?>

<main class="main-content">
    <div class="container">
        <?php
        // Get URL parameters from WordPress query vars
        $orderby = get_query_var('orderby') ? sanitize_text_field(get_query_var('orderby')) : '';
        $game_category = get_query_var('game_category') ? sanitize_text_field(get_query_var('game_category')) : '';
        $game_tag = get_query_var('game_tag') ? sanitize_text_field(get_query_var('game_tag')) : '';
        $search = get_query_var('search') ? sanitize_text_field(get_query_var('search')) : '';

        // Also check $_GET as fallback for direct URLs
        if (!$orderby && isset($_GET['orderby'])) {
            $orderby = sanitize_text_field($_GET['orderby']);
        }
        if (!$game_category && isset($_GET['game_category'])) {
            $game_category = sanitize_text_field($_GET['game_category']);
        }
        if (!$game_tag && isset($_GET['game_tag'])) {
            $game_tag = sanitize_text_field($_GET['game_tag']);
        }
        if (!$search && isset($_GET['search'])) {
            $search = sanitize_text_field($_GET['search']);
        }

        // Set up query arguments
        $args = array(
            'post_type' => 'game',
            'posts_per_page' => 24,
            'post_status' => 'publish',
            'paged' => get_query_var('paged') ? get_query_var('paged') : 1,
        );

        // Handle orderby parameter
        $page_title = 'All Games';
        if ($orderby) {
            switch ($orderby) {
                case 'popularity':
                    $args['meta_key'] = '_play_count';
                    $args['orderby'] = 'meta_value_num';
                    $args['order'] = 'DESC';
                    $page_title = 'Most Popular Games';
                    break;
                case 'date':
                    $args['orderby'] = 'date';
                    $args['order'] = 'DESC';
                    $page_title = 'Newest Games';
                    break;
                case 'trending':
                    // Trending = popular in last 30 days
                    $args['meta_key'] = '_play_count';
                    $args['orderby'] = 'meta_value_num';
                    $args['order'] = 'DESC';
                    $args['date_query'] = array(
                        array(
                            'after' => '30 days ago',
                        ),
                    );
                    $page_title = 'Trending Games';
                    break;
                case 'most_played':
                    $args['meta_key'] = '_play_count';
                    $args['orderby'] = 'meta_value_num';
                    $args['order'] = 'DESC';
                    $args['posts_per_page'] = 12;
                    $page_title = 'Most Played Games';
                    break;
                case 'rand':
                    $args['orderby'] = 'rand';
                    $page_title = 'Random Games';
                    break;
            }
        }

        // Handle category parameter
        if ($game_category) {
            $args['tax_query'] = array(
                array(
                    'taxonomy' => 'game_category',
                    'field'    => 'slug',
                    'terms'    => $game_category,
                ),
            );
            $category = get_term_by('slug', $game_category, 'game_category');
            $page_title = $category ? $category->name . ' Games' : 'Category Games';
        }

        // Handle tag parameter
        if ($game_tag) {
            $args['tax_query'] = array(
                array(
                    'taxonomy' => 'game_tag',
                    'field'    => 'slug',
                    'terms'    => $game_tag,
                ),
            );
            $tag = get_term_by('slug', $game_tag, 'game_tag');
            $page_title = $tag ? $tag->name . ' Games' : 'Tagged Games';
        }

        // Handle search parameter
        if ($search) {
            $args['s'] = urldecode($search);
            $page_title = 'Search Results for: ' . esc_html($args['s']);
        }

        // Get games
        $games_query = new WP_Query($args);
        ?>

        <!-- Games Header Section -->
        <div class="games-hero-section">
            <div class="games-hero-content">
                <h1 class="games-hero-title">
                    <span class="games-icon">🎮</span>
                    <?php echo esc_html($page_title); ?>
                </h1>
                <p class="games-hero-description">
                    <?php
                    switch($orderby) {
                        case 'popularity':
                            echo 'Discover the most popular Geometry Dash games!';
                            break;
                        case 'date':
                            echo 'Check out the newest Geometry Dash games!';
                            break;
                        case 'trending':
                            echo 'Play the trending Geometry Dash games!';
                            break;
                        case 'most_played':
                            echo 'Experience the most played Geometry Dash games!';
                            break;
                        case 'rand':
                            echo 'Try some random Geometry Dash games!';
                            break;
                        default:
                            if ($game_category) {
                                echo 'Explore amazing ' . esc_html($game_category) . ' games!';
                            } elseif ($game_tag) {
                                echo 'Discover games tagged with ' . esc_html($game_tag) . '!';
                            } elseif ($search) {
                                echo 'Search results for "' . esc_html($search) . '"';
                            } else {
                                echo 'Discover all our amazing Geometry Dash games!';
                            }
                    }
                    ?>
                </p>
            </div>
        </div>

        <!-- Games Controls Section -->
        <div class="games-controls-section">
            <div class="games-controls-left">
                <div class="sort-control">
                    <label for="games-sort">Sort by:</label>
                    <select id="games-sort" onchange="handleSortChange(this.value)">
                        <option value="latest" <?php echo ($orderby === 'date' || !$orderby) ? 'selected' : ''; ?>>Latest</option>
                        <option value="popularity" <?php echo ($orderby === 'popularity') ? 'selected' : ''; ?>>Popular</option>
                        <option value="trending" <?php echo ($orderby === 'trending') ? 'selected' : ''; ?>>Trending</option>
                        <option value="most_played" <?php echo ($orderby === 'most_played') ? 'selected' : ''; ?>>Most Played</option>
                        <option value="random" <?php echo ($orderby === 'rand') ? 'selected' : ''; ?>>Random</option>
                    </select>
                </div>

                <div class="filter-tags">
                    <span class="filter-label">Filter by tags:</span>
                    <div class="tag-buttons">
                        <button class="tag-btn active" data-tag="all">All</button>
                        <?php
                        $game_tags = get_terms(array(
                            'taxonomy' => 'game_tag',
                            'hide_empty' => true,
                            'number' => 10
                        ));
                        if ($game_tags && !is_wp_error($game_tags)) {
                            foreach ($game_tags as $tag) {
                                $active_class = ($game_tag === $tag->slug) ? 'active' : '';
                                echo '<button class="tag-btn ' . $active_class . '" data-tag="' . esc_attr($tag->slug) . '">' . esc_html($tag->name) . '</button>';
                            }
                        }
                        ?>
                    </div>
                </div>
            </div>

            <div class="games-controls-right">
                <div class="view-toggle">
                    <button class="view-btn grid-view active" data-view="grid" title="Grid View">
                        <span>⊞</span> Grid
                    </button>
                    <button class="view-btn list-view" data-view="list" title="List View">
                        <span>☰</span> List
                    </button>
                </div>
            </div>
        </div>

        <?php if ($games_query->have_posts()) : ?>
            <div class="games-grid">
                <?php while ($games_query->have_posts()) : $games_query->the_post(); ?>
                    <article class="game-card">
                        <a href="<?php the_permalink(); ?>">
                            <div class="game-thumbnail">
                                <?php if (has_post_thumbnail()) : ?>
                                    <?php the_post_thumbnail('game-thumbnail'); ?>
                                <?php else : ?>
                                    <div class="no-image">
                                        <span class="game-icon">🎮</span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="game-card-content">
                                <h3><?php the_title(); ?></h3>
                            </div>
                        </a>
                    </article>
                <?php endwhile; ?>
            </div>

            <!-- Pagination -->
            <div class="pagination-wrapper">
                <?php
                $big = 999999999; // need an unlikely integer
                echo paginate_links(array(
                    'base' => str_replace($big, '%#%', esc_url(get_pagenum_link($big))),
                    'format' => '?paged=%#%',
                    'current' => max(1, get_query_var('paged')),
                    'total' => $games_query->max_num_pages,
                    'show_all' => false,
                    'end_size' => 1,
                    'mid_size' => 2,
                    'prev_next' => true,
                    'prev_text' => '← Previous',
                    'next_text' => 'Next →',
                    'type' => 'list',
                ));
                ?>
            </div>
        <?php else : ?>
            <div class="no-games-found">
                <h2>No games found</h2>
                <p>Sorry, no games match your criteria. Try browsing our <a href="<?php echo home_url('/'); ?>">homepage</a> for popular games.</p>
            </div>
        <?php endif; ?>

        <?php wp_reset_postdata(); ?>
    </div>
</main>

<style>
/* Games Hero Section */
.games-hero-section {
    background: linear-gradient(135deg, rgba(0, 153, 204, 0.1), rgba(0, 212, 255, 0.1));
    padding: 3rem 0;
    margin-bottom: 2rem;
    border-radius: 15px;
    text-align: center;
}

.games-hero-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 2rem;
}

.games-hero-title {
    font-size: 3rem;
    color: #00d4ff;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.games-icon {
    font-size: 3.5rem;
}

.games-hero-description {
    font-size: 1.2rem;
    color: #ccc;
    margin: 0;
}

/* Games Controls Section */
.games-controls-section {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
    flex-wrap: wrap;
    gap: 1rem;
}

.games-controls-left {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    flex: 1;
}

.games-controls-right {
    display: flex;
    align-items: center;
}

/* Sort Control */
.sort-control {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.sort-control label {
    color: #fff;
    font-weight: bold;
}

.sort-control select {
    background: rgba(0, 0, 0, 0.9);
    border: 1px solid #00d4ff;
    color: #fff;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    font-size: 1rem;
}

.sort-control select:focus {
    outline: none;
    border-color: #00d4ff;
    box-shadow: 0 0 5px rgba(0, 212, 255, 0.5);
}

/* Filter Tags */
.filter-tags {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-label {
    color: #00d4ff;
    font-weight: bold;
}

.tag-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid #555;
    color: #ccc;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.tag-btn:hover {
    background: rgba(0, 212, 255, 0.2);
    border-color: #00d4ff;
    color: #fff;
}

.tag-btn.active {
    background: #00d4ff;
    border-color: #00d4ff;
    color: #000;
    font-weight: bold;
}

/* View Toggle */
.view-toggle {
    display: flex;
    background: rgba(0, 0, 0, 0.9);
    border-radius: 5px;
    overflow: hidden;
    border: 1px solid #555;
}

.view-btn {
    background: transparent;
    border: none;
    color: #ccc;
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.view-btn:hover {
    background: rgba(0, 212, 255, 0.2);
    color: #fff;
}

.view-btn.active {
    background: #00d4ff;
    color: #000;
    font-weight: bold;
}

/* Responsive Design */
@media (max-width: 768px) {
    .games-hero-title {
        font-size: 2rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .games-icon {
        font-size: 2.5rem;
    }

    .games-controls-section {
        flex-direction: column;
        align-items: stretch;
    }

    .games-controls-left {
        order: 1;
    }

    .games-controls-right {
        order: 2;
        justify-content: center;
    }

    .tag-buttons {
        justify-content: center;
    }
}
</style>

<script>
// Handle sort change
function handleSortChange(value) {
    let url = '';
    switch(value) {
        case 'latest':
            url = '/newest/';
            break;
        case 'popularity':
            url = '/popularity/';
            break;
        case 'trending':
            url = '/trending/';
            break;
        case 'most_played':
            url = '/most-played/';
            break;
        case 'random':
            url = '/random/';
            break;
    }
    if (url) {
        window.location.href = url;
    }
}

// Handle tag filtering and view toggle
document.addEventListener('DOMContentLoaded', function() {
    const tagButtons = document.querySelectorAll('.tag-btn');
    const viewButtons = document.querySelectorAll('.view-btn');
    const gamesGrid = document.querySelector('.games-grid');

    // Tag button functionality
    tagButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tag = this.dataset.tag;

            // Remove active class from all buttons
            tagButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');

            // Navigate to tag URL
            if (tag === 'all') {
                window.location.href = '/newest/';
            } else {
                window.location.href = '/tag/' + tag + '/';
            }
        });
    });

    // View toggle functionality
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const view = this.dataset.view;

            // Remove active class from all view buttons
            viewButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');

            // Toggle grid/list view
            if (view === 'grid') {
                gamesGrid.classList.remove('list-view');
                gamesGrid.classList.add('grid-view');
            } else {
                gamesGrid.classList.remove('grid-view');
                gamesGrid.classList.add('list-view');
            }
        });
    });
});
</script>

<?php get_footer(); ?>
