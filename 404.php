<?php
/**
 * The template for displaying 404 pages (not found)
 */

get_header(); ?>

<div class="error-404-container">
    <div class="error-content">
        <h1>Nothing Found</h1>
        <p>It looks like nothing was found at this location. Maybe try a search?</p>
        
        <?php get_search_form(); ?>
        
        <?php if (WP_DEBUG) : ?>
        <div class="debug-info" style="background: #f9f9f9; padding: 15px; margin: 20px 0; border-radius: 5px; font-family: monospace; font-size: 12px;">
            <h3>Debug Information:</h3>
            <p><strong>Requested URL:</strong> <?php echo esc_html($_SERVER['REQUEST_URI']); ?></p>
            <p><strong>Query String:</strong> <?php echo esc_html($_SERVER['QUERY_STRING']); ?></p>
            <p><strong>Post Type:</strong> <?php echo esc_html(get_post_type()); ?></p>
            <p><strong>Is Single:</strong> <?php echo is_single() ? 'Yes' : 'No'; ?></p>
            <p><strong>Is Game:</strong> <?php echo (get_post_type() === 'game') ? 'Yes' : 'No'; ?></p>
            <p><strong>Current Template:</strong> 404.php</p>
            
            <?php
            // Check if this looks like a game URL
            $url_path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
            if (preg_match('/^\/games\/([^\/]+)\/?$/', $url_path, $matches)) {
                $game_slug = $matches[1];
                echo '<p><strong>Detected Game Slug:</strong> ' . esc_html($game_slug) . '</p>';
                
                // Check if game exists
                $game_post = get_page_by_path($game_slug, OBJECT, 'game');
                if ($game_post) {
                    echo '<p><strong>Game Post Found:</strong> ID ' . $game_post->ID . ' - ' . esc_html($game_post->post_title) . '</p>';
                    echo '<p><strong>Post Status:</strong> ' . esc_html($game_post->post_status) . '</p>';
                } else {
                    echo '<p><strong>Game Post:</strong> Not found in database</p>';
                    
                    // Check for similar games
                    $similar_games = get_posts(array(
                        'post_type' => 'game',
                        'posts_per_page' => 5,
                        's' => $game_slug,
                        'post_status' => 'publish'
                    ));
                    
                    if ($similar_games) {
                        echo '<p><strong>Similar Games Found:</strong></p>';
                        echo '<ul>';
                        foreach ($similar_games as $game) {
                            echo '<li><a href="' . get_permalink($game->ID) . '">' . esc_html($game->post_title) . '</a> (slug: ' . esc_html($game->post_name) . ')</li>';
                        }
                        echo '</ul>';
                    }
                }
            }
            ?>
        </div>
        <?php endif; ?>
    </div>
</div>

<style>
.error-404-container {
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    text-align: center;
}

.error-content h1 {
    font-size: 2.5em;
    color: #333;
    margin-bottom: 20px;
}

.error-content p {
    font-size: 1.1em;
    color: #666;
    margin-bottom: 30px;
}

.debug-info {
    text-align: left;
    max-width: 100%;
    overflow-x: auto;
}

.debug-info h3 {
    margin-top: 0;
    color: #333;
}

.debug-info p {
    margin: 5px 0;
    font-size: 12px;
}

.debug-info ul {
    margin: 10px 0;
    padding-left: 20px;
}

.debug-info li {
    margin: 5px 0;
}
</style>

<?php get_footer(); ?>
