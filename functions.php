<?php
/**
 * D27 Gaming Theme Functions
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Theme setup
function d27_theme_setup() {
    // Add theme support (title-tag removed - handled by SEO meta)
    add_theme_support('post-thumbnails');
    add_theme_support('custom-logo');
    add_theme_support('html5', array('search-form', 'comment-form', 'comment-list', 'gallery', 'caption'));
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'd27-gaming'),
        'footer' => __('Footer Menu', 'd27-gaming'),
    ));
    
    // Add image sizes
    add_image_size('game-thumbnail', 200, 200, true);
    add_image_size('game-featured', 800, 500, true);
}
add_action('after_setup_theme', 'd27_theme_setup');

// Enqueue scripts and styles
function d27_enqueue_scripts() {
    // Cache buster version
    $version = '1.0.7'; // Increased version
    
    // Enqueue main stylesheet with higher version
    wp_enqueue_style('d27-style', get_stylesheet_uri(), array(), $version);
    
    // Enqueue interactive elements stylesheet
    wp_enqueue_style('d27-interactive', get_template_directory_uri() . '/assets/css/interactive-elements.css', array('d27-style'), $version);
    
    // Enqueue header fix stylesheet - CRITICAL: Must be after main styles
    wp_enqueue_style('d27-header-fix', get_template_directory_uri() . '/assets/css/header-fix.css', array('d27-style', 'd27-interactive'), $version);
    
    // Enqueue logo visibility fix - HIGH PRIORITY
    wp_enqueue_style('d27-logo-visibility', get_template_directory_uri() . '/assets/css/logo-visibility-fix.css', array('d27-style', 'd27-interactive', 'd27-header-fix'), $version);
    
    // Enqueue dropdown fix - HIGH PRIORITY
    wp_enqueue_style('d27-dropdown-fix', get_template_directory_uri() . '/assets/css/dropdown-fix.css', array('d27-style', 'd27-interactive', 'd27-header-fix', 'd27-logo-visibility'), $version);
    
    // Enqueue responsive dropdown styles - ABSOLUTE HIGHEST PRIORITY 
    wp_enqueue_style('d27-responsive-dropdown', get_template_directory_uri() . '/assets/css/responsive-dropdown.css', array('d27-style', 'd27-interactive', 'd27-header-fix', 'd27-logo-visibility', 'd27-dropdown-fix'), $version . '.' . time());
    
    // Dequeue any potential conflicting scripts
    wp_dequeue_script('d27-dropdown-handler');
    wp_dequeue_script('d27-force-dropdown');
    wp_dequeue_script('d27-fixed-dropdown');
    
    // Enqueue game layout fixes for single game pages
    if (is_singular('game')) {
        // Layout fixes CSS
        wp_enqueue_style('d27-game-layout-fixes', get_template_directory_uri() . '/assets/css/game-layout-fixes.css', array('d27-style'), $version);
        
        // Content jump prevention script
        wp_enqueue_script('d27-prevent-content-jump', get_template_directory_uri() . '/assets/js/prevent-content-jump.js', array('jquery'), $version, true);
    }
    
    // Enqueue JavaScript with cache-busting version
    wp_enqueue_script('d27-play-handler', get_template_directory_uri() . '/assets/js/play-handler.js', array('jquery'), $version, true);
    
    // Enqueue fallback handlers for game interaction
    wp_enqueue_script('d27-fallback-handlers', get_template_directory_uri() . '/assets/js/fallback-handlers.js', array('d27-play-handler'), '1.0.0', true);
    
    // Enqueue layout fix script for game pages
    if (is_singular('game')) {
        wp_enqueue_script('d27-layout-fix', get_template_directory_uri() . '/assets/js/layout-fix.js', array('d27-play-handler'), '1.0.0', true);
    }
    
    // Debug tools removed for production security
    
    // Localize script for AJAX with multiple nonces for compatibility
    $ajax_data = array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('d27_nonce'),
        'wp_rest_nonce' => wp_create_nonce('wp_rest'),
        'ajax_nonce' => wp_create_nonce('d27_ajax_nonce'),
        'site_url' => site_url(),
        'debug' => defined('WP_DEBUG') && WP_DEBUG,
        'game_post_type' => 'game',
        'version' => '1.0.2'
    );
    
    wp_localize_script('d27-play-handler', 'd27_ajax', $ajax_data);
}
add_action('wp_enqueue_scripts', 'd27_enqueue_scripts');

// Register Custom Post Type: Games
function d27_register_game_post_type() {
    $labels = array(
        'name' => 'Games',
        'singular_name' => 'Game',
        'menu_name' => 'Games',
        'add_new' => 'Add New Game',
        'add_new_item' => 'Add New Game',
        'edit_item' => 'Edit Game',
        'new_item' => 'New Game',
        'view_item' => 'View Game',
        'search_items' => 'Search Games',
        'not_found' => 'No games found',
        'not_found_in_trash' => 'No games found in trash'
    );
    
    $args = array(
        'labels' => $labels,
        'public' => true,
        'has_archive' => true,
        'menu_icon' => 'dashicons-games',
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'comments', 'custom-fields'),
        'rewrite' => array('slug' => 'games'),
        'show_in_rest' => true,
    );
    
    register_post_type('game', $args);
}
add_action('init', 'd27_register_game_post_type');

// Register Game Tags Taxonomy
function d27_register_game_taxonomy() {
    $labels = array(
        'name' => 'Game Tags',
        'singular_name' => 'Game Tag',
        'search_items' => 'Search Game Tags',
        'all_items' => 'All Game Tags',
        'edit_item' => 'Edit Game Tag',
        'update_item' => 'Update Game Tag',
        'add_new_item' => 'Add New Game Tag',
        'new_item_name' => 'New Game Tag Name',
        'menu_name' => 'Game Tags',
    );
    
    register_taxonomy('game_tag', 'game', array(
        'labels' => $labels,
        'hierarchical' => false,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'game-tag'),
        'show_in_rest' => true,
    ));
}
add_action('init', 'd27_register_game_taxonomy');

// Add custom meta boxes for games
function d27_add_game_meta_boxes() {
    add_meta_box(
        'd27_game_details',
        'Game Details',
        'd27_game_details_callback',
        'game',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'd27_add_game_meta_boxes');

// Meta box callback
function d27_game_details_callback($post) {
    wp_nonce_field('d27_save_game_details', 'd27_game_details_nonce');

    $iframe_link = get_post_meta($post->ID, '_iframe_link', true);
    $instructions = get_post_meta($post->ID, '_game_instructions', true);

    // Get automatic rating and play count data (read-only display)
    $total_votes = (int) get_post_meta($post->ID, '_total_votes', true);
    $total_score = (int) get_post_meta($post->ID, '_total_score', true);
    $avg_rating = $total_votes > 0 ? round($total_score / $total_votes, 1) : 0;
    $play_count = get_post_meta($post->ID, '_play_count', true);

    echo '<table class="form-table">';
    echo '<tr><th><label for="iframe_link">Game Iframe URL:</label></th>';
    echo '<td><input type="url" id="iframe_link" name="iframe_link" value="' . esc_attr($iframe_link) . '" style="width: 100%;" /></td></tr>';

    echo '<tr><th><label for="game_instructions">Game Instructions:</label></th>';
    echo '<td><textarea id="game_instructions" name="game_instructions" rows="5" style="width: 100%;">' . esc_textarea($instructions) . '</textarea></td></tr>';

    // Display automatic rating and play count (read-only)
    echo '<tr><th>Current Rating:</th>';
    echo '<td><strong>' . $avg_rating . '/5</strong> (' . $total_votes . ' votes) <em style="color: #666;">- Updated automatically by player ratings</em></td></tr>';

    echo '<tr><th>Play Count:</th>';
    echo '<td><strong>' . number_format($play_count ?: 0) . '</strong> plays <em style="color: #666;">- Updated automatically when players play the game</em></td></tr>';

    echo '</table>';
}

// Save meta box data
function d27_save_game_details($post_id) {
    if (!isset($_POST['d27_game_details_nonce']) || !wp_verify_nonce($_POST['d27_game_details_nonce'], 'd27_save_game_details')) {
        return;
    }

    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Only save editable fields - rating and play_count are now automatic
    $fields = array('iframe_link', 'game_instructions');

    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            update_post_meta($post_id, '_' . $field, sanitize_text_field($_POST[$field]));
        }
    }
}
add_action('save_post', 'd27_save_game_details');

// Include customizer
require get_template_directory() . '/inc/customizer.php';

// Include theme options
require get_template_directory() . '/inc/theme-options.php';

// Include SEO meta
require get_template_directory() . '/inc/seo-meta.php';

// Include WebP converter
require get_template_directory() . '/inc/webp-converter.php';
require get_template_directory() . '/inc/webp-admin.php';

// Include Auto Alt Text generator
require get_template_directory() . '/inc/auto-alt-text.php';

// Ensure our SEO meta tags take priority (temporarily disabled)
function d27_remove_conflicting_seo() {
    // Temporarily disabled to fix critical error
    return;

    // Remove Yoast SEO meta if present
    if (defined('WPSEO_VERSION')) {
        remove_action('wp_head', array('WPSEO_Frontend', 'get_instance'), 1);
    }

    // Remove RankMath SEO meta if present
    if (defined('RANK_MATH_VERSION')) {
        remove_action('wp_head', array('RankMath\Frontend\Frontend', 'get_instance'), 1);
    }

    // Remove All in One SEO meta if present
    if (defined('AIOSEOP_VERSION')) {
        remove_action('wp_head', array('All_in_One_SEO_Pack', 'wp_head'), 1);
    }
}
// add_action('init', 'd27_remove_conflicting_seo', 1); // Temporarily disabled

// WordPress rewrite rules for clean URLs
function d27_add_rewrite_rules() {
    add_rewrite_rule('^popularity/?$', 'index.php?pagename=games&orderby=popularity', 'top');
    add_rewrite_rule('^newest/?$', 'index.php?pagename=games&orderby=date', 'top');
    add_rewrite_rule('^trending/?$', 'index.php?pagename=games&orderby=trending', 'top');
    add_rewrite_rule('^most-played/?$', 'index.php?pagename=games&orderby=most_played', 'top');
    add_rewrite_rule('^random/?$', 'index.php?pagename=games&orderby=rand', 'top');
    add_rewrite_rule('^category/([^/]+)/?$', 'index.php?pagename=games&game_category=$matches[1]', 'top');
    // Removed custom tag rewrite rule to use WordPress native taxonomy archives
    // add_rewrite_rule('^tag/([^/]+)/?$', 'index.php?pagename=games&game_tag=$matches[1]', 'top');
    add_rewrite_rule('^search/([^/]+)/?$', 'index.php?pagename=games&search=$matches[1]', 'top');
}
add_action('init', 'd27_add_rewrite_rules');





// Add custom query vars
function d27_add_query_vars($vars) {
    $vars[] = 'orderby';
    $vars[] = 'game_category';
    $vars[] = 'game_tag';
    $vars[] = 'search';
    return $vars;
}
add_filter('query_vars', 'd27_add_query_vars');

// Flush rewrite rules on theme activation
function d27_flush_rules() {
    d27_register_game_post_type();
    d27_add_rewrite_rules();
    flush_rewrite_rules();
}
add_action('after_switch_theme', 'd27_flush_rules');

// Add admin notice and flush option
function d27_admin_flush_permalinks() {
    if (isset($_GET['d27_flush_permalinks']) && $_GET['d27_flush_permalinks'] === 'true') {
        d27_register_game_post_type();
        d27_register_game_taxonomy();
        d27_add_rewrite_rules();
        flush_rewrite_rules();

        echo '<div class="notice notice-success is-dismissible">';
        echo '<p><strong>Success!</strong> Game permalinks have been flushed. Game URLs are restored to standard format (/games/game-name/).</p>';
        echo '</div>';
    }
}
add_action('admin_notices', 'd27_admin_flush_permalinks');

// Remove the permalink flush flag since we're done with URL changes
update_option('d27_permalinks_need_flush', false);

// Helper function to generate clean URLs
function d27_get_clean_url($type, $value = '') {
    $base_url = home_url('/');

    switch ($type) {
        case 'popularity':
            return $base_url . 'popularity/';
        case 'newest':
            return $base_url . 'newest/';
        case 'trending':
            return $base_url . 'trending/';
        case 'most-played':
            return $base_url . 'most-played/';
        case 'random':
            return $base_url . 'random/';
        case 'category':
            return $base_url . 'category/' . sanitize_title($value) . '/';
        case 'tag':
            return $base_url . 'tag/' . sanitize_title($value) . '/';
        case 'search':
            return $base_url . 'search/' . urlencode($value) . '/';
        default:
            return $base_url . 'games/';
    }
}

// Custom function to get tag URL - now uses WordPress native taxonomy URLs
function d27_get_tag_link($tag) {
    if (is_object($tag)) {
        return get_term_link($tag);
    } else {
        $term = get_term_by('slug', $tag, 'game_tag');
        if ($term) {
            return get_term_link($term);
        }
    }
    return home_url('/games/');
}

// Function to get current clean URL parameters
function d27_get_current_url_params() {
    $params = array();

    // Check for orderby parameter
    if (get_query_var('orderby')) {
        $params['orderby'] = get_query_var('orderby');
    }

    // Check for category parameter
    if (get_query_var('game_category')) {
        $params['game_category'] = get_query_var('game_category');
    }

    // Check for tag parameter
    if (get_query_var('game_tag')) {
        $params['game_tag'] = get_query_var('game_tag');
    }

    // Check for search parameter
    if (get_query_var('search')) {
        $params['search'] = get_query_var('search');
    }

    return $params;
}

// AJAX handler for game play count
function d27_increment_play_count() {
    // More permissive error handling
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'd27_nonce')) {
        wp_send_json_error(array('message' => 'Invalid security token. Please refresh the page and try again.'));
        return;
    }
    
    if (!isset($_POST['game_id'])) {
        wp_send_json_error(array('message' => 'Game ID is missing'));
        return;
    }
    
    $game_id = intval($_POST['game_id']);
    
    if ($game_id <= 0) {
        wp_send_json_error(array('message' => 'Invalid game ID'));
        return;
    }
    
    // Delete any existing object cache for this post meta
    wp_cache_delete($game_id, 'post_meta');
    
    // Get current count directly from database
    global $wpdb;
    $current_count = $wpdb->get_var($wpdb->prepare(
        "SELECT meta_value FROM $wpdb->postmeta WHERE post_id = %d AND meta_key = '_play_count' LIMIT 1",
        $game_id
    ));
    
    // If no result, try the standard method
    if ($current_count === null) {
        $current_count = get_post_meta($game_id, '_play_count', true);
    }
    
    $new_count = $current_count ? $current_count + 1 : 1;
    
    // Update and force database write immediately
    $result = update_post_meta($game_id, '_play_count', $new_count);
    
    // Get all stats to return - this will now be fresh data
    $stats = d27_get_fresh_game_stats($game_id);
    
    if ($result) {
        wp_send_json_success(array(
            'count' => $new_count, 
            'message' => 'Play count updated successfully',
            'stats' => $stats
        ));
    } else {
        wp_send_json_error(array('message' => 'Failed to update play count'));
    }
}
add_action('wp_ajax_increment_play_count', 'd27_increment_play_count');
add_action('wp_ajax_nopriv_increment_play_count', 'd27_increment_play_count');

/**
 * AJAX handler for getting fresh game stats
 */
function d27_get_fresh_game_stats_ajax() {
    // Simple nonce check - but less strict to ensure functionality
    if (isset($_POST['nonce'])) {
        $valid_nonce = wp_verify_nonce($_POST['nonce'], 'wp_rest') || 
                     wp_verify_nonce($_POST['nonce'], 'd27_nonce') || 
                     wp_verify_nonce($_POST['nonce'], 'd27_ajax_nonce');
                     
        if (!$valid_nonce) {
            wp_send_json_error(['message' => 'Invalid security token']);
            return;
        }
    }

    $game_id = isset($_POST['game_id']) ? absint($_POST['game_id']) : 0;
    
    if (!$game_id) {
        wp_send_json_error(['message' => 'Invalid game ID']);
        return;
    }
    
    // Clear post meta cache for this specific post
    wp_cache_delete($game_id, 'post_meta');
    
    // Get fresh stats
    $stats = d27_get_fresh_game_stats($game_id);
    
    wp_send_json_success($stats);
}
add_action('wp_ajax_get_fresh_game_stats', 'd27_get_fresh_game_stats_ajax');
add_action('wp_ajax_nopriv_get_fresh_game_stats', 'd27_get_fresh_game_stats_ajax');

// Get popular games
function d27_get_popular_games($limit = 6) {
    $args = array(
        'post_type' => 'game',
        'posts_per_page' => $limit,
        'meta_key' => '_play_count',
        'orderby' => 'meta_value_num',
        'order' => 'DESC',
        'meta_query' => array(
            array(
                'key' => '_play_count',
                'compare' => 'EXISTS'
            )
        )
    );
    
    return new WP_Query($args);
}

// Get latest games
function d27_get_latest_games($limit = 12) {
    $args = array(
        'post_type' => 'game',
        'posts_per_page' => $limit,
        'orderby' => 'date',
        'order' => 'DESC'
    );
    
    return new WP_Query($args);
}

// Custom excerpt length
function d27_excerpt_length() {
    return 20;
}
add_filter('excerpt_length', 'd27_excerpt_length');

// Custom excerpt more
function d27_excerpt_more() {
    return '...';
}
add_filter('excerpt_more', 'd27_excerpt_more');

// Handle game images and thumbnails
function d27_prepare_game_images($post_id) {
    if (get_post_type($post_id) === 'game') {
        // Ensure we have a thumbnail
        if (!has_post_thumbnail($post_id)) {
            // Try to get the first image from content
            $content = get_post_field('post_content', $post_id);
            preg_match('/<img.+?src=[\'"]([^\'"]+)[\'"].*?>/i', $content, $matches);
            
            if (!empty($matches[1])) {
                // Download and set the image as thumbnail
                require_once(ABSPATH . 'wp-admin/includes/media.php');
                require_once(ABSPATH . 'wp-admin/includes/file.php');
                require_once(ABSPATH . 'wp-admin/includes/image.php');
                
                $image_url = $matches[1];
                $tmp = download_url($image_url);
                
                if (!is_wp_error($tmp)) {
                    $file_array = array(
                        'name' => basename($image_url),
                        'tmp_name' => $tmp
                    );
                    
                    $thumbnail_id = media_handle_sideload($file_array, $post_id);
                    
                    if (!is_wp_error($thumbnail_id)) {
                        set_post_thumbnail($post_id, $thumbnail_id);
                    }
                }
                
                @unlink($tmp);
            }
        }
        
        // Optimize game thumbnails
        if (has_post_thumbnail($post_id)) {
            $thumbnail_id = get_post_thumbnail_id($post_id);
            
            // Generate appropriately sized thumbnails
            $sizes = array(
                'game-thumbnail' => array(200, 200, true),
                'game-featured' => array(800, 500, true)
            );
            
            foreach ($sizes as $size => $dimensions) {
                $image = get_image_dimensions($thumbnail_id, $size);
                if (!$image) {
                    // Size doesn't exist, generate it
                    $full_path = get_attached_file($thumbnail_id);
                    if ($full_path) {
                        $editor = wp_get_image_editor($full_path);
                        if (!is_wp_error($editor)) {
                            $editor->resize($dimensions[0], $dimensions[1], $dimensions[2]);
                            $editor->save();
                        }
                    }
                }
            }
        }
    }
}
add_action('save_post', 'd27_prepare_game_images');

// Helper function to check if image size exists
function get_image_dimensions($image_id, $size) {
    $meta = wp_get_attachment_metadata($image_id);
    if (!empty($meta['sizes'][$size])) {
        return $meta['sizes'][$size];
    }
    return false;
}

// Fix content duplication in game posts
function d27_fix_game_content_duplication($content) {
    if (is_singular('game')) {
        // Get the post title
        $post_title = get_the_title();
        
        // Remove any h1-h6 that matches the title
        $title_pattern = '/<h[1-6][^>]*>' . preg_quote($post_title, '/') . '<\/h[1-6]>/i';
        $content = preg_replace($title_pattern, '', $content);
        
        // Remove duplicate h2 + p combinations (common in game descriptions)
        $content = preg_replace('/<h2[^>]*>' . preg_quote($post_title, '/') . '<\/h2>\s*<p>.*?<\/p>/is', '', $content);
        
        // Remove empty paragraphs and line breaks
        $content = preg_replace([
            '/<p[^>]*>\s*<\/p>/i',
            '/(<br\s*\/?>\s*)+/',
            '/<p[^>]*>\s*(&nbsp;|\s)*\s*<\/p>/i'
        ], '', $content);
        
        // Clean up multiple consecutive line breaks
        $content = preg_replace('/(\r\n|\n|\r){3,}/', "\n\n", $content);
        
        // Ensure content starts clean
        $content = trim($content);
    }
    return $content;
}
add_filter('the_content', 'd27_fix_game_content_duplication', 20);

// Additional function to prevent content duplication in excerpts
function d27_fix_game_excerpt_duplication($excerpt) {
    if (is_singular('game')) {
        $post_title = get_the_title();
        // Remove the title if it appears at the start of the excerpt
        $excerpt = preg_replace('/^' . preg_quote($post_title, '/') . '\s*[-–—]\s*/i', '', $excerpt);
    }
    return $excerpt;
}
add_filter('get_the_excerpt', 'd27_fix_game_excerpt_duplication', 20);

// Ensure proper game iframe handling
function d27_handle_game_iframe($content) {
    if (is_singular('game')) {
        // Remove any iframes from the content as we handle them separately
        $content = preg_replace('/<iframe.*?<\/iframe>/is', '', $content);
    }
    return $content;
}
add_filter('the_content', 'd27_handle_game_iframe', 1);

// Add inline CSS to head
function d27_inline_css() {
    $inline_css_file = get_template_directory() . '/assets/css/inline.css';
    if (file_exists($inline_css_file)) {
        echo '<style type="text/css">';
        include $inline_css_file;
        echo '</style>';
    }
}
add_action('wp_head', 'd27_inline_css');

// Disable WordPress emoji scripts (performance optimization)
// Except in debug mode to ensure maximum compatibility during testing
if (!defined('WP_DEBUG') || !WP_DEBUG) {
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');
}

// Remove unnecessary WordPress features for performance
remove_action('wp_head', 'wp_generator');
remove_action('wp_head', 'wlwmanifest_link');
remove_action('wp_head', 'rsd_link');
remove_action('wp_head', 'wp_shortlink_wp_head');

// Flush rewrite rules on theme activation (original function)
function d27_flush_rewrite_rules_original() {
    d27_register_game_post_type();
    d27_register_game_taxonomy();
    flush_rewrite_rules();
}


add_action('after_switch_theme', 'd27_flush_rewrite_rules_original');

/**
 * AJAX handler for toggling game likes
 */
function d27_toggle_game_like() {
    check_ajax_referer('d27_ajax_nonce', 'nonce');

    $game_id = intval($_POST['game_id']);
    if (!$game_id) {
        wp_die('Invalid game ID');
    }

    $current_count = get_post_meta($game_id, '_like_count', true) ?: 0;
    $current_count = intval($current_count);

    // For simplicity, we'll just increment the count
    // In a real implementation, you'd track user likes to prevent duplicates
    $new_count = $current_count + 1;
    update_post_meta($game_id, '_like_count', $new_count);

    wp_send_json_success(array('count' => $new_count));
}
add_action('wp_ajax_toggle_game_like', 'd27_toggle_game_like');
add_action('wp_ajax_nopriv_toggle_game_like', 'd27_toggle_game_like');

/**
 * AJAX handler for toggling game favorites
 */
function d27_toggle_game_favorite() {
    check_ajax_referer('d27_ajax_nonce', 'nonce');

    $game_id = intval($_POST['game_id']);
    if (!$game_id) {
        wp_die('Invalid game ID');
    }

    $current_count = get_post_meta($game_id, '_favorite_count', true) ?: 0;
    $current_count = intval($current_count);

    // For simplicity, we'll just increment the count
    // In a real implementation, you'd track user favorites to prevent duplicates
    $new_count = $current_count + 1;
    update_post_meta($game_id, '_favorite_count', $new_count);

    wp_send_json_success(array('count' => $new_count));
}
add_action('wp_ajax_toggle_game_favorite', 'd27_toggle_game_favorite');
add_action('wp_ajax_nopriv_toggle_game_favorite', 'd27_toggle_game_favorite');

/**
 * AJAX handler for incrementing game shares
 */
function d27_increment_game_share() {
    check_ajax_referer('d27_ajax_nonce', 'nonce');

    $game_id = intval($_POST['game_id']);
    if (!$game_id) {
        wp_die('Invalid game ID');
    }

    $current_count = get_post_meta($game_id, '_share_count', true) ?: 0;
    $current_count = intval($current_count);
    $new_count = $current_count + 1;

    update_post_meta($game_id, '_share_count', $new_count);

    wp_send_json_success(array('count' => $new_count));
}
add_action('wp_ajax_increment_game_share', 'd27_increment_game_share');
add_action('wp_ajax_nopriv_increment_game_share', 'd27_increment_game_share');



/**
 * Enable comments for game post type
 */
function d27_enable_game_comments() {
    add_post_type_support('game', 'comments');
}
add_action('init', 'd27_enable_game_comments');

/**
 * Auto-generate game instructions based on title and tags - SHORT VERSION (max 2 lines)
 */
function d27_generate_game_instructions($game_title, $game_tags = null) {
    $instructions = array();
    $game_title_lower = strtolower($game_title);

    // Extract tag names for easier checking
    $tag_names = array();
    if ($game_tags && !is_wp_error($game_tags)) {
        $tag_names = array_map('strtolower', wp_list_pluck($game_tags, 'name'));
    }

    // Generate SHORT instructions based on game type/tags (max 2 lines)
    if (in_array('platformer', $tag_names) || in_array('platform', $tag_names)) {
        $instructions[] = "Jump over obstacles and navigate through challenging platform levels.";
        $instructions[] = "Time your jumps carefully to reach the end of each level.";
    }
    elseif (in_array('rhythm', $tag_names) || in_array('music', $tag_names) || strpos($game_title_lower, 'dash') !== false) {
        $instructions[] = "Move to the beat of the music and synchronize your actions.";
        $instructions[] = "Listen to the soundtrack to guide your timing.";
    }
    elseif (in_array('puzzle', $tag_names) || in_array('brain', $tag_names)) {
        $instructions[] = "Solve puzzles by thinking strategically about each move.";
        $instructions[] = "Plan your approach before taking action.";
    }
    elseif (in_array('arcade', $tag_names) || in_array('classic', $tag_names)) {
        $instructions[] = "Master the classic arcade-style gameplay mechanics.";
        $instructions[] = "Aim for high scores and beat your personal best.";
    }
    elseif (in_array('adventure', $tag_names) || in_array('exploration', $tag_names)) {
        $instructions[] = "Explore the game world and discover hidden secrets.";
        $instructions[] = "Investigate every corner to find collectibles.";
    }
    elseif (in_array('speed', $tag_names) || in_array('fast', $tag_names) || in_array('quick', $tag_names)) {
        $instructions[] = "React quickly with fast reflexes in this high-speed challenge.";
        $instructions[] = "Practice makes perfect - keep trying!";
    }
    elseif (in_array('hard', $tag_names) || in_array('difficult', $tag_names) || in_array('extreme', $tag_names)) {
        $instructions[] = "Prepare for an extremely challenging experience.";
        $instructions[] = "Patience and persistence are key to success.";
    }
    // Geometry Dash specific instructions
    elseif (strpos($game_title_lower, 'geometry') !== false || strpos($game_title_lower, 'dash') !== false) {
        $instructions[] = "Guide your geometric character through obstacle-filled levels.";
        $instructions[] = "Sync your movements with the unique music soundtrack.";
    }
    else {
        // Generic instructions if no specific tags match
        $instructions[] = "Master the game mechanics through practice and determination.";
        $instructions[] = "Stay focused and keep trying - every attempt makes you better!";
    }

    // Format as simple paragraph (no list for shorter appearance)
    $formatted_instructions = '<p>' . implode(' ', $instructions) . '</p>';

    return $formatted_instructions;
}

/**
 * AJAX handler for game rating - Updated with improved error handling
 */
add_action('wp_ajax_submit_game_rating', 'handle_game_rating');
add_action('wp_ajax_nopriv_submit_game_rating', 'handle_game_rating');

function handle_game_rating() {
    // Log request for debugging
    error_log('Rating request received: ' . print_r($_POST, true));
    
    // More permissive nonce checking
    if (!isset($_POST['nonce'])) {
        wp_send_json_error(['message' => 'Security token missing']);
        return;
    }
    
    // Check both possible nonce keys for maximum compatibility
    $valid_nonce = wp_verify_nonce($_POST['nonce'], 'wp_rest') || 
                  wp_verify_nonce($_POST['nonce'], 'd27_nonce') || 
                  wp_verify_nonce($_POST['nonce'], 'd27_ajax_nonce');
                  
    if (!$valid_nonce) {
        wp_send_json_error(['message' => 'Invalid security token. Please refresh the page.']);
        return;
    }

    if (!isset($_POST['game_id']) || !isset($_POST['rating'])) {
        wp_send_json_error(['message' => 'Missing required data']);
        return;
    }

    $game_id = absint($_POST['game_id']);
    $rating = min(5, max(1, intval($_POST['rating'])));

    if ($game_id <= 0) {
        wp_send_json_error(['message' => 'Invalid game ID']);
        return;
    }

    // Remove cookie check to allow rating for testing
    // $voted = $_COOKIE['voted_' . $game_id] ?? false;
    // if ($voted) {
    //     wp_send_json_error(['message' => 'Already voted']);
    //     return;
    // }
    
    // Clear any object cache
    wp_cache_delete($game_id, 'post_meta');
    
    // Get data directly from DB for maximum freshness
    global $wpdb;
    $meta_data = $wpdb->get_results($wpdb->prepare(
        "SELECT meta_key, meta_value FROM $wpdb->postmeta 
         WHERE post_id = %d AND meta_key IN ('_total_votes', '_total_score')
         ORDER BY meta_key",
        $game_id
    ));
    
    // Parse results
    $total_votes = 0;
    $total_score = 0;
    
    foreach ($meta_data as $meta) {
        if ($meta->meta_key === '_total_votes') {
            $total_votes = (int) $meta->meta_value;
        } elseif ($meta->meta_key === '_total_score') {
            $total_score = (int) $meta->meta_value;
        }
    }
    
    // If no results from direct query, fall back to standard method
    if (empty($meta_data)) {
        $total_votes = (int) get_post_meta($game_id, '_total_votes', true);
        $total_score = (int) get_post_meta($game_id, '_total_score', true);
    }

    $total_votes += 1;
    $total_score += $rating;

    $votes_updated = update_post_meta($game_id, '_total_votes', $total_votes);
    $score_updated = update_post_meta($game_id, '_total_score', $total_score);

    if (!$votes_updated || !$score_updated) {
        wp_send_json_error(['message' => 'Failed to update rating data']);
        return;
    }

    // Set cookies to prevent multiple votes - but with a more secure path
    $cookie_path = parse_url(home_url(), PHP_URL_PATH) ?: '/';
    setcookie('voted_' . $game_id, 1, time() + 3600 * 24 * 30, $cookie_path, '', is_ssl(), true);
    
    // Get all fresh stats to return
    $stats = d27_get_fresh_game_stats($game_id);

    wp_send_json_success([
        'total_votes' => $total_votes,
        'new_average' => round($total_score / $total_votes, 1),
        'message' => 'Rating submitted successfully',
        'stats' => $stats
    ]);
}

/**
 * Initialize rating data for games that don't have it
 */
function d27_initialize_game_ratings() {
    $games = get_posts(array(
        'post_type' => 'game',
        'posts_per_page' => -1,
        'meta_query' => array(
            array(
                'key' => '_total_votes',
                'compare' => 'NOT EXISTS'
            )
        )
    ));

    foreach ($games as $game) {
        update_post_meta($game->ID, '_total_votes', 0);
        update_post_meta($game->ID, '_total_score', 0);

        // Remove old rating meta if it exists
        delete_post_meta($game->ID, '_rating');
    }

    return count($games);
}

/**
 * Admin notice to initialize rating data and display debug information
 */
function d27_rating_init_admin_notice() {
    if (!current_user_can('manage_options')) {
        return;
    }
    
    // Handle rating initialization
    if (isset($_GET['d27_init_ratings'])) {
        $count = d27_initialize_game_ratings();
        echo '<div class="notice notice-success is-dismissible"><p>Initialized rating data for ' . $count . ' games.</p></div>';
    }
    
    // Handle debug mode toggle
    if (isset($_GET['d27_toggle_debug'])) {
        $debug_enabled = get_option('d27_debug_mode', false);
        update_option('d27_debug_mode', !$debug_enabled);
        
        echo '<div class="notice notice-info is-dismissible"><p>Debug mode ' . 
            (!$debug_enabled ? 'enabled' : 'disabled') . '. Refresh to see changes.</p></div>';
    }
    
    // Add debug tools button
    $debug_enabled = get_option('d27_debug_mode', false);
    $debug_url = add_query_arg('d27_toggle_debug', '1', admin_url());
    
    echo '<div class="notice notice-info is-dismissible">';
    echo '<p><strong>D27 Gaming Theme:</strong> Debug Tools ';
    echo '<a href="' . esc_url($debug_url) . '" class="button">' . 
        ($debug_enabled ? 'Disable Debug Mode' : 'Enable Debug Mode') . '</a>';
    echo ' <a href="' . esc_url(add_query_arg('d27_test_ajax', '1', admin_url())) . 
        '" class="button">Test AJAX Endpoints</a>';
    echo '</p></div>';

    // Check if there are games without proper rating data
    $games_without_ratings = get_posts(array(
        'post_type' => 'game',
        'posts_per_page' => 1,
        'meta_query' => array(
            array(
                'key' => '_total_votes',
                'compare' => 'NOT EXISTS'
            )
        )
    ));

    if (!empty($games_without_ratings)) {
        $init_url = add_query_arg('d27_init_ratings', '1', admin_url());
        echo '<div class="notice notice-warning"><p><strong>D27 Gaming Theme:</strong> Some games need rating data initialization. <a href="' . esc_url($init_url) . '" class="button">Initialize Rating Data</a></p></div>';
    }
    
    // Test AJAX endpoints if requested
    if (isset($_GET['d27_test_ajax'])) {
        echo '<div class="notice notice-info">';
        echo '<p><strong>Testing AJAX Endpoints:</strong></p>';
        echo '<ul style="margin-left: 20px; list-style: disc;">';
        
        // Test play count endpoint
        $play_count_action = has_action('wp_ajax_increment_play_count');
        $play_count_action_nopriv = has_action('wp_ajax_nopriv_increment_play_count');
        
        echo '<li>Play Count Action: ' . ($play_count_action ? 'Registered ✅' : 'Missing ❌') . '</li>';
        echo '<li>Play Count Action (no priv): ' . ($play_count_action_nopriv ? 'Registered ✅' : 'Missing ❌') . '</li>';
        
        // Test rating endpoint
        $rating_action = has_action('wp_ajax_submit_game_rating');
        $rating_action_nopriv = has_action('wp_ajax_nopriv_submit_game_rating');
        
        echo '<li>Rating Action: ' . ($rating_action ? 'Registered ✅' : 'Missing ❌') . '</li>';
        echo '<li>Rating Action (no priv): ' . ($rating_action_nopriv ? 'Registered ✅' : 'Missing ❌') . '</li>';
        
        // Test like endpoint
        $like_action = has_action('wp_ajax_toggle_game_like');
        $like_action_nopriv = has_action('wp_ajax_nopriv_toggle_game_like');

        echo '<li>Like Action: ' . ($like_action ? 'Registered ✅' : 'Missing ❌') . '</li>';
        echo '<li>Like Action (no priv): ' . ($like_action_nopriv ? 'Registered ✅' : 'Missing ❌') . '</li>';
        
        echo '</ul>';
        echo '</div>';
    }
}
add_action('admin_notices', 'd27_rating_init_admin_notice');

/**
 * Define WP_DEBUG based on our option if not already defined
 */
function d27_maybe_set_debug_mode() {
    if (!defined('WP_DEBUG')) {
        $debug_enabled = get_option('d27_debug_mode', false);
        define('WP_DEBUG', $debug_enabled);
    }
}
add_action('plugins_loaded', 'd27_maybe_set_debug_mode', 1);



// Include additional functionality
require_once get_template_directory() . '/inc/sitemap-generator.php';
require_once get_template_directory() . '/inc/spam-blocker.php';

// Debug function to check navigation data
function d27_debug_navigation() {
    if (current_user_can('manage_options') && isset($_GET['debug_nav_data'])) {
        $nav_items_json = get_theme_mod('d27_nav_items', '');
        $nav_items = json_decode($nav_items_json, true);

        echo '<div style="background: #fff; padding: 20px; margin: 20px; border: 1px solid #ccc;">';
        echo '<h3>Navigation Debug Data</h3>';
        echo '<p><strong>Raw JSON:</strong> ' . esc_html($nav_items_json) . '</p>';
        echo '<p><strong>Decoded Array:</strong> <pre>' . esc_html(print_r($nav_items, true)) . '</pre></p>';
        echo '<p><strong>Is Empty:</strong> ' . (empty($nav_items) ? 'YES' : 'NO') . '</p>';
        echo '<p><strong>Is Array:</strong> ' . (is_array($nav_items) ? 'YES' : 'NO') . '</p>';
        echo '</div>';
    }
}
add_action('wp_footer', 'd27_debug_navigation');

// Reset navigation function
function d27_reset_navigation() {
    if (current_user_can('manage_options') && isset($_GET['reset_nav'])) {
        // Reset to default navigation items
        $default_nav = json_encode(array(
            array('name' => 'Geometry Dash Full Version', 'url' => home_url('/games/geometry-dash-full-version/'), 'target' => '_self'),
            array('name' => 'Geometry Dash Wave', 'url' => home_url('/games/geometry-dash-wave/'), 'target' => '_self'),
            array('name' => 'Geometry Dash Scratch', 'url' => home_url('/games/geometry-dash-scratch/'), 'target' => '_self'),
            array('name' => 'Platform', 'url' => home_url('/game-tag/platform/'), 'target' => '_self'),
            array('name' => 'Rhythm', 'url' => home_url('/game-tag/rhythm/'), 'target' => '_self'),
            array('name' => 'Music', 'url' => home_url('/game-tag/music/'), 'target' => '_self'),
            array('name' => 'Jumping', 'url' => home_url('/game-tag/jumping/'), 'target' => '_self'),
            array('name' => 'Scratch', 'url' => home_url('/game-tag/scratch/'), 'target' => '_self'),
            array('name' => 'Adventure', 'url' => home_url('/game-tag/adventure/'), 'target' => '_self'),
            array('name' => 'Puzzle', 'url' => home_url('/game-tag/puzzle/'), 'target' => '_self'),
            array('name' => 'Strategy', 'url' => home_url('/game-tag/strategy/'), 'target' => '_self'),
            array('name' => 'Action', 'url' => home_url('/game-tag/action/'), 'target' => '_self'),
            array('name' => 'Dash', 'url' => home_url('/game-tag/dash/'), 'target' => '_self'),
        ));

        set_theme_mod('d27_nav_items', $default_nav);

        echo '<div style="background: #4CAF50; color: white; padding: 20px; margin: 20px; border-radius: 5px;">';
        echo '<h3>✅ Navigation Reset Complete!</h3>';
        echo '<p>Navigation has been reset with your new menu items. Refresh the page to see changes.</p>';
        echo '</div>';
    }
}
add_action('wp_footer', 'd27_reset_navigation');

/**
 * Sync WordPress menu to custom navigation (Auto-sync functionality)
 * This creates a close connection between Customizer ▸ Menus and website interface
 */
function d27_sync_wp_menu_to_custom($menu_items) {
    if (!$menu_items || !is_array($menu_items)) {
        return;
    }

    // Convert WordPress menu items to custom navigation format
    $custom_nav_items = array();

    foreach ($menu_items as $item) {
        // Skip parent items with children (for now, keep it simple)
        if ($item->menu_item_parent == 0) {
            $custom_nav_items[] = array(
                'name' => $item->title,
                'url' => $item->url,
                'target' => ($item->target === '_blank') ? '_blank' : '_self'
            );
        }
    }

    // Update custom navigation with WordPress menu data
    if (!empty($custom_nav_items)) {
        $nav_json = json_encode($custom_nav_items);
        set_theme_mod('d27_nav_items', $nav_json);

        // Debug log for administrators
        if (current_user_can('manage_options') && defined('WP_DEBUG') && WP_DEBUG) {
            error_log('D27 Theme: Auto-synced ' . count($custom_nav_items) . ' menu items from WordPress menu to custom navigation');
        }
    }
}

/**
 * Hook to sync menu changes automatically
 * This ensures changes in Customizer ▸ Menus immediately reflect on website
 */
function d27_auto_sync_menu_on_save($menu_id, $menu_data) {
    // Check if this is the primary menu
    $locations = get_nav_menu_locations();
    if (isset($locations['primary']) && $locations['primary'] == $menu_id) {
        // Get updated menu items and sync
        $menu_items = wp_get_nav_menu_items($menu_id);
        if ($menu_items) {
            d27_sync_wp_menu_to_custom($menu_items);
        }
    }
}
add_action('wp_update_nav_menu', 'd27_auto_sync_menu_on_save', 10, 2);

/**
 * Sync when menu locations are updated
 */
function d27_sync_on_menu_location_update($locations) {
    if (isset($locations['primary'])) {
        $menu_items = wp_get_nav_menu_items($locations['primary']);
        if ($menu_items) {
            d27_sync_wp_menu_to_custom($menu_items);
        }
    }
}
add_action('update_option_theme_mods_' . get_option('stylesheet'), 'd27_sync_on_menu_location_update');

/**
 * Admin notice about menu sync functionality
 */
function d27_menu_sync_admin_notice() {
    $screen = get_current_screen();

    // Show notice on menu pages and customizer
    if ($screen && (strpos($screen->id, 'nav-menus') !== false || $screen->id === 'customize')) {
        echo '<div class="notice notice-info is-dismissible">';
        echo '<p><strong>🎮 D27 Gaming Theme:</strong> Your navigation menu is now connected! ';
        echo 'Changes made in <strong>Customizer ▸ Menus</strong> will automatically sync with your website interface. ';
        echo 'The theme will prioritize WordPress menus over custom navigation settings.</p>';
        echo '</div>';
    }
}
add_action('admin_notices', 'd27_menu_sync_admin_notice');

/**
 * Get fresh game stats without caching
 * This function bypasses WordPress meta cache to get real-time values
 */
function d27_get_fresh_game_stats($game_id) {
    global $wpdb;
    
    // Input validation
    $game_id = absint($game_id);
    if (!$game_id) {
        return array(
            'play_count' => 0,
            'total_votes' => 0,
            'total_score' => 0,
            'avg_rating' => 0
        );
    }
    
    // Query database directly to bypass caches
    $stats = array(
        'play_count' => 0,
        'total_votes' => 0,
        'total_score' => 0,
        'avg_rating' => 0
    );
    
    $meta_data = $wpdb->get_results($wpdb->prepare(
        "SELECT meta_key, meta_value FROM $wpdb->postmeta 
         WHERE post_id = %d AND meta_key IN ('_play_count', '_total_votes', '_total_score')
         ORDER BY meta_key",
        $game_id
    ));
    
    // Process results
    foreach ($meta_data as $meta) {
        if ($meta->meta_key === '_play_count') {
            $stats['play_count'] = (int) $meta->meta_value;
        } elseif ($meta->meta_key === '_total_votes') {
            $stats['total_votes'] = (int) $meta->meta_value;
        } elseif ($meta->meta_key === '_total_score') {
            $stats['total_score'] = (int) $meta->meta_value;
        }
    }
    
    // Calculate average rating
    if ($stats['total_votes'] > 0) {
        $stats['avg_rating'] = round($stats['total_score'] / $stats['total_votes'], 1);
    }
    
    return $stats;
}
?>