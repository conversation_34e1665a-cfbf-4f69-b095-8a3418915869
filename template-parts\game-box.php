<?php
/**
 * Template part for displaying the main game box
 */

$iframe_link = get_post_meta(get_the_ID(), '_iframe_link', true);
?>

<div id="game-box" class="main-game-box game-frame">
    <?php if ($iframe_link) : ?>
        <iframe
            class="game-iframe"
            src="<?php echo esc_url($iframe_link); ?>"
            data-game-id="<?php echo esc_attr(get_the_ID()); ?>"
            allowfullscreen
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture">
        </iframe>
    <?php else : ?>
        <div class="no-game-message">
            <h3>🎮 Game Not Available</h3>
            <p>This game is currently not available. Please check back later!</p>
        </div>
    <?php endif; ?>

    <?php
    $game_thumbnail = get_the_post_thumbnail_url(get_the_ID(), 'thumbnail');
    $thumbnail_style = $game_thumbnail ? 'style="--game-thumbnail: url(' . esc_url($game_thumbnail) . ');"' : '';
    $thumbnail_class = $game_thumbnail ? 'has-thumbnail' : '';
    ?>
    <button class="play-button <?php echo $thumbnail_class; ?>" onclick="playGame(<?php echo esc_js(get_the_ID()); ?>)" <?php echo $thumbnail_style; ?>>
        <div class="play-icon">
            <svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="30" cy="30" r="30" fill="rgba(255,255,255,0.9)"/>
                <path d="M23 18L23 42L41 30L23 18Z" fill="#333"/>
            </svg>
        </div>
        <span class="play-text">PLAY GAME</span>
    </button>

    <!-- Game Controls Info -->
    <div class="game-controls-info">
        <button class="controls-toggle" onclick="toggleControls()">
            <span class="controls-icon">🎮</span>
            <span class="controls-text">Controls</span>
        </button>

        <div class="controls-panel">
            <h4>Game Controls:</h4>
            <ul>
                <li><strong>Space/Click:</strong> Jump</li>
                <li><strong>Hold:</strong> Continuous jump</li>
                <li><strong>Arrow Keys:</strong> Navigate menus</li>
                <li><strong>ESC:</strong> Pause game</li>
            </ul>
        </div>
    </div>
</div>

<!-- Game Controls Box -->
<div class="game-controls-box">
    <div class="game-title">
        <h1><?php the_title(); ?></h1>
    </div>

    <div class="game-action-buttons">
        <button class="action-btn fullscreen-btn" onclick="toggleFullscreen()" title="Fullscreen">
            <span>⛶</span>
            <span>Fullscreen</span>
        </button>

        <button class="action-btn share-btn" onclick="shareGame(<?php echo esc_js(get_the_ID()); ?>)" title="Share" data-game-id="<?php echo esc_attr(get_the_ID()); ?>">
            <span>📤</span>
            <span>Share</span>
        </button>
    </div>

    <div class="game-info-bar">
        <div class="game-quick-stats">
                <?php
                $game_id = get_the_ID();
                $total_votes = (int) get_post_meta($game_id, '_total_votes', true);
                $total_score = (int) get_post_meta($game_id, '_total_score', true);
                $avg_rating = $total_votes > 0 ? round($total_score / $total_votes, 1) : 0;
                $play_count = get_post_meta($game_id, '_play_count', true);
                ?>

                <div class="quick-rating">
                    <span class="stars">
                        <?php
                        for ($i = 1; $i <= 5; $i++) {
                            echo $i <= round($avg_rating) ? '⭐' : '☆';
                        }
                        ?>
                    </span>
                    <span class="rating-value"><?php echo esc_html($avg_rating); ?></span>
                </div>

                <div class="quick-plays">
                    <span class="play-icon">▶️</span>
                    <span class="play-count" id="play-count-display"><?php echo esc_html(number_format($play_count ?: 0)); ?></span>
                    <span class="play-label">plays</span>
                </div>
            </div>
        </div>
        
        <div class="game-actions">
            <button class="action-btn favorite-btn" onclick="toggleFavorite(<?php echo esc_js(get_the_ID()); ?>)">
                <span class="heart-icon">🤍</span>
                <span class="action-text">Favorite</span>
            </button>
            
            <button class="action-btn share-btn" onclick="shareGame()">
                <span class="share-icon">📤</span>
                <span class="action-text">Share</span>
            </button>
            
            <button class="action-btn report-btn" onclick="reportGame(<?php echo esc_js(get_the_ID()); ?>)">
                <span class="report-icon">⚠️</span>
                <span class="action-text">Report</span>
            </button>
        </div>
    </div>
</div>

<style>
/* Game Box Styles */
.main-game-box {
    background: rgba(0, 0, 0, 0.9);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0, 212, 255, 0.2);
}

.game-frame {
    position: relative;
    width: 100%;
    max-width: 900px;
    height: 600px;
    margin: 0 auto;
    background: #000;
    border-radius: 10px;
    overflow: hidden;
    border: 2px solid #00d4ff;
}

.game-iframe {
    width: 100%;
    height: 100%;
    border: none;
    display: none;
}

.no-game-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #fff;
    text-align: center;
}

.no-game-message h3 {
    color: #00d4ff;
    margin-bottom: 1rem;
    font-size: 2rem;
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    min-width: 200px;
    min-height: 80px;
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    border: none;
    border-radius: 15px;
    color: #fff;
    cursor: pointer;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);
    font-family: 'Arial', sans-serif;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.play-button:hover {
    transform: translate(-50%, -50%) scale(1.05);
    box-shadow: 0 12px 35px rgba(0, 212, 255, 0.6);
    background: linear-gradient(45deg, #00e6ff, #00b3d9);
}

.play-button:active {
    transform: translate(-50%, -50%) scale(0.98);
}

.play-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease;
}

.play-button:hover .play-icon {
    transform: scale(1.1);
}

.play-text {
    font-size: 16px;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}



.fullscreen-btn {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid #00d4ff;
    color: #00d4ff;
    padding: 0.5rem;
    border-radius: 5px;
    cursor: pointer;
    display: none;
    transition: all 0.3s ease;
}

.fullscreen-btn:hover {
    background: #00d4ff;
    color: #000;
}

.game-controls-info {
    position: absolute;
    bottom: 15px;
    left: 15px;
}

.controls-toggle {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid #00d4ff;
    color: #00d4ff;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.controls-toggle:hover {
    background: #00d4ff;
    color: #000;
}

.controls-panel {
    position: absolute;
    bottom: 100%;
    left: 0;
    background: rgba(0, 0, 0, 0.95);
    border: 1px solid #00d4ff;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 0.5rem;
    min-width: 250px;
    display: none;
    color: #fff;
}

.controls-panel h4 {
    color: #00d4ff;
    margin-bottom: 0.5rem;
}

.controls-panel ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.controls-panel li {
    margin-bottom: 0.3rem;
    font-size: 0.9rem;
}

.game-info-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(0, 212, 255, 0.3);
    flex-wrap: wrap;
    gap: 1rem;
}

.game-title {
    color: #00d4ff;
    font-size: 2rem;
    margin: 0 0 0.5rem 0;
}

.game-quick-stats {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.quick-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stars {
    color: #ffd700;
}

.rating-value {
    color: #00d4ff;
    font-weight: bold;
}

.quick-plays {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    color: #ccc;
}

.game-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.action-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid #00d4ff;
    color: #fff;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.action-btn:hover {
    background: #00d4ff;
    color: #000;
}

.favorite-btn.active {
    background: #ff4757;
    border-color: #ff4757;
}

.favorite-btn.active .heart-icon {
    content: "❤️";
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-game-box {
        padding: 1rem;
    }
    
    .game-frame {
        height: 400px;
    }
    
    .play-button {
        width: 80px;
        height: 80px;
        font-size: 1.5rem;
    }
    
    .game-title {
        font-size: 1.5rem;
    }
    
    .game-info-bar {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }
    
    .game-actions {
        justify-content: center;
    }
    
    .controls-panel {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1000;
    }
}
</style>
