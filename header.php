<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <link rel="profile" href="https://gmpg.org/xfn/11">

    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<header class="main-header">
        <!-- Left group (Logo) -->
        <div class="header-left">
            <?php
            $site_logo = get_theme_mod('d27_site_logo', '🎮');
            $site_title = get_bloginfo('name');
            ?>
            <a href="<?php echo esc_url(home_url('/')); ?>" class="home-link">
                <span class="site-logo"><?php echo esc_html($site_logo); ?></span>
                <span class="site-title-nav"><?php echo esc_html($site_title); ?></span>
            </a>
        </div>

        <!-- Middle group (Menu) - Single row layout -->
        <nav class="header-center main-navigation">
            <?php
            // PRIORITY 1: Check for WordPress standard menu first (Customizer ▸ Menus)
            $menu_items = null;
            $use_wp_menu = false;

            if (has_nav_menu('primary')) {
                $menu_items = wp_get_nav_menu_items(get_nav_menu_locations()['primary']);
                if ($menu_items && !empty($menu_items)) {
                    $use_wp_menu = true;

                    // Auto-sync: Update custom navigation with WordPress menu items
                    d27_sync_wp_menu_to_custom($menu_items);
                }
            }

            // PRIORITY 2: Fallback to custom navigation items from customizer
            if (!$use_wp_menu) {
                $nav_items_json = get_theme_mod('d27_nav_items', '');
                $nav_items = json_decode($nav_items_json, true);

                // Debug: Show current navigation items (remove this after debugging)
                if (current_user_can('manage_options')) {
                    echo '<!-- DEBUG: Using custom nav items -->';
                    echo '<!-- DEBUG: Current nav items JSON: ' . esc_html($nav_items_json) . ' -->';
                    echo '<!-- DEBUG: Decoded nav items: ' . esc_html(print_r($nav_items, true)) . ' -->';
                }
            } else {
                // Debug: Show WordPress menu usage
                if (current_user_can('manage_options')) {
                    echo '<!-- DEBUG: Using WordPress menu with ' . count($menu_items) . ' items -->';
                }
            }

            // Render navigation based on source (logo is now separate)
            if ($use_wp_menu && $menu_items) {
                // Render WordPress menu items
                echo '<ul id="primary-menu" class="nav-menu-dynamic">';

                // Show WordPress menu items (no logo - it's separate now)
                foreach ($menu_items as $index => $item) {
                    $target = ($item->target === '_blank') ? ' target="_blank" rel="noopener"' : '';
                    echo '<li class="nav-item" data-index="' . $index . '"><a href="' . esc_url($item->url) . '"' . $target . '>' . esc_html($item->title) . '</a></li>';
                }
            } elseif (!empty($nav_items) && is_array($nav_items)) {
                // Render custom navigation items
                echo '<ul id="primary-menu" class="nav-menu-dynamic">';

                // Show custom navigation items (no logo - it's separate now)
                foreach ($nav_items as $index => $item) {
                    $target = ($item['target'] === '_blank') ? ' target="_blank" rel="noopener"' : '';
                    echo '<li class="nav-item" data-index="' . $index . '"><a href="' . esc_url($item['url']) . '"' . $target . '>' . esc_html($item['name']) . '</a></li>';
                }
            }

            // Add common navigation elements for both menu types
            if ($use_wp_menu || (!empty($nav_items) && is_array($nav_items))) {
// Only create dropdown toggle button if it doesn't exist yet
                $dropdown_exists = false;
                echo '<script>
                   var existingToggle = document.getElementById("dropdownToggle");
                   if (existingToggle) {
                      document.write("<script>var dropdown_exists = true;<\/script>");
                   }
                </script>';
                
                if (!$dropdown_exists) {
                    echo '<li class="nav-item dropdown-toggle" id="dropdownToggle" style="display: none; position: relative;">';
                    echo '<button type="button" onclick="toggleResponsiveDropdown(event); return false;" style="background: rgba(0, 212, 255, 0.5); color: white; border: 1px solid rgba(0, 212, 255, 0.7); padding: 5px 10px; border-radius: 5px; cursor: pointer; font-weight: bold; position: relative; z-index: 999999;">⋯ More</button>';
                    echo '</li>';
                }
                echo '<li class="nav-item dropdown-toggle" id="dropdownToggle" style="display: none; position: relative;">';
                echo '<button type="button" onclick="toggleInlineDropdown(); return false;" style="background: rgba(0, 212, 255, 0.5); color: white; border: 1px solid rgba(0, 212, 255, 0.7); padding: 5px 10px; border-radius: 5px; cursor: pointer; font-weight: bold; position: relative; z-index: 999999;">⋯ More</button>';
                
                // Hidden menu with fixed positioning to appear above main-content
                echo '<div id="inlineDropdown" style="display: none; position: fixed; top: 45px; right: 20px; width: 200px; background-color: rgba(0, 0, 0, 0.95); border: 2px solid #00d4ff; border-radius: 8px; z-index: 9999999; box-shadow: 0 0 20px #00d4ff; padding: 5px;"><ul style="list-style: none; margin: 0; padding: 0;" id="dropdownContent"></ul></div>';
                
                echo '</li>';
                
                // No need for inline script since we're using responsive-dropdown.js

                echo '</ul>';
            } else {
                // Fallback menu if no menu is assigned
                d27_fallback_menu();
            }
            ?>
        </nav>

        <!-- Right group (Search) -->
        <div class="header-right">
            <div class="search-container">
                <?php get_search_form(); ?>
            </div>
        </div>
</header>

<main class="main-content">

<?php
// Fallback menu if no menu is assigned
function d27_fallback_menu() {
    // Build fallback menu items
    $fallback_items = array();
    $fallback_items[] = array('name' => 'All Games', 'url' => get_post_type_archive_link('game'));

    // Get game tags
    $game_tags = get_terms(array(
        'taxonomy' => 'game_tag',
        'hide_empty' => true,
        'number' => 10, // Increased to show more items
    ));

    if (!empty($game_tags) && !is_wp_error($game_tags)) {
        foreach ($game_tags as $tag) {
            $fallback_items[] = array('name' => $tag->name, 'url' => d27_get_tag_link($tag));
        }
    }

    echo '<ul id="primary-menu" class="nav-menu-dynamic">';

    // Logo is now separate - no need to add it here

    // Show all fallback items (search and logo are now separate)
    foreach ($fallback_items as $index => $item) {
        echo '<li class="nav-item" data-index="' . $index . '"><a href="' . esc_url($item['url']) . '">' . esc_html($item['name']) . '</a></li>';
    }

    // Add direct inline dropdown to avoid any CSS/JS conflicts
    echo '<li class="nav-item dropdown-toggle" id="dropdownToggle" style="display: none; position: relative;">';
    echo '<button type="button" onclick="toggleInlineDropdown(); return false;" style="background: rgba(0, 212, 255, 0.5); color: white; border: 1px solid rgba(0, 212, 255, 0.7); padding: 5px 10px; border-radius: 5px; cursor: pointer; font-weight: bold; position: relative; z-index: 999999;">⋯ More</button>';
    
    // Hidden menu with inline styles
    echo '<div id="inlineDropdown" style="display: none; position: absolute; top: 40px; right: 0; width: 200px; background-color: rgba(0, 0, 0, 0.95); border: 2px solid #00d4ff; border-radius: 8px; z-index: 1000000; box-shadow: 0 0 20px #00d4ff; padding: 5px;"><ul style="list-style: none; margin: 0; padding: 0;" id="dropdownContent"></ul></div>';
    
    echo '</li>';

    echo '</ul>';
}
?>

<!-- Consolidated dropdown functionality is now handled via wp_enqueue_script in functions.php -->