/**
 * D27 Gaming Theme - Interactive Elements Styles
 * Enhanced styling for game interaction elements
 */

/* Enhanced Star Rating Styles */
.rating-star {
    cursor: pointer;
    font-size: 24px;
    color: gold;
    margin: 0 2px;
    transition: transform 0.2s ease, color 0.2s ease, text-shadow 0.2s ease;
    display: inline-block;
    user-select: none;
}

.rating-star.hover,
.rating-star:hover {
    transform: scale(1.3);
    color: #ffcc00;
    text-shadow: 0 0 8px rgba(255, 215, 0, 0.7);
}

.rating-star.filled {
    color: #ffaa00;
    text-shadow: 0 0 5px rgba(255, 170, 0, 0.5);
}

.rating-result {
    margin-left: 10px;
    font-weight: bold;
    color: #00bfff;
    vertical-align: middle;
    text-shadow: 0 0 3px rgba(0, 191, 255, 0.3);
}

/* Enhanced Play Button */
.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    border: none;
    border-radius: 15px;
    min-width: 200px;
    min-height: 80px;
    color: #fff;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    z-index: 10;
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3), 0 0 20px rgba(0, 212, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    font-family: 'Arial', sans-serif;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}

.play-button:hover {
    transform: translate(-50%, -50%) scale(1.05);
    box-shadow: 0 0 30px rgba(0, 212, 255, 0.6), 0 0 15px rgba(0, 212, 255, 0.4);
    background: linear-gradient(45deg, #0099cc, #00d4ff);
}

.play-button:active {
    transform: translate(-50%, -50%) scale(0.98);
    box-shadow: 0 0 15px rgba(0, 212, 255, 0.4);
}

.play-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease;
}

.play-button:hover .play-icon {
    transform: scale(1.1);
}

.play-text {
    font-size: 16px;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.play-button::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 70%);
    opacity: 0;
    transform: scale(0.5);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.play-button:hover::after {
    opacity: 1;
    transform: scale(1);
}

/* Pulsing animation for play button */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 212, 255, 0.7);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(0, 212, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 212, 255, 0);
    }
}

.play-button {
    animation: pulse 2s infinite;
}

.play-button:hover {
    animation: none;
}

/* Allow JavaScript to hide the play button - Enhanced */
.play-button[style*="display: none"],
.play-button.hidden,
.play-button[style*="display:none"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
}

/* Game Stats Section Enhancement */
.game-stats {
    margin: 1.5rem 0;
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
    padding: 1.2rem;
    border-radius: 10px;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(0, 212, 255, 0.2);
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
}

.game-stats .rating,
.game-stats .play-count {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(0, 212, 255, 0.1);
}

.game-stats strong {
    color: #00d4ff;
    font-weight: 600;
    text-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
}

#play-count-display {
    color: #fff;
    font-weight: bold;
    transition: color 0.3s ease;
}

/* Custom notification styling */
.game-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    background: rgba(0, 0, 0, 0.9);
    color: #fff;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border-left: 4px solid #00d4ff;
    max-width: 300px;
    font-size: 0.9rem;
}

.game-notification.show {
    transform: translateX(0);
}

.game-notification.success {
    border-left-color: #00d4ff;
}

.game-notification.error {
    border-left-color: #ff4757;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .game-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .play-button {
        width: 180px;
        height: 70px;
        font-size: 1.3rem;
    }
    
    .rating-star {
        font-size: 20px;
    }
}

/* Debug Elements */
#d27-debug-panel {
    position: fixed;
    bottom: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: #00ff00;
    padding: 10px;
    border-radius: 5px;
    z-index: 9999;
    max-width: 300px;
    max-height: 200px;
    overflow: auto;
    font-size: 12px;
    font-family: monospace;
    border: 1px solid #333;
}

#d27-debug-panel h3 {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #00d4ff;
    text-align: center;
}

.debug-actions {
    display: flex;
    gap: 5px;
    margin-bottom: 8px;
}

.debug-actions button {
    background: #333;
    color: #fff;
    border: 1px solid #555;
    padding: 3px 5px;
    border-radius: 3px;
    font-size: 10px;
    cursor: pointer;
    flex: 1;
}

.debug-actions button:hover {
    background: #444;
}

.debug-log {
    max-height: 120px;
    overflow-y: auto;
    padding: 3px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 3px;
}

.log-entry {
    margin-bottom: 3px;
    padding: 2px 0;
    border-bottom: 1px solid #333;
}

.log-entry:last-child {
    border-bottom: none;
}

.log-error {
    color: #ff5555;
}

.log-success {
    color: #55ff55;
}

/* Prevent browser outline from showing in wrong places */
.rating-star:focus,
.play-button:focus {
    outline: none;
}