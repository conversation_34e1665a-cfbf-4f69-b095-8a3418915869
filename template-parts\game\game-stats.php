<?php
/**
 * Template part for displaying game stats (rating and play count)
 * Used in both front-page.php and single-game.php for consistent display
 * 
 * @package D27 Gaming Theme
 */

// Get game ID - the template should receive it as a parameter
$game_id = isset($args['game_id']) ? $args['game_id'] : 0;

if (!$game_id) {
    return; // Exit if no game ID provided
}

// Force a fresh read from the database to get accurate stats
global $wpdb;

// Get all meta directly from database in one query
$meta_data = $wpdb->get_results($wpdb->prepare(
    "SELECT meta_key, meta_value FROM $wpdb->postmeta 
     WHERE post_id = %d AND meta_key IN ('_play_count', '_total_votes', '_total_score')
     ORDER BY meta_key",
    $game_id
));

// Parse results
$play_count = 0;
$total_votes = 0;
$total_score = 0;

foreach ($meta_data as $meta) {
    if ($meta->meta_key === '_play_count') {
        $play_count = (int) $meta->meta_value;
    } elseif ($meta->meta_key === '_total_votes') {
        $total_votes = (int) $meta->meta_value;
    } elseif ($meta->meta_key === '_total_score') {
        $total_score = (int) $meta->meta_value;
    }
}

// Calculate average rating
$avg_rating = $total_votes > 0 ? round($total_score / $total_votes, 1) : 0;
?>

<div class="game-stats">
    <div class="rating">
        <strong>Rating:</strong>
        <div class="star-rating" data-game-id="<?php echo $game_id; ?>">
            <?php 
            // Add debug info in comments
            echo "<!-- Rating data: votes=$total_votes, score=$total_score, avg=$avg_rating -->\n";
            
            for ($i = 1; $i <= 5; $i++) {
                echo '<span class="rating-star ' . ($i <= round($avg_rating) ? 'filled' : '') . '" data-rating="' . $i . '">⭐</span>';
            }
            ?>
            <span class="rating-result"><?php echo $avg_rating; ?>/5 (<?php echo $total_votes; ?> votes)</span>
            <!-- Added for debugging -->
            <div id="rating-debug" style="display:none; font-size: 10px; color: #666;"></div>
        </div>
    </div>

    <div class="play-count">
        <strong>Plays:</strong> <span id="play-count-display"><?php echo esc_html(number_format($play_count ?: 0)); ?></span>
    </div>
</div>