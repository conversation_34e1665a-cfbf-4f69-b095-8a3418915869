/**
 * Dropdown Duplicate Cleanup
 * 
 * This script finds and removes duplicate dropdown toggle buttons
 * It runs immediately to ensure there's only one dropdown toggle
 */

(function() {
    // Run immediately
    cleanupDuplicateDropdowns();
    
    // Also run when <PERSON><PERSON> is ready to be sure
    document.addEventListener('DOMContentLoaded', cleanupDuplicateDropdowns);
    
    function cleanupDuplicateDropdowns() {
        console.log('Running dropdown duplicate cleanup');
        
        // Find all dropdown toggle elements
        const toggles = document.querySelectorAll('#dropdownToggle, .dropdown-toggle');
        
        if (toggles.length <= 1) {
            console.log('No duplicates found, all good');
            return;
        }
        
        console.log('Found ' + toggles.length + ' dropdown toggles, removing duplicates');
        
        // Keep track of which one to keep (preferably the one with our responsive button)
        let keepIndex = 0;
        
        // Find the index of the toggle with our responsive button
        for (let i = 0; i < toggles.length; i++) {
            const button = toggles[i].querySelector('button[onclick*="toggleResponsiveDropdown"]');
            if (button) {
                keepIndex = i;
                break;
            }
        }
        
        // Remove all toggles except the one we want to keep
        for (let i = 0; i < toggles.length; i++) {
            if (i !== keepIndex && toggles[i] && toggles[i].parentNode) {
                console.log('Removing duplicate dropdown toggle #' + i);
                toggles[i].parentNode.removeChild(toggles[i]);
            }
        }
        
        // Make sure the remaining toggle has the right ID
        if (toggles[keepIndex]) {
            toggles[keepIndex].id = 'dropdownToggle';
            console.log('Kept dropdown toggle #' + keepIndex);
        }
    }
})();