<?php
/**
 * The template for displaying comments
 */

if (post_password_required()) {
    return;
}
?>

<div id="comments" class="comments-area">
    <?php if (have_comments()) : ?>
        <h3 class="comments-title">
            <?php
            $comment_count = get_comments_number();
            if ($comment_count == 1) {
                echo '💬 1 Comment';
            } else {
                echo '💬 ' . $comment_count . ' Comments';
            }
            ?>
        </h3>

        <ol class="comment-list">
            <?php
            wp_list_comments(array(
                'style' => 'ol',
                'short_ping' => true,
                'callback' => 'd27_custom_comment_callback',
            ));
            ?>
        </ol>

        <?php if (get_comment_pages_count() > 1 && get_option('page_comments')) : ?>
            <nav class="comment-navigation">
                <div class="nav-previous"><?php previous_comments_link('← Older Comments'); ?></div>
                <div class="nav-next"><?php next_comments_link('Newer Comments →'); ?></div>
            </nav>
        <?php endif; ?>

    <?php endif; ?>

    <?php if (!comments_open() && get_comments_number() && post_type_supports(get_post_type(), 'comments')) : ?>
        <p class="no-comments">Comments are closed.</p>
    <?php endif; ?>

    <?php
    $comment_form_args = array(
        'title_reply' => '✍️ Leave a Comment',
        'title_reply_to' => '↩️ Reply to %s',
        'cancel_reply_link' => '✖️ Cancel Reply',
        'label_submit' => '🚀 Post Comment',
        'comment_field' => '<div class="comment-form-field">
            <label for="comment">Your Comment *</label>
            <textarea id="comment" name="comment" cols="45" rows="6" maxlength="65525" required placeholder="Share your thoughts about this game..."></textarea>
        </div>',
        'fields' => array(
            'author' => '<div class="comment-form-field">
                <label for="author">Name *</label>
                <input id="author" name="author" type="text" value="' . esc_attr($commenter['comment_author']) . '" size="30" maxlength="245" required placeholder="Your name" />
            </div>',
            'email' => '<div class="comment-form-field">
                <label for="email">Email *</label>
                <input id="email" name="email" type="email" value="' . esc_attr($commenter['comment_author_email']) . '" size="30" maxlength="100" required placeholder="<EMAIL>" />
            </div>',
            'url' => '<div class="comment-form-field">
                <label for="url">Website</label>
                <input id="url" name="url" type="url" value="' . esc_attr($commenter['comment_author_url']) . '" size="30" maxlength="200" placeholder="https://yourwebsite.com (optional)" />
            </div>',
        ),
        'class_form' => 'comment-form',
        'class_submit' => 'submit-btn',
        'comment_notes_before' => '<p class="comment-notes">Your email address will not be published. Required fields are marked *</p>',
        'comment_notes_after' => '',
    );
    
    comment_form($comment_form_args);
    ?>
</div>

<?php
// Custom comment callback function
if (!function_exists('d27_custom_comment_callback')) {
    function d27_custom_comment_callback($comment, $args, $depth) {
        $GLOBALS['comment'] = $comment;
        ?>
        <li <?php comment_class(); ?> id="comment-<?php comment_ID(); ?>">
            <div class="comment">
                <div class="comment-author">
                    <?php echo get_avatar($comment, 50, '', '', array('class' => 'comment-author-avatar')); ?>
                    <div class="comment-author-info">
                        <span class="comment-author-name"><?php comment_author(); ?></span>
                        <div class="comment-meta">
                            <time datetime="<?php comment_time('c'); ?>">
                                <?php comment_time('F j, Y \a\t g:i a'); ?>
                            </time>
                        </div>
                    </div>
                </div>
                
                <div class="comment-content">
                    <?php comment_text(); ?>
                </div>
                
                <div class="comment-actions">
                    <?php
                    comment_reply_link(array_merge($args, array(
                        'depth' => $depth,
                        'max_depth' => $args['max_depth'],
                        'reply_text' => '↩️ Reply'
                    )));
                    ?>
                </div>
            </div>
        <?php
    }
}
?>

<style>
.comment-notes {
    color: #ccc;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.comments-title {
    color: #00d4ff;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.comment-list {
    list-style: none;
    padding: 0;
    margin: 0 0 2rem 0;
}

.comment-author-info {
    display: flex;
    flex-direction: column;
}

.comment-navigation {
    display: flex;
    justify-content: space-between;
    margin: 2rem 0;
}

.comment-navigation a {
    color: #00d4ff;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 5px;
    transition: all 0.3s ease;
}

.comment-navigation a:hover {
    background: rgba(0, 212, 255, 0.1);
    border-color: #00d4ff;
}

.no-comments {
    color: #ccc;
    font-style: italic;
    text-align: center;
    padding: 2rem;
}

/* Responsive */
@media (max-width: 768px) {
    .comment-author {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .comment-navigation {
        flex-direction: column;
        gap: 1rem;
    }
}
</style>
