<?php
/**
 * The front page template file
 */

get_header();

// Get featured game from customizer
$featured_game_id = get_theme_mod('d27_featured_game', '');
$featured_game = null;

if ($featured_game_id) {
    $featured_game = get_post($featured_game_id);
}

// If no featured game set, get the latest game
if (!$featured_game) {
    $latest_games = d27_get_latest_games(1);
    if ($latest_games->have_posts()) {
        $latest_games->the_post();
        $featured_game = get_post();
        wp_reset_postdata();
    }
}
?>

<?php if ($featured_game) : ?>
    <!-- Main Game Frame -->
    <div id="game-box" class="game-frame">
        <?php
        $iframe_link = get_post_meta($featured_game->ID, '_iframe_link', true);
        if ($iframe_link) :
        ?>
            <iframe
                class="game-iframe"
                src="<?php echo esc_url($iframe_link); ?>"
                data-game-id="<?php echo esc_attr($featured_game->ID); ?>"
                allowfullscreen>
            </iframe>
        <?php endif; ?>

        <?php
        $game_thumbnail = get_the_post_thumbnail_url($featured_game->ID, 'thumbnail');
        $thumbnail_style = $game_thumbnail ? 'style="--game-thumbnail: url(' . esc_url($game_thumbnail) . ');"' : '';
        $thumbnail_class = $game_thumbnail ? 'has-thumbnail' : '';
        ?>
        <button class="play-button <?php echo $thumbnail_class; ?>" onclick="playGame(<?php echo esc_js($featured_game->ID); ?>)" <?php echo $thumbnail_style; ?>>
            <div class="play-icon">
                <svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="30" cy="30" r="30" fill="rgba(255,255,255,0.9)"/>
                    <path d="M23 18L23 42L41 30L23 18Z" fill="#333"/>
                </svg>
            </div>
            <span class="play-text">PLAY GAME</span>
        </button>
    </div>

    <!-- Game Controls Box -->
    <div class="game-controls-box">
        <div class="game-title">
            <h1><?php echo esc_html($featured_game->post_title); ?></h1>
        </div>

        <div class="game-action-buttons">
            <button class="action-btn fullscreen-btn" onclick="toggleFullscreen()" title="Fullscreen">
                <span>⛶</span>
                <span>Fullscreen</span>
            </button>

            <button class="action-btn share-btn" onclick="shareGame(<?php echo esc_js($featured_game->ID); ?>)" title="Share" data-game-id="<?php echo esc_attr($featured_game->ID); ?>">
                <span>📤</span>
                <span>Share</span>
            </button>
        </div>
    </div>
<?php endif; ?>

<!-- Two Column Layout: Game Description + Popular Games -->
<div class="content-grid">
    <div class="game-description">
        <?php if ($featured_game) : ?>
            <h2><?php echo esc_html($featured_game->post_title); ?></h2>

            <?php 
            // Use unified template part for game stats
            get_template_part('template-parts/game/game-stats', null, array('game_id' => $featured_game->ID)); 
            ?>

            <div class="game-content">
                <?php echo wp_kses_post($featured_game->post_content); ?>
            </div>

            <?php
            $instructions = get_post_meta($featured_game->ID, '_game_instructions', true);
            if ($instructions) :
            ?>
                <div class="game-instructions">
                    <h3>How to Play:</h3>
                    <p><?php echo esc_html($instructions); ?></p>
                </div>
            <?php endif; ?>

            <div class="game-tags">
                <?php
                $game_tags = get_the_terms($featured_game->ID, 'game_tag');
                if ($game_tags && !is_wp_error($game_tags)) {
                    foreach ($game_tags as $tag) {
                        echo '<a href="' . esc_url(d27_get_tag_link($tag)) . '" class="tag">' . esc_html($tag->name) . '</a>';
                    }
                }
                ?>
            </div>
        <?php else : ?>
            <h2>Welcome to <?php bloginfo('name'); ?></h2>
            <p>Discover amazing Geometry Dash games and challenge yourself with exciting levels!</p>
        <?php endif; ?>
    </div>
    
    <div class="popular-games">
        <h3>🔥 Most Played Games</h3>
        <?php
        $popular_games = d27_get_popular_games(6);
        if ($popular_games->have_posts()) :
            while ($popular_games->have_posts()) : $popular_games->the_post();
        ?>
            <a href="<?php the_permalink(); ?>" class="popular-game-item">
                <?php if (has_post_thumbnail()) : ?>
                    <?php the_post_thumbnail('thumbnail', array('class' => 'popular-game-thumb')); ?>
                <?php else : ?>
                    <div class="popular-game-thumb no-thumb">🎮</div>
                <?php endif; ?>
                
                <div class="popular-game-info">
                    <h4><?php the_title(); ?></h4>
                    <?php
                    $play_count = get_post_meta(get_the_ID(), '_play_count', true);
                    if ($play_count) :
                    ?>
                        <span class="plays"><?php echo esc_html(number_format($play_count)); ?> plays</span>
                    <?php endif; ?>
                </div>
            </a>
        <?php
            endwhile;
            wp_reset_postdata();
        else :
        ?>
            <p>No games available yet. Check back soon!</p>
        <?php endif; ?>
    </div>
</div>

<!-- Comments Section (if enabled) -->
<?php if (get_theme_mod('d27_enable_comments', true) && $featured_game) : ?>
    <div class="comments-section">
        <h3>💬 Comments</h3>
        <?php
        // Set up global post data for comments
        $GLOBALS['post'] = $featured_game;
        setup_postdata($featured_game);
        
        if (comments_open() || get_comments_number()) {
            comments_template();
        }
        
        wp_reset_postdata();
        ?>
    </div>
<?php endif; ?>

<!-- Latest Games Grid -->
<div class="latest-games-section">
    <h2>🎮 Latest Games</h2>
    
    <div class="games-grid">
        <?php
        $latest_games = d27_get_latest_games(12);
        if ($latest_games->have_posts()) :
            while ($latest_games->have_posts()) : $latest_games->the_post();
        ?>
            <article class="game-card">
                <a href="<?php the_permalink(); ?>">
                    <div class="game-thumbnail">
                        <?php if (has_post_thumbnail()) : ?>
                            <?php the_post_thumbnail('game-thumbnail'); ?>
                        <?php else : ?>
                            <div class="no-image">
                                <span class="game-icon">🎮</span>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="game-card-content">
                        <h3><?php the_title(); ?></h3>
                    </div>
                </a>
            </article>
        <?php
            endwhile;
            wp_reset_postdata();
        endif;
        ?>
    </div>
    
    <div class="view-all-games">
        <a href="<?php echo esc_url(get_post_type_archive_link('game')); ?>" class="btn btn-primary">
            View All Games →
        </a>
    </div>
</div>

<?php 
// Include the shared interactive JavaScript template
get_template_part('template-parts/game/game-interactive.js');

get_footer(); 
?>

<style>
/* Front page specific styles */
.game-title {
    margin-top: 1rem;
}

.game-title h1 {
    color: #00d4ff;
    font-size: 2rem;
    margin: 0;
}

.game-stats .rating,
.game-stats .play-count {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stars {
    color: #ffd700;
}

.rating-number {
    color: #00d4ff;
    font-weight: bold;
}

.popular-game-thumb.no-thumb {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #1a1a2e, #16213e);
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.popular-game-info h4 {
    margin: 0;
    font-size: 0.9rem;
    color: #fff;
}

.popular-game-info .plays {
    font-size: 0.8rem;
    color: #ccc;
}

.latest-games-section {
    margin-top: 3rem;
}

.latest-games-section h2 {
    color: #00d4ff;
    text-align: center;
    margin-bottom: 2rem;
    font-size: 2rem;
}

.view-all-games {
    text-align: center;
    margin-top: 2rem;
}

.btn {
    display: inline-block;
    padding: 1rem 2rem;
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    color: #fff;
    text-decoration: none;
    border-radius: 25px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
}

.game-thumbnail.no-image {
    height: 150px;
    background: linear-gradient(45deg, #1a1a2e, #16213e);
    display: flex;
    align-items: center;
    justify-content: center;
}

.game-icon {
    font-size: 3rem;
    opacity: 0.7;
}
</style>