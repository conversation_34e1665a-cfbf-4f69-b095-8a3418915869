/**
 * D27 Gaming Theme - Optimized Game Handler
 * Consolidated and optimized game interaction functionality
 * Combines: play-handler.js, fallback-handlers.js, layout-fix.js
 */

(function() {
    'use strict';
    
    // Configuration
    const CONFIG = {
        AJAX_TIMEOUT: 10000,
        RETRY_ATTEMPTS: 3,
        DEBOUNCE_DELAY: 300,
        ANIMATION_DURATION: 200
    };
    
    // State management
    const state = {
        currentGameId: null,
        isFullscreen: false,
        ratingSubmitted: false,
        playTriggered: false,
        isInitialized: false
    };
    
    // DOM elements cache
    const elements = {
        gameIframe: null,
        playButton: null,
        fullscreenBtn: null,
        starContainers: null,
        favoriteButtons: null
    };
    
    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', init);
    window.addEventListener('load', init);
    
    // Also run immediately if page is already loaded
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        setTimeout(init, 100);
    }
    
    function init() {
        if (state.isInitialized) return;
        
        console.log('🎮 Initializing optimized game handler');
        
        // Cache DOM elements
        cacheElements();
        
        // Initialize all handlers
        initializeGameHandlers();
        initializeUIHandlers();
        initializeStarRating();
        initializeFavorites();
        
        // Fix any layout issues
        fixLayoutIssues();
        
        state.isInitialized = true;
        console.log('✅ Game handler initialized successfully');
    }
    
    function cacheElements() {
        elements.gameIframe = document.querySelector('.game-iframe');
        elements.playButton = document.querySelector('.play-button');
        elements.fullscreenBtn = document.querySelector('.fullscreen-btn');
        elements.starContainers = document.querySelectorAll('.star-rating');
        elements.favoriteButtons = document.querySelectorAll('.favorite-btn');
    }
    
    function initializeGameHandlers() {
        // Set up iframe load event
        if (elements.gameIframe) {
            elements.gameIframe.addEventListener('load', showFullscreenButton);
            
            // Extract game ID from iframe data attribute
            state.currentGameId = elements.gameIframe.getAttribute('data-game-id');
        }
        
        // Set up fullscreen change events
        const fullscreenEvents = [
            'fullscreenchange',
            'webkitfullscreenchange', 
            'mozfullscreenchange',
            'MSFullscreenChange'
        ];
        
        fullscreenEvents.forEach(event => {
            document.addEventListener(event, handleFullscreenChange);
        });
        
        // Set up play button
        if (elements.playButton) {
            elements.playButton.addEventListener('click', handlePlayClick);
        }
        
        // Set up fullscreen button
        if (elements.fullscreenBtn) {
            elements.fullscreenBtn.addEventListener('click', toggleFullscreen);
        }
    }
    
    function initializeUIHandlers() {
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // ESC to exit fullscreen
            if (e.key === 'Escape' && state.isFullscreen) {
                exitFullscreen();
            }
            
            // F key for fullscreen
            if ((e.key === 'f' || e.key === 'F') && elements.gameIframe && 
                elements.gameIframe.style.display !== 'none') {
                e.preventDefault();
                toggleFullscreen();
            }
        });
        
        // Controls toggle
        const controlsToggle = document.querySelector('.controls-toggle');
        const controlsPanel = document.querySelector('.controls-panel');
        
        if (controlsToggle && controlsPanel) {
            controlsToggle.addEventListener('click', function() {
                const isVisible = controlsPanel.style.display === 'block';
                controlsPanel.style.display = isVisible ? 'none' : 'block';
            });
            
            // Hide controls panel when clicking outside
            document.addEventListener('click', function(e) {
                if (!controlsToggle.contains(e.target) && !controlsPanel.contains(e.target)) {
                    controlsPanel.style.display = 'none';
                }
            });
        }
    }
    
    function initializeStarRating() {
        if (!elements.starContainers) return;
        
        elements.starContainers.forEach(container => {
            const gameId = container.getAttribute('data-game-id');
            const stars = container.querySelectorAll('.rating-star');
            const resultDisplay = container.querySelector('.rating-result');
            
            if (!gameId || !stars.length) return;
            
            // Add hover effects and click handlers
            stars.forEach((star, index) => {
                // Hover effects
                star.addEventListener('mouseenter', function() {
                    highlightStars(stars, index);
                });
                
                // Click handler
                star.addEventListener('click', function() {
                    if (state.ratingSubmitted) {
                        showNotification('You have already submitted a rating', 'info');
                        return;
                    }
                    
                    const rating = index + 1;
                    submitRating(gameId, rating, container);
                });
            });
            
            // Reset on mouse leave
            container.addEventListener('mouseleave', function() {
                resetStarDisplay(stars, resultDisplay);
            });
        });
    }
    
    function initializeFavorites() {
        if (!elements.favoriteButtons) return;
        
        const favorites = getFavorites();
        
        elements.favoriteButtons.forEach(btn => {
            const gameId = btn.getAttribute('onclick')?.match(/\d+/)?.[0];
            if (gameId && favorites.includes(gameId)) {
                btn.classList.add('active');
                const heartIcon = btn.querySelector('.heart-icon');
                if (heartIcon) heartIcon.textContent = '❤️';
            }
        });
    }
    
    function fixLayoutIssues() {
        // Fix duplicate content if present
        const gameContent = document.querySelector('.game-content');
        if (gameContent) {
            const headings = gameContent.querySelectorAll('h2');
            
            if (headings.length > 1) {
                const firstHeadingText = headings[0].textContent.trim();
                for (let i = 1; i < headings.length; i++) {
                    if (headings[i].textContent.trim() === firstHeadingText) {
                        let nextElement = headings[i].nextElementSibling;
                        headings[i].remove();
                        if (nextElement && nextElement.tagName === 'P') {
                            nextElement.remove();
                        }
                    }
                }
            }
        }
        
        // Prevent content jump
        const gameFrame = document.querySelector('.game-frame');
        if (gameFrame) {
            gameFrame.style.minHeight = '400px';
        }
    }
    
    function handlePlayClick(event) {
        event.preventDefault();
        event.stopPropagation();
        
        if (state.playTriggered) return;
        
        const gameId = this.getAttribute('onclick')?.match(/\d+/)?.[0] || state.currentGameId;
        if (gameId) {
            playGame(parseInt(gameId));
        }
    }
    
    function playGame(gameId) {
        if (state.playTriggered) return;
        
        console.log('🎮 Playing game:', gameId);
        state.playTriggered = true;
        state.currentGameId = gameId;
        
        // Show iframe, hide play button
        if (elements.gameIframe) {
            elements.gameIframe.style.display = 'block';
            elements.gameIframe.focus();
        }
        
        if (elements.playButton) {
            elements.playButton.style.display = 'none';
        }
        
        // Show fullscreen button
        showFullscreenButton();
        
        // Increment play count
        incrementPlayCount(gameId);
    }
    
    function toggleFullscreen() {
        if (state.isFullscreen) {
            exitFullscreen();
        } else {
            enterFullscreen();
        }
    }
    
    function enterFullscreen() {
        if (!elements.gameIframe) return;
        
        const requestFullscreen = elements.gameIframe.requestFullscreen ||
                                 elements.gameIframe.webkitRequestFullscreen ||
                                 elements.gameIframe.mozRequestFullScreen ||
                                 elements.gameIframe.msRequestFullscreen;
        
        if (requestFullscreen) {
            requestFullscreen.call(elements.gameIframe);
        }
    }
    
    function exitFullscreen() {
        const exitFullscreen = document.exitFullscreen ||
                              document.webkitExitFullscreen ||
                              document.mozCancelFullScreen ||
                              document.msExitFullscreen;
        
        if (exitFullscreen) {
            exitFullscreen.call(document);
        }
    }
    
    function handleFullscreenChange() {
        state.isFullscreen = !!(document.fullscreenElement ||
                               document.webkitFullscreenElement ||
                               document.mozFullScreenElement ||
                               document.msFullscreenElement);
        
        if (elements.fullscreenBtn) {
            elements.fullscreenBtn.textContent = state.isFullscreen ? 'Exit Fullscreen' : 'Fullscreen';
        }
    }
    
    function showFullscreenButton() {
        if (elements.fullscreenBtn) {
            elements.fullscreenBtn.style.display = 'block';
        }
    }
    
    function highlightStars(stars, index) {
        stars.forEach((star, i) => {
            if (i <= index) {
                star.textContent = '★';
                star.classList.add('hover');
            } else {
                star.textContent = '☆';
                star.classList.remove('hover');
            }
        });
    }
    
    function resetStarDisplay(stars, resultDisplay) {
        // Reset to current rating or empty
        const currentRating = resultDisplay ? 
            parseInt(resultDisplay.textContent.match(/\d+/)?.[0]) || 0 : 0;
        
        stars.forEach((star, i) => {
            if (i < currentRating) {
                star.textContent = '★';
                star.classList.add('filled');
            } else {
                star.textContent = '☆';
                star.classList.remove('filled', 'hover');
            }
        });
    }
    
    function submitRating(gameId, rating, container) {
        if (!window.d27_ajax) {
            console.warn('AJAX configuration not available');
            return;
        }
        
        const formData = new FormData();
        formData.append('action', 'submit_game_rating');
        formData.append('game_id', gameId);
        formData.append('rating', rating);
        formData.append('nonce', window.d27_ajax.nonce);
        
        fetch(window.d27_ajax.ajax_url, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                state.ratingSubmitted = true;
                updateRatingDisplay(container, data.data);
                showNotification('Rating submitted successfully!', 'success');
            } else {
                showNotification(data.data || 'Failed to submit rating', 'error');
            }
        })
        .catch(error => {
            console.error('Rating submission error:', error);
            showNotification('Failed to submit rating', 'error');
        });
    }
    
    function incrementPlayCount(gameId) {
        if (!window.d27_ajax) return;
        
        const formData = new FormData();
        formData.append('action', 'increment_play_count');
        formData.append('game_id', gameId);
        formData.append('nonce', window.d27_ajax.nonce);
        
        fetch(window.d27_ajax.ajax_url, {
            method: 'POST',
            body: formData
        })
        .catch(error => {
            console.error('Play count increment error:', error);
        });
    }
    
    function updateRatingDisplay(container, data) {
        const resultDisplay = container.querySelector('.rating-result');
        if (resultDisplay && data.average_rating && data.total_votes) {
            resultDisplay.textContent = `${data.average_rating}/5 (${data.total_votes} votes)`;
        }
    }
    
    function getFavorites() {
        try {
            return JSON.parse(localStorage.getItem('d27_favorites') || '[]');
        } catch (e) {
            return [];
        }
    }
    
    function showNotification(message, type = 'info') {
        // Simple notification system
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
            color: white;
            border-radius: 4px;
            z-index: 10000;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        // Fade in
        setTimeout(() => notification.style.opacity = '1', 10);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
    
    // Global API
    window.playGame = playGame;
    window.toggleGameFullscreen = toggleFullscreen;
    
    // Expose optimized game handler API
    window.D27GameHandler = {
        play: playGame,
        toggleFullscreen: toggleFullscreen,
        submitRating: submitRating,
        isPlaying: () => state.playTriggered,
        isFullscreen: () => state.isFullscreen,
        getCurrentGameId: () => state.currentGameId
    };

})();
