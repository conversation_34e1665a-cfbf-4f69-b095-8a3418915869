<?php
/**
 * Comprehensive Spam URL Blocker for Theme 27
 * Blocks spam URLs and returns 404 errors
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize spam blocking system
 */
function d27_init_spam_blocker() {
    // Hook early to catch spam URLs before WordPress processes them
    add_action('init', 'd27_block_spam_urls', 1);
    add_action('template_redirect', 'd27_block_spam_template_redirect', 1);
    add_filter('wp_robots', 'd27_add_noindex_to_spam_pages');
    
    // Block spam in admin
    add_action('admin_init', 'd27_block_admin_spam');
    
    // Block spam user agents
    add_action('init', 'd27_block_spam_user_agents', 1);
    
    // Log spam attempts (optional)
    add_action('d27_spam_blocked', 'd27_log_spam_attempt');
}
add_action('plugins_loaded', 'd27_init_spam_blocker', 1);

/**
 * Main spam URL blocking function
 */
function d27_block_spam_urls() {
    $request_uri = $_SERVER['REQUEST_URI'] ?? '';
    $query_string = $_SERVER['QUERY_STRING'] ?? '';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    // Define spam URL patterns
    $spam_patterns = d27_get_spam_url_patterns();
    
    // Check for spam patterns in URL
    foreach ($spam_patterns as $pattern) {
        if (preg_match($pattern, $request_uri) || preg_match($pattern, $query_string)) {
            d27_block_request('Spam URL pattern detected: ' . $pattern);
            return;
        }
    }
    
    // Check for spam file extensions
    $spam_extensions = d27_get_spam_file_extensions();
    foreach ($spam_extensions as $ext) {
        if (preg_match('/\.' . preg_quote($ext, '/') . '(\?|$)/i', $request_uri)) {
            d27_block_request('Spam file extension: ' . $ext);
            return;
        }
    }
    
    // Check for spam query parameters
    $spam_params = d27_get_spam_query_params();
    foreach ($spam_params as $param) {
        if (strpos($query_string, $param) !== false) {
            d27_block_request('Spam query parameter: ' . $param);
            return;
        }
    }
    
    // Check for common exploit attempts
    $exploit_patterns = d27_get_exploit_patterns();
    foreach ($exploit_patterns as $pattern) {
        if (preg_match($pattern, $request_uri) || preg_match($pattern, $query_string)) {
            d27_block_request('Exploit attempt detected: ' . $pattern);
            return;
        }
    }
}

/**
 * Block spam URLs on template redirect
 */
function d27_block_spam_template_redirect() {
    // Block access to WordPress core files
    $blocked_files = array(
        'wp-config.php',
        'wp-config-sample.php',
        'readme.html',
        'readme.txt',
        'license.txt',
        'xmlrpc.php',
        'wp-trackback.php',
        'wp-comments-post.php',
        'install.php',
        'upgrade.php'
    );
    
    $request_uri = $_SERVER['REQUEST_URI'] ?? '';
    $basename = basename(parse_url($request_uri, PHP_URL_PATH));
    
    if (in_array($basename, $blocked_files)) {
        d27_block_request('Blocked WordPress core file access: ' . $basename);
        return;
    }
    
    // Block direct access to theme/plugin files
    if (preg_match('/\/(wp-content|wp-includes|wp-admin)\/.*\.php$/i', $request_uri)) {
        // Allow specific exceptions
        $allowed_exceptions = array(
            '/wp-admin/admin-ajax.php',
            '/wp-admin/admin-post.php'
        );
        
        $is_allowed = false;
        foreach ($allowed_exceptions as $exception) {
            if (strpos($request_uri, $exception) !== false) {
                $is_allowed = true;
                break;
            }
        }
        
        if (!$is_allowed) {
            d27_block_request('Direct PHP file access blocked: ' . $request_uri);
            return;
        }
    }
}

/**
 * Block spam user agents
 */
function d27_block_spam_user_agents() {
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    if (empty($user_agent)) {
        d27_block_request('Empty user agent');
        return;
    }
    
    $spam_user_agents = d27_get_spam_user_agents();
    
    foreach ($spam_user_agents as $spam_agent) {
        if (stripos($user_agent, $spam_agent) !== false) {
            d27_block_request('Spam user agent: ' . $spam_agent);
            return;
        }
    }
}

/**
 * Block spam in admin area
 */
function d27_block_admin_spam() {
    if (!is_admin()) {
        return;
    }
    
    $request_uri = $_SERVER['REQUEST_URI'] ?? '';
    
    // Block common admin spam patterns
    $admin_spam_patterns = array(
        '/wp-admin\/.*\.php\?.*[<>"\']/',
        '/wp-admin\/.*\.php\?.*script/',
        '/wp-admin\/.*\.php\?.*javascript/',
        '/wp-admin\/.*\.php\?.*eval\(/',
        '/wp-admin\/.*\.php\?.*base64_decode/',
        '/wp-admin\/.*\.php\?.*exec\(/',
        '/wp-admin\/.*\.php\?.*system\(/',
        '/wp-admin\/.*\.php\?.*shell_exec/',
        '/wp-admin\/.*\.php\?.*passthru/',
        '/wp-admin\/.*\.php\?.*file_get_contents/',
        '/wp-admin\/.*\.php\?.*curl_exec/',
        '/wp-admin\/.*\.php\?.*wget/',
        '/wp-admin\/.*\.php\?.*lynx/',
    );
    
    foreach ($admin_spam_patterns as $pattern) {
        if (preg_match($pattern, $request_uri)) {
            d27_block_request('Admin spam pattern detected: ' . $pattern);
            return;
        }
    }
}

/**
 * Get spam URL patterns
 */
function d27_get_spam_url_patterns() {
    return array(
        // WordPress core vulnerabilities
        '/\/wp-config\.php/i',
        '/\/wp-admin\/install\.php/i',
        '/\/wp-admin\/upgrade\.php/i',
        '/\/wp-admin\/setup-config\.php/i',
        '/\/xmlrpc\.php/i',
        '/\/wp-trackback\.php/i',
        
        // Common exploit patterns
        '/\.\.\//i',
        '/\.\.%2F/i',
        '/\.\.%5C/i',
        '/%2e%2e%2f/i',
        '/%2e%2e%5c/i',
        '/\x00/i',
        '/%00/i',
        
        // SQL injection patterns
        '/union.*select/i',
        '/select.*from/i',
        '/insert.*into/i',
        '/update.*set/i',
        '/delete.*from/i',
        '/drop.*table/i',
        '/create.*table/i',
        '/alter.*table/i',
        '/exec.*\(/i',
        '/execute.*\(/i',
        
        // XSS patterns
        '/<script/i',
        '/<\/script>/i',
        '/javascript:/i',
        '/vbscript:/i',
        '/onload=/i',
        '/onerror=/i',
        '/onclick=/i',
        '/onmouseover=/i',
        '/onfocus=/i',
        '/onblur=/i',
        '/onchange=/i',
        '/onsubmit=/i',
        
        // PHP code injection
        '/\<\?php/i',
        '/\<\?=/i',
        '/eval\(/i',
        '/base64_decode/i',
        '/file_get_contents/i',
        '/fopen/i',
        '/fwrite/i',
        '/include/i',
        '/require/i',
        '/system\(/i',
        '/exec\(/i',
        '/shell_exec/i',
        '/passthru/i',
        '/curl_exec/i',
        '/wget/i',
        '/lynx/i',
        
        // Common spam patterns
        '/pharmacy/i',
        '/viagra/i',
        '/cialis/i',
        '/casino/i',
        '/poker/i',
        '/loan/i',
        '/mortgage/i',
        '/insurance/i',
        '/dating/i',
        '/escort/i',
        '/porn/i',
        '/sex/i',
        '/adult/i',
        '/xxx/i',
        '/replica/i',
        '/fake/i',
        '/cheap/i',
        '/discount/i',
        '/sale/i',
        '/buy.*online/i',
        '/weight.*loss/i',
        '/diet.*pill/i',
        '/make.*money/i',
        '/work.*home/i',
        '/earn.*money/i',
        '/get.*rich/i',
        '/free.*money/i',
        '/lottery/i',
        '/winner/i',
        '/prize/i',
        '/congratulations/i',
        
        // Malicious file patterns
        '/\.php\./i',
        '/\.asp\./i',
        '/\.jsp\./i',
        '/\.cgi\./i',
        '/\.pl\./i',
        '/\.py\./i',
        '/\.rb\./i',
        '/\.sh\./i',
        '/\.bat\./i',
        '/\.cmd\./i',
        '/\.exe\./i',
        '/\.dll\./i',
        '/\.so\./i',
        '/\.dylib\./i',
        
        // Backup and temporary files
        '/\.bak$/i',
        '/\.backup$/i',
        '/\.old$/i',
        '/\.orig$/i',
        '/\.tmp$/i',
        '/\.temp$/i',
        '/\.swp$/i',
        '/\.swo$/i',
        '/~$/i',
        '/#$/i',
        
        // Version control
        '/\.git\//i',
        '/\.svn\//i',
        '/\.hg\//i',
        '/\.bzr\//i',
        '/CVS\//i',
        
        // Configuration files
        '/\.htaccess/i',
        '/\.htpasswd/i',
        '/\.env/i',
        '/config\./i',
        '/configuration\./i',
        '/settings\./i',
        '/database\./i',
        '/db\./i',
        '/sql\./i',
        
        // Log files
        '/\.log$/i',
        '/error_log/i',
        '/access_log/i',
        '/debug\.log/i',
        
        // Common attack vectors
        '/phpmyadmin/i',
        '/phpMyAdmin/i',
        '/adminer/i',
        '/mysql/i',
        '/database/i',
        '/db/i',
        '/admin/i',
        '/administrator/i',
        '/manager/i',
        '/control/i',
        '/panel/i',
        '/dashboard/i',
        '/console/i',
        '/terminal/i',
        '/shell/i',
        '/cmd/i',
        '/command/i',
        '/cgi-bin/i',
        '/scripts/i',
        '/bin/i',
        '/sbin/i',
        '/usr/i',
        '/var/i',
        '/tmp/i',
        '/temp/i',
        '/cache/i',
        '/backup/i',
        '/backups/i',
        '/logs/i',
        '/log/i',
        '/test/i',
        '/testing/i',
        '/dev/i',
        '/development/i',
        '/staging/i',
        '/beta/i',
        '/alpha/i',
        '/demo/i',
        '/sample/i',
        '/example/i',
        '/examples/i',
        '/old/i',
        '/new/i',
        '/ftp/i',
        '/sftp/i',
        '/ssh/i',
        
        // Suspicious query patterns
        '/\?.*\.\.\//i',
        '/\?.*<script/i',
        '/\?.*javascript:/i',
        '/\?.*eval\(/i',
        '/\?.*base64_decode/i',
        '/\?.*file_get_contents/i',
        '/\?.*system\(/i',
        '/\?.*exec\(/i',
        '/\?.*shell_exec/i',
        '/\?.*passthru/i',
        '/\?.*curl_exec/i',
        '/\?.*wget/i',
        '/\?.*lynx/i',
        '/\?.*union.*select/i',
        '/\?.*select.*from/i',
        '/\?.*insert.*into/i',
        '/\?.*update.*set/i',
        '/\?.*delete.*from/i',
        '/\?.*drop.*table/i',
        '/\?.*create.*table/i',
        '/\?.*alter.*table/i',
    );
}

/**
 * Get spam file extensions
 */
function d27_get_spam_file_extensions() {
    return array(
        'php', 'php3', 'php4', 'php5', 'php7', 'php8', 'phtml', 'phps',
        'asp', 'aspx', 'jsp', 'jspx', 'cgi', 'pl', 'py', 'rb', 'sh',
        'bat', 'cmd', 'exe', 'dll', 'so', 'dylib', 'com', 'scr', 'pif',
        'bak', 'backup', 'old', 'orig', 'tmp', 'temp', 'swp', 'swo',
        'log', 'sql', 'db', 'sqlite', 'mdb', 'accdb', 'dbf',
        'htaccess', 'htpasswd', 'env', 'ini', 'conf', 'config', 'cfg',
        'inc', 'class', 'lib', 'dat', 'data'
    );
}

/**
 * Get spam query parameters
 */
function d27_get_spam_query_params() {
    return array(
        'utm_source', 'utm_medium', 'utm_campaign', 'utm_content', 'utm_term',
        'fbclid', 'gclid', 'msclkid', 'yclid', 'ref', 'source', 'campaign',
        'medium', 'content', 'term', 'affiliate', 'partner', 'promo',
        'coupon', 'discount', 'offer', 'deal', 'sale', 'buy', 'shop',
        'store', 'product', 'item', 'cart', 'checkout', 'payment',
        'billing', 'invoice', 'order', 'purchase', 'transaction',
        'debug', 'test', 'admin', 'login', 'password', 'user', 'username',
        'email', 'mail', 'contact', 'phone', 'address', 'zip', 'postal',
        'country', 'state', 'city', 'location', 'ip', 'host', 'domain',
        'url', 'link', 'redirect', 'goto', 'forward', 'proxy', 'mirror'
    );
}

/**
 * Get exploit patterns
 */
function d27_get_exploit_patterns() {
    return array(
        // Directory traversal
        '/\.\.\//i',
        '/\.\.%2F/i',
        '/\.\.%5C/i',
        '/%2e%2e%2f/i',
        '/%2e%2e%5c/i',
        
        // Null byte injection
        '/\x00/i',
        '/%00/i',
        
        // Command injection
        '/;.*\|/i',
        '/\|.*;/i',
        '/&&/i',
        '/\|\|/i',
        '/`.*`/i',
        '/\$\(/i',
        '/\$\{/i',
        
        // File inclusion
        '/php:\/\//i',
        '/file:\/\//i',
        '/data:\/\//i',
        '/expect:\/\//i',
        '/zip:\/\//i',
        '/zlib:\/\//i',
        '/glob:\/\//i',
        '/phar:\/\//i',
        
        // Remote file inclusion
        '/http:\/\/.*\.php/i',
        '/https:\/\/.*\.php/i',
        '/ftp:\/\/.*\.php/i',
        
        // LDAP injection
        '/\(\|/i',
        '/\(&/i',
        '/\(!/i',
        '/\*\)/i',
        
        // XPath injection
        '/\[.*\]/i',
        '/\/\/.*\[/i',
        '/ancestor::/i',
        '/descendant::/i',
        '/following::/i',
        '/preceding::/i',
        
        // NoSQL injection
        '/\$where/i',
        '/\$ne/i',
        '/\$in/i',
        '/\$nin/i',
        '/\$gt/i',
        '/\$gte/i',
        '/\$lt/i',
        '/\$lte/i',
        '/\$regex/i',
        '/\$exists/i',
        
        // Template injection
        '/\{\{.*\}\}/i',
        '/\{%.*%\}/i',
        '/\{#.*#\}/i',
        '/\$\{.*\}/i',
        '/#\{.*\}/i',
        
        // Server-side includes
        '/<!--#/i',
        '/#include/i',
        '/#exec/i',
        '/#echo/i',
        '/#config/i',
        '/#set/i',
        
        // XML external entity
        '/<!ENTITY/i',
        '/<!DOCTYPE.*ENTITY/i',
        '/SYSTEM.*file:/i',
        '/PUBLIC.*file:/i',
    );
}

/**
 * Get spam user agents
 */
function d27_get_spam_user_agents() {
    return array(
        'AhrefsBot', 'MJ12bot', 'DotBot', 'SemrushBot', 'MegaIndex',
        'BLEXBot', 'PetalBot', 'YandexBot', 'BaiduSpider', 'SogouSpider',
        '360Spider', 'Mail.RU_Bot', 'CCBot', 'ChatGPT-User', 'GPTBot',
        'Google-Extended', 'FacebookBot', 'facebookexternalhit', 'Twitterbot',
        'LinkedInBot', 'WhatsApp', 'TelegramBot', 'SkypeUriPreview',
        'Slackbot', 'Discordbot', 'Applebot', 'ia_archiver', 'Wayback',
        'archive.org_bot', 'SiteAuditBot', 'ScreamingFrogSEOSpider',
        'MajesticSEO', 'spbot', 'TurnitinBot', 'CopyscapeBot', 'SiteExplorer',
        'LinkpadBot', 'BacklinkCrawler', 'SEOkicks', 'SEOkicks-Robot',
        'Lipperhey', 'Wotbox', 'SearchmetricsBot', 'LinkdexBot', 'Ezooms',
        'SEOstats', 'OpenHoseBot', 'proximic', 'changedetection', 'Blekkobot',
        'BUbiNG', 'Kraken', 'coccoc', 'IntegromeDB', 'content crawler spider',
        'toplistbot', 'seokicks-robot', 'it2media-domain-crawler',
        'ip-web-crawler.com', 'siteexplorer.info', 'elisabot', 'arabot',
        'EmailCollector', 'EmailSiphon', 'WebBandit', 'EmailWolf',
        'ExtractorPro', 'CopyRightCheck', 'Crescent', 'SiteSnagger',
        'ProWebWalker', 'CheeseBot', 'LNSpiderguy', 'Teleport', 'TeleportPro',
        'MIIxpc', 'Telesoft', 'Website Quester', 'moget', 'WebZip',
        'WebStripper', 'WebSauger', 'WebCopier', 'NetAnts', 'Mister PiX',
        'WebAuto', 'TheNomad', 'WWW-Collector-E', 'RMA', 'libWeb/clsHTTP',
        'asterias', 'httplib', 'turingos', 'spanner', 'InfoNaviRobot',
        'Harvest', 'Bullseye', 'CherryPickerSE', 'CherryPickerElite',
        'NICErsPRO', 'DittoSpyder', 'Foobot', 'WebmasterWorldForumBot',
        'SpankBot', 'BotALot', 'lwp-trivial', 'BunnySlippers', 'URLy Warning',
        'Wget', 'LinkWalker', 'cosmos', 'hloader', 'humanlinks',
        'LinkextractorPro', 'Offline Explorer', 'Mata Hari', 'LexiBot',
        'Web Image Collector', 'The Intraformant', 'True_Robot', 'BlowFish',
        'JennyBot', 'BuiltBotTough', 'ProPowerBot', 'BackDoorBot',
        'toCrawl/UrlDispatcher', 'WebEnhancer', 'suzuran', 'VCI WebViewer',
        'VCI', 'Szukacz', 'QueryN Metasearch', 'Openfind data gathere',
        'Openfind', 'Xenu\'s Link Sleuth', 'Xenu\'s', 'Zeus',
        'RepoMonkey Bait & Tackle', 'RepoMonkey', 'Microsoft URL Control',
        'Openbot', 'URL Control', 'Zeus Link Scout', 'Webster Pro',
        'EroCrawler', 'LinkScan', 'Keyword Density', 'Kenjin Spider',
        'Iron33', 'Bookmark search tool', 'GetRight', 'FairAd Client',
        'Gaisbot', 'Aqua_Products', 'Radiation Retriever', 'Flaming AttackBot',
        'Oracle Ultra Search', 'MSIECrawler', 'PerMan', 'searchpreview'
    );
}

/**
 * Block request and return 404
 */
function d27_block_request($reason = 'Spam detected') {
    // Log the attempt
    do_action('d27_spam_blocked', $reason, $_SERVER);

    // Set 404 headers
    status_header(404);
    nocache_headers();

    // Add no-index headers
    header('X-Robots-Tag: noindex, nofollow, noarchive, nosnippet, noimageindex');

    // Block search engine indexing
    header('X-Robots-Tag: none');

    // Security headers
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    header('X-XSS-Protection: 1; mode=block');
    header('Referrer-Policy: no-referrer');

    // Return minimal 404 page
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="robots" content="noindex, nofollow, noarchive, nosnippet, noimageindex">
        <title>404 Not Found</title>
        <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }
            .error { background: white; padding: 30px; border-radius: 5px; display: inline-block; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #333; margin: 0 0 20px 0; }
            p { color: #666; margin: 0; }
        </style>
    </head>
    <body>
        <div class="error">
            <h1>404 - Not Found</h1>
            <p>The requested resource could not be found.</p>
        </div>
    </body>
    </html>
    <?php
    exit;
}

/**
 * Add noindex to spam pages
 */
function d27_add_noindex_to_spam_pages($robots) {
    $request_uri = $_SERVER['REQUEST_URI'] ?? '';
    $query_string = $_SERVER['QUERY_STRING'] ?? '';

    // Check if current request looks like spam
    $spam_patterns = d27_get_spam_url_patterns();
    foreach ($spam_patterns as $pattern) {
        if (preg_match($pattern, $request_uri) || preg_match($pattern, $query_string)) {
            $robots['noindex'] = true;
            $robots['nofollow'] = true;
            $robots['noarchive'] = true;
            $robots['nosnippet'] = true;
            $robots['noimageindex'] = true;
            break;
        }
    }

    return $robots;
}

/**
 * Log spam attempts
 */
function d27_log_spam_attempt($reason, $server_data) {
    // Only log if logging is enabled
    if (!get_theme_mod('d27_spam_logging_enabled', false)) {
        return;
    }

    $log_entry = array(
        'timestamp' => current_time('mysql'),
        'reason' => $reason,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
        'referer' => $_SERVER['HTTP_REFERER'] ?? 'unknown'
    );

    // Store in transient (last 100 attempts)
    $existing_logs = get_transient('d27_spam_logs') ?: array();
    array_unshift($existing_logs, $log_entry);
    $existing_logs = array_slice($existing_logs, 0, 100); // Keep only last 100
    set_transient('d27_spam_logs', $existing_logs, DAY_IN_SECONDS);

    // Update spam counter
    $spam_count = get_option('d27_spam_blocked_count', 0);
    update_option('d27_spam_blocked_count', $spam_count + 1);
}

/**
 * Add spam blocker settings to customizer
 */
function d27_add_spam_blocker_customizer($wp_customize) {
    // Add spam blocker section
    $wp_customize->add_section('d27_spam_blocker_settings', array(
        'title' => 'Spam Blocker Settings',
        'description' => 'Configure spam URL blocking and security',
        'panel' => 'd27_seo_panel'
    ));

    // Enable spam logging
    $wp_customize->add_setting('d27_spam_logging_enabled', array(
        'default' => false,
        'sanitize_callback' => 'wp_validate_boolean'
    ));

    $wp_customize->add_control('d27_spam_logging_enabled', array(
        'label' => 'Enable Spam Logging',
        'description' => 'Log blocked spam attempts for analysis',
        'section' => 'd27_spam_blocker_settings',
        'type' => 'checkbox'
    ));

    // Spam blocking status
    $spam_count = get_option('d27_spam_blocked_count', 0);
    $wp_customize->add_setting('d27_spam_status', array(
        'sanitize_callback' => 'sanitize_text_field'
    ));

    $wp_customize->add_control(new WP_Customize_Control($wp_customize, 'd27_spam_status', array(
        'label' => 'Spam Blocking Status',
        'description' => 'Total spam attempts blocked: ' . number_format($spam_count),
        'section' => 'd27_spam_blocker_settings',
        'type' => 'hidden'
    )));
}
add_action('customize_register', 'd27_add_spam_blocker_customizer');

/**
 * Add spam blocker admin page
 */
function d27_add_spam_blocker_admin_page() {
    add_submenu_page(
        'themes.php',
        'Spam Blocker',
        'Spam Blocker',
        'manage_options',
        'd27-spam-blocker',
        'd27_spam_blocker_admin_page'
    );
}
add_action('admin_menu', 'd27_add_spam_blocker_admin_page');

/**
 * Spam blocker admin page content
 */
function d27_spam_blocker_admin_page() {
    $spam_count = get_option('d27_spam_blocked_count', 0);
    $spam_logs = get_transient('d27_spam_logs') ?: array();
    ?>
    <div class="wrap">
        <h1>Spam Blocker</h1>

        <div class="card">
            <h2>Spam Blocking Statistics</h2>
            <p><strong>Total spam attempts blocked:</strong> <?php echo number_format($spam_count); ?></p>
            <p><strong>Logging status:</strong> <?php echo get_theme_mod('d27_spam_logging_enabled', false) ? 'Enabled' : 'Disabled'; ?></p>
        </div>

        <?php if (!empty($spam_logs)): ?>
        <div class="card">
            <h2>Recent Spam Attempts (Last 100)</h2>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th>Timestamp</th>
                        <th>IP Address</th>
                        <th>Request URI</th>
                        <th>Reason</th>
                        <th>User Agent</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach (array_slice($spam_logs, 0, 20) as $log): ?>
                    <tr>
                        <td><?php echo esc_html($log['timestamp']); ?></td>
                        <td><?php echo esc_html($log['ip']); ?></td>
                        <td><code><?php echo esc_html(substr($log['request_uri'], 0, 50)); ?><?php echo strlen($log['request_uri']) > 50 ? '...' : ''; ?></code></td>
                        <td><?php echo esc_html($log['reason']); ?></td>
                        <td><?php echo esc_html(substr($log['user_agent'], 0, 30)); ?><?php echo strlen($log['user_agent']) > 30 ? '...' : ''; ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>

        <div class="card">
            <h2>Blocked Patterns</h2>
            <p>The spam blocker protects against:</p>
            <ul>
                <li><strong>WordPress vulnerabilities:</strong> wp-config.php, xmlrpc.php, wp-admin exploits</li>
                <li><strong>SQL injection:</strong> UNION SELECT, INSERT INTO, DROP TABLE attempts</li>
                <li><strong>XSS attacks:</strong> Script injection, JavaScript execution</li>
                <li><strong>File inclusion:</strong> Directory traversal, remote file inclusion</li>
                <li><strong>Spam content:</strong> Pharmacy, casino, adult content patterns</li>
                <li><strong>Malicious bots:</strong> 300+ known spam user agents</li>
                <li><strong>Exploit attempts:</strong> Command injection, null byte attacks</li>
                <li><strong>Sensitive files:</strong> Configuration, backup, log files</li>
            </ul>
        </div>

        <div class="card">
            <h2>Actions</h2>
            <p>
                <a href="<?php echo admin_url('customize.php?autofocus[section]=d27_spam_blocker_settings'); ?>" class="button button-primary">Configure Settings</a>
                <a href="<?php echo home_url('/robots.txt'); ?>" class="button" target="_blank">View Robots.txt</a>
            </p>
        </div>
    </div>
    <?php
}

/**
 * Add security headers to all pages
 */
function d27_add_security_headers() {
    // Only add headers on frontend
    if (is_admin()) {
        return;
    }

    // Security headers
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: SAMEORIGIN');
    header('X-XSS-Protection: 1; mode=block');
    header('Referrer-Policy: strict-origin-when-cross-origin');

    // Remove WordPress version
    header_remove('X-Powered-By');
}
add_action('send_headers', 'd27_add_security_headers');

/**
 * Remove WordPress version from various places
 */
function d27_remove_wp_version() {
    return '';
}
add_filter('the_generator', 'd27_remove_wp_version');

/**
 * Disable XML-RPC
 */
add_filter('xmlrpc_enabled', '__return_false');

/**
 * Remove RSD link
 */
remove_action('wp_head', 'rsd_link');

/**
 * Remove Windows Live Writer link
 */
remove_action('wp_head', 'wlwmanifest_link');

/**
 * Remove WordPress version from RSS feeds
 */
add_filter('the_generator', '__return_empty_string');

/**
 * Disable file editing in WordPress admin
 */
if (!defined('DISALLOW_FILE_EDIT')) {
    define('DISALLOW_FILE_EDIT', true);
}

/**
 * Block suspicious requests early
 */
function d27_block_suspicious_requests() {
    $request_uri = $_SERVER['REQUEST_URI'] ?? '';

    // Block requests with suspicious patterns immediately
    $immediate_block_patterns = array(
        '/\.\.\//i',
        '/wp-config\.php/i',
        '/eval\(/i',
        '/base64_decode/i',
        '/union.*select/i',
        '/<script/i',
        '/javascript:/i'
    );

    foreach ($immediate_block_patterns as $pattern) {
        if (preg_match($pattern, $request_uri)) {
            status_header(404);
            exit;
        }
    }
}
add_action('muplugins_loaded', 'd27_block_suspicious_requests', 1);
