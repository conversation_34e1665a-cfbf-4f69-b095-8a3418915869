/**
 * Prevent content jumping during page load
 */
document.addEventListener('DOMContentLoaded', function() {
    // Set initial dimensions for game frame
    const gameFrame = document.getElementById('game-box');
    if (gameFrame) {
        // Calculate and set aspect ratio based container width
        const containerWidth = gameFrame.offsetWidth;
        const height = (containerWidth * 10) / 16; // 16:10 aspect ratio
        gameFrame.style.height = Math.min(711, height) + 'px';
    }

    // Handle game iframe
    const gameIframe = document.querySelector('.game-iframe');
    if (gameIframe) {
        // Ensure iframe is properly sized and positioned
        gameIframe.style.cssText = `
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            border: none !important;
            display: none; /* Initially hidden until play button is clicked */
        `;
    }

    // Preload thumbnail image if available
    const playButton = document.querySelector('.play-button');
    if (playButton && playButton.hasAttribute('style')) {
        const thumbnailUrl = playButton.style.getPropertyValue('--game-thumbnail').replace(/['"]/g, '');
        if (thumbnailUrl) {
            const img = new Image();
            img.src = thumbnailUrl.slice(4, -1); // Remove url() wrapper
        }
    }

    // Fix content layout immediately
    fixContentLayout();
});

// Fix content layout function
function fixContentLayout() {
    // Remove duplicate content if present
    const gameContent = document.querySelector('.game-content');
    if (gameContent) {
        // Find and remove duplicate headings
        const headings = gameContent.querySelectorAll('h1, h2, h3');
        const title = document.querySelector('.game-title h1').textContent.trim();
        headings.forEach(heading => {
            if (heading.textContent.trim() === title) {
                heading.remove();
            }
        });

        // Find and remove empty paragraphs
        const paragraphs = gameContent.querySelectorAll('p');
        paragraphs.forEach(p => {
            if (!p.textContent.trim() && !p.querySelector('img')) {
                p.remove();
            }
        });
    }

    // Ensure game description has minimum height
    const gameDescription = document.querySelector('.game-description');
    if (gameDescription) {
        gameDescription.style.minHeight = '200px';
    }

    // Fix popular games section height
    const popularGames = document.querySelector('.popular-games');
    if (popularGames) {
        popularGames.style.height = 'fit-content';
    }
}

// Handle window resize
let resizeTimeout;
window.addEventListener('resize', function() {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(function() {
        const gameFrame = document.getElementById('game-box');
        if (gameFrame) {
            const containerWidth = gameFrame.offsetWidth;
            const height = (containerWidth * 10) / 16;
            gameFrame.style.height = Math.min(711, height) + 'px';
        }
    }, 100);
});