<script>
/**
 * D27 Gaming Theme - Game Interactive JavaScript
 * Consistent JS handling for all game pages (front page and single)
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('D27 Game Interactive initialized');
    
    // 1. Fetch fresh game stats first thing on page load
    fetchFreshGameStats();
    
    // 2. Initialize rating stars with hover effects
    initStarRating();
    
    // 3. Add additional backup click handler to play button
    initPlayButton();
    
    /**
     * Initialize star rating functionality
     */
    function initStarRating() {
        const starContainers = document.querySelectorAll('.star-rating');
        
        starContainers.forEach(container => {
            const gameId = container.getAttribute('data-game-id');
            const stars = container.querySelectorAll('.rating-star');
            const resultSpan = container.querySelector('.rating-result');
            const debugDiv = container.querySelector('#rating-debug');
            
            if (!gameId || !stars.length) {
                console.error('Missing game ID or stars');
                return;
            }
            
            console.log('Setting up ratings for game ID:', gameId);
            
            // Add hover effect
            stars.forEach((star, index) => {
                // Mouse enter effect
                star.addEventListener('mouseenter', function() {
                    // Fill in stars up to current hover
                    for (let i = 0; i < stars.length; i++) {
                        if (i <= index) {
                            stars[i].classList.add('hover');
                        } else {
                            stars[i].classList.remove('hover');
                        }
                    }
                });
                
                // Click handler
                star.addEventListener('click', function() {
                    const rating = index + 1;
                    console.log('Star clicked:', rating);
                    
                    if (debugDiv) {
                        debugDiv.style.display = 'block';
                        debugDiv.textContent = 'Submitting rating: ' + rating;
                    }
                    
                    // Send rating via fetch
                    if (typeof d27_ajax !== 'undefined') {
                        const formData = new FormData();
                        formData.append('action', 'submit_game_rating');
                        formData.append('game_id', gameId);
                        formData.append('rating', rating);
                        formData.append('nonce', d27_ajax.nonce);
                        
                        fetch(d27_ajax.ajax_url, {
                            method: 'POST',
                            body: formData
                        })
                        .then(response => response.json())
                        .then(data => {
                            console.log('Rating response:', data);
                            if (data.success) {
                                // If we have complete stats data, use it
                                if (data.data.stats) {
                                    updateGameStatsDisplay(data.data.stats);
                                } else {
                                    // Otherwise update directly from response
                                    if (resultSpan) {
                                        resultSpan.textContent = data.data.new_average + '/5 (' + data.data.total_votes + ' votes)';
                                    }
                                    
                                    // Update stars based on new average
                                    const roundedRating = Math.round(data.data.new_average);
                                    stars.forEach((s, i) => {
                                        s.classList.toggle('filled', i < roundedRating);
                                    });
                                }
                                
                                // Show success message
                                if (debugDiv) {
                                    debugDiv.textContent = 'Rating submitted successfully!';
                                    setTimeout(() => {
                                        debugDiv.style.display = 'none';
                                    }, 3000);
                                }
                                
                                showNotification('Rating submitted successfully!', 'success');
                            } else {
                                // Show error
                                if (debugDiv) {
                                    debugDiv.textContent = 'Error: ' + (data.data?.message || 'Unknown error');
                                }
                                console.error('Rating error:', data);
                                showNotification('Error submitting rating', 'error');
                            }
                        })
                        .catch(error => {
                            console.error('Rating fetch error:', error);
                            if (debugDiv) {
                                debugDiv.textContent = 'Error: ' + error.message;
                            }
                            showNotification('Error submitting rating', 'error');
                        });
                    } else {
                        console.error('d27_ajax not defined');
                        if (debugDiv) {
                            debugDiv.textContent = 'Error: AJAX configuration missing';
                        }
                        showNotification('Error: AJAX configuration missing', 'error');
                    }
                });
            });
            
            // Reset stars on mouse leave
            container.addEventListener('mouseleave', function() {
                stars.forEach((star) => {
                    star.classList.remove('hover');
                });
            });
        });
    }
    
    /**
     * Initialize play button with redundant handling
     */
    function initPlayButton() {
        const playBtn = document.querySelector('.play-button');
        if (playBtn) {
            console.log('Play button found, adding additional click handler');
            
            // Add additional click handler as backup
            playBtn.addEventListener('click', function(e) {
                const gameId = this.getAttribute('onclick')?.match(/playGame\((\d+)\)/)?.[1];
                if (gameId && window.playGame) {
                    console.log('Play button clicked for game ID:', gameId);
                    window.playGame(parseInt(gameId));
                    return false;
                } else {
                    console.error('Could not extract game ID or playGame function not available');
                }
            });
        }
    }
    
    /**
     * Fetch fresh game stats from server
     */
    function fetchFreshGameStats() {
        const gameId = document.querySelector('.star-rating')?.getAttribute('data-game-id');
        if (!gameId) return;
        
        console.log('Fetching fresh game stats for ID:', gameId);
        
        // Use either the theme's AJAX configuration or build URL manually
        const ajaxUrl = (typeof d27_ajax !== 'undefined' ? d27_ajax.ajax_url : '/wp-admin/admin-ajax.php');
        
        fetch(ajaxUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                'action': 'get_fresh_game_stats',
                'game_id': gameId,
                'nonce': (typeof d27_ajax !== 'undefined' ? d27_ajax.nonce : '')
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Received fresh game stats:', data);
                updateGameStatsDisplay(data.data);
            } else {
                console.error('Error fetching fresh stats:', data);
            }
        })
        .catch(error => {
            console.error('Error fetching game stats:', error);
        });
    }
    
    /**
     * Update game stats display with fresh data
     */
    function updateGameStatsDisplay(stats) {
        // Update play count
        const playCountDisplay = document.getElementById('play-count-display');
        if (playCountDisplay && stats.play_count !== undefined) {
            playCountDisplay.textContent = formatNumber(stats.play_count);
        }
        
        // Update rating display
        const ratingResult = document.querySelector('.rating-result');
        if (ratingResult && stats.avg_rating !== undefined) {
            ratingResult.textContent = stats.avg_rating + '/5 (' + stats.total_votes + ' votes)';
        }
        
        // Update star display
        const stars = document.querySelectorAll('.star-rating .rating-star');
        if (stars.length && stats.avg_rating !== undefined) {
            const roundedRating = Math.round(stats.avg_rating);
            
            stars.forEach((star, i) => {
                star.classList.toggle('filled', i < roundedRating);
            });
        }
    }
    
    /**
     * Format number for display
     */
    function formatNumber(num) {
        if (!num) return '0';
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
    
    /**
     * Show notification
     */
    function showNotification(message, type = 'info') {
        if (document.querySelector('.game-notification')) {
            document.querySelector('.game-notification').remove();
        }
        
        // Create notification
        const notification = document.createElement('div');
        notification.className = 'game-notification ' + type;
        notification.textContent = message;
        
        // Add to page
        document.body.appendChild(notification);
        
        // Trigger animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // Remove after delay
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
    
    // Expose functions globally in case they need to be called directly
    window.d27 = window.d27 || {};
    window.d27.fetchFreshGameStats = fetchFreshGameStats;
    window.d27.updateGameStatsDisplay = updateGameStatsDisplay;
    window.d27.showNotification = showNotification;
});
</script>

<style>
/* Game Notification Styles */
.game-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    background: rgba(0, 0, 0, 0.9);
    color: #fff;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border-left: 4px solid #00d4ff;
    max-width: 300px;
    font-size: 0.9rem;
}

.game-notification.show {
    transform: translateX(0);
}

.game-notification.success {
    border-left-color: #00d4ff;
}

.game-notification.error {
    border-left-color: #ff4757;
}

.game-notification.info {
    border-left-color: #f1c40f;
}

/* Enhanced Star Rating Styles */
.rating-star {
    cursor: pointer;
    font-size: 24px;
    color: gold;
    margin: 0 2px;
    transition: transform 0.2s ease, color 0.2s ease, text-shadow 0.2s ease;
    display: inline-block;
    user-select: none;
}

.rating-star.hover,
.rating-star:hover {
    transform: scale(1.3);
    color: #ffcc00;
    text-shadow: 0 0 8px rgba(255, 215, 0, 0.7);
}

.rating-star.filled {
    color: #ffaa00;
    text-shadow: 0 0 5px rgba(255, 170, 0, 0.5);
}

.rating-result {
    margin-left: 10px;
    font-weight: bold;
    color: #00bfff;
    vertical-align: middle;
    text-shadow: 0 0 3px rgba(0, 191, 255, 0.3);
}

/* Game Stats Styling */
.game-stats {
    margin: 1.5rem 0;
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
    padding: 1.2rem;
    border-radius: 10px;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(0, 212, 255, 0.2);
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
}

.game-stats .rating,
.game-stats .play-count {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(0, 212, 255, 0.1);
}

.game-stats strong {
    color: #00d4ff;
    font-weight: 600;
    text-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
}

#play-count-display {
    color: #fff;
    font-weight: bold;
    transition: color 0.3s ease;
}
</style>