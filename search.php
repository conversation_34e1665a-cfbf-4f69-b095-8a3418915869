<?php
/**
 * The template for displaying search results
 */

get_header();

$search_query = get_search_query();
$total_results = $wp_query->found_posts;
?>

<div class="search-results-header">
    <h1 class="search-title">
        🔍 Search Results
        <?php if ($search_query) : ?>
            for "<span class="search-query"><?php echo esc_html($search_query); ?></span>"
        <?php endif; ?>
    </h1>
    
    <div class="search-meta">
        <?php if ($total_results > 0) : ?>
            <p class="results-count">
                Found <strong><?php echo esc_html(number_format($total_results)); ?></strong> 
                <?php echo $total_results === 1 ? 'result' : 'results'; ?>
            </p>
        <?php else : ?>
            <p class="no-results-count">No results found</p>
        <?php endif; ?>
    </div>
    
    <!-- Enhanced Search Form -->
    <div class="search-again">
        <?php get_search_form(); ?>
    </div>
</div>

<?php if (have_posts()) : ?>
    
    <!-- Filter Options -->
    <div class="search-filters">
        <div class="filter-tabs">
            <button class="filter-tab active" data-filter="all">All Results</button>
            <button class="filter-tab" data-filter="game">Games</button>
            <button class="filter-tab" data-filter="post">Posts</button>
            <button class="filter-tab" data-filter="page">Pages</button>
        </div>
        
        <div class="sort-options">
            <label for="search-sort">Sort by:</label>
            <select id="search-sort">
                <option value="relevance">Relevance</option>
                <option value="date">Date</option>
                <option value="title">Title</option>
                <option value="popularity">Popularity</option>
            </select>
        </div>
    </div>
    
    <!-- Search Results -->
    <div class="search-results-container">
        <?php while (have_posts()) : the_post(); ?>
            
            <article id="post-<?php the_ID(); ?>" <?php post_class('search-result-item'); ?> data-post-type="<?php echo esc_attr(get_post_type()); ?>">
                
                <?php if (get_post_type() === 'game') : ?>
                    <!-- Game Result -->
                    <div class="result-thumbnail">
                        <a href="<?php the_permalink(); ?>">
                            <?php if (has_post_thumbnail()) : ?>
                                <?php the_post_thumbnail('thumbnail'); ?>
                            <?php else : ?>
                                <div class="no-thumbnail game-thumb">
                                    <span class="game-icon">🎮</span>
                                </div>
                            <?php endif; ?>
                            
                            <div class="result-overlay">
                                <span class="play-icon">▶️</span>
                                <span class="play-text">PLAY</span>
                            </div>
                        </a>
                    </div>
                    
                    <div class="result-content">
                        <div class="result-header">
                            <span class="result-type game-type">🎮 Game</span>
                            <h2 class="result-title">
                                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                            </h2>
                        </div>
                        
                        <div class="result-excerpt">
                            <?php echo wp_trim_words(get_the_excerpt() ?: get_the_content(), 25, '...'); ?>
                        </div>
                        
                        <div class="result-meta">
                            <?php
                            $rating = get_post_meta(get_the_ID(), '_rating', true);
                            $play_count = get_post_meta(get_the_ID(), '_play_count', true);
                            ?>
                            
                            <?php if ($rating) : ?>
                                <span class="meta-item rating">
                                    <span class="stars">
                                        <?php
                                        for ($i = 1; $i <= 5; $i++) {
                                            echo $i <= $rating ? '⭐' : '☆';
                                        }
                                        ?>
                                    </span>
                                    <span class="rating-value"><?php echo esc_html($rating); ?></span>
                                </span>
                            <?php endif; ?>
                            
                            <?php if ($play_count) : ?>
                                <span class="meta-item plays">
                                    <span class="play-icon">▶️</span>
                                    <?php echo esc_html(number_format($play_count)); ?> plays
                                </span>
                            <?php endif; ?>
                            
                            <span class="meta-item date">
                                📅 <?php echo get_the_date(); ?>
                            </span>
                        </div>
                        
                        <div class="result-tags">
                            <?php
                            $game_tags = get_the_terms(get_the_ID(), 'game_tag');
                            if ($game_tags && !is_wp_error($game_tags)) {
                                foreach (array_slice($game_tags, 0, 3) as $tag) {
                                    echo '<span class="result-tag">' . esc_html($tag->name) . '</span>';
                                }
                            }
                            ?>
                        </div>
                    </div>
                    
                <?php else : ?>
                    <!-- Post/Page Result -->
                    <div class="result-thumbnail">
                        <a href="<?php the_permalink(); ?>">
                            <?php if (has_post_thumbnail()) : ?>
                                <?php the_post_thumbnail('thumbnail'); ?>
                            <?php else : ?>
                                <div class="no-thumbnail post-thumb">
                                    <span class="post-icon">
                                        <?php echo get_post_type() === 'page' ? '📄' : '📝'; ?>
                                    </span>
                                </div>
                            <?php endif; ?>
                        </a>
                    </div>
                    
                    <div class="result-content">
                        <div class="result-header">
                            <span class="result-type <?php echo esc_attr(get_post_type()); ?>-type">
                                <?php echo get_post_type() === 'page' ? '📄 Page' : '📝 Post'; ?>
                            </span>
                            <h2 class="result-title">
                                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                            </h2>
                        </div>
                        
                        <div class="result-excerpt">
                            <?php echo wp_trim_words(get_the_excerpt() ?: get_the_content(), 30, '...'); ?>
                        </div>
                        
                        <div class="result-meta">
                            <span class="meta-item date">
                                📅 <?php echo get_the_date(); ?>
                            </span>
                            
                            <span class="meta-item author">
                                👤 <?php the_author(); ?>
                            </span>
                            
                            <?php if (get_comments_number() > 0) : ?>
                                <span class="meta-item comments">
                                    💬 <?php echo get_comments_number(); ?> comments
                                </span>
                            <?php endif; ?>
                        </div>
                        
                        <?php if (get_post_type() === 'post') : ?>
                            <div class="result-tags">
                                <?php
                                $categories = get_the_category();
                                if ($categories) {
                                    foreach (array_slice($categories, 0, 3) as $category) {
                                        echo '<span class="result-tag">' . esc_html($category->name) . '</span>';
                                    }
                                }
                                ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
                
            </article>
            
        <?php endwhile; ?>
    </div>
    
    <!-- Pagination -->
    <div class="search-pagination">
        <?php
        the_posts_pagination(array(
            'mid_size' => 2,
            'prev_text' => __('← Previous', 'd27-gaming'),
            'next_text' => __('Next →', 'd27-gaming'),
        ));
        ?>
    </div>
    
<?php else : ?>
    
    <!-- No Results Found -->
    <div class="no-search-results">
        <div class="no-results-icon">🔍</div>
        <h2>No results found</h2>
        
        <?php if ($search_query) : ?>
            <p>Sorry, no results were found for "<strong><?php echo esc_html($search_query); ?></strong>".</p>
        <?php else : ?>
            <p>Please enter a search term to find content.</p>
        <?php endif; ?>
        
        <div class="search-suggestions">
            <h3>Try these suggestions:</h3>
            <ul>
                <li>Check your spelling</li>
                <li>Use different keywords</li>
                <li>Try more general terms</li>
                <li>Browse our <a href="<?php echo esc_url(get_post_type_archive_link('game')); ?>">games collection</a></li>
            </ul>
        </div>
        
        <!-- Popular Games as Alternative -->
        <div class="alternative-content">
            <h3>🔥 Popular Games</h3>
            <div class="popular-games-grid">
                <?php
                $popular_games = d27_get_popular_games(6);
                if ($popular_games->have_posts()) :
                    while ($popular_games->have_posts()) : $popular_games->the_post();
                ?>
                    <div class="popular-game-item">
                        <a href="<?php the_permalink(); ?>">
                            <?php if (has_post_thumbnail()) : ?>
                                <?php the_post_thumbnail('thumbnail'); ?>
                            <?php else : ?>
                                <div class="no-thumb">🎮</div>
                            <?php endif; ?>
                            <h4><?php the_title(); ?></h4>
                        </a>
                    </div>
                <?php
                    endwhile;
                    wp_reset_postdata();
                endif;
                ?>
            </div>
        </div>
    </div>
    
<?php endif; ?>

<?php get_footer(); ?>

<style>
/* Search Results Styles */
.search-results-header {
    background: rgba(0, 0, 0, 0.8);
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
    text-align: center;
}

.search-title {
    color: #00d4ff;
    font-size: 2rem;
    margin-bottom: 1rem;
}

.search-query {
    color: #fff;
    background: rgba(0, 212, 255, 0.2);
    padding: 0.2rem 0.5rem;
    border-radius: 5px;
}

.search-meta {
    margin-bottom: 1.5rem;
    color: #ccc;
}

.search-again {
    max-width: 400px;
    margin: 0 auto;
}

.search-filters {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(0, 0, 0, 0.8);
    padding: 1rem 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.filter-tabs {
    display: flex;
    gap: 0.5rem;
}

.filter-tab {
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border: 1px solid #00d4ff;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-tab.active,
.filter-tab:hover {
    background: #00d4ff;
    color: #000;
}

.sort-options select {
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border: 1px solid #00d4ff;
    border-radius: 5px;
}

.search-result-item {
    display: flex;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.search-result-item:hover {
    border-color: #00d4ff;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.2);
}

.result-thumbnail {
    width: 120px;
    height: 120px;
    margin-right: 1.5rem;
    flex-shrink: 0;
    position: relative;
    border-radius: 8px;
    overflow: hidden;
}

.result-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-thumbnail {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(45deg, #1a1a2e, #16213e);
    font-size: 2rem;
}

.result-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    color: #00d4ff;
}

.search-result-item:hover .result-overlay {
    opacity: 1;
}

.result-content {
    flex: 1;
}

.result-header {
    margin-bottom: 0.5rem;
}

.result-type {
    display: inline-block;
    padding: 0.2rem 0.6rem;
    background: rgba(0, 212, 255, 0.2);
    color: #00d4ff;
    border-radius: 12px;
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
}

.result-title {
    margin: 0;
    font-size: 1.3rem;
}

.result-title a {
    color: #fff;
    text-decoration: none;
    transition: color 0.3s ease;
}

.result-title a:hover {
    color: #00d4ff;
}

.result-excerpt {
    color: #ccc;
    line-height: 1.6;
    margin: 1rem 0;
}

.result-meta {
    display: flex;
    gap: 1rem;
    margin: 1rem 0;
    flex-wrap: wrap;
}

.meta-item {
    color: #888;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.result-tags {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.result-tag {
    background: rgba(255, 255, 255, 0.1);
    color: #ccc;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.8rem;
}

.no-search-results {
    text-align: center;
    padding: 3rem 2rem;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
    color: #fff;
}

.no-results-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.7;
}

.search-suggestions {
    margin: 2rem 0;
    text-align: left;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.search-suggestions h3 {
    color: #00d4ff;
    margin-bottom: 1rem;
}

.search-suggestions ul {
    list-style: none;
    padding: 0;
}

.search-suggestions li {
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.alternative-content {
    margin-top: 3rem;
}

.alternative-content h3 {
    color: #00d4ff;
    margin-bottom: 1.5rem;
}

.popular-games-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.popular-game-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.3s ease;
}

.popular-game-item:hover {
    background: rgba(0, 212, 255, 0.1);
}

.popular-game-item img,
.popular-game-item .no-thumb {
    width: 100%;
    height: 100px;
    object-fit: cover;
    border-radius: 5px;
    margin-bottom: 0.5rem;
}

.popular-game-item .no-thumb {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(45deg, #1a1a2e, #16213e);
    font-size: 2rem;
}

.popular-game-item h4 {
    margin: 0;
    font-size: 0.9rem;
    color: #fff;
}

.popular-game-item a {
    text-decoration: none;
    color: inherit;
}

/* Responsive Design */
@media (max-width: 768px) {
    .search-filters {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-tabs {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .search-result-item {
        flex-direction: column;
        padding: 1rem;
    }
    
    .result-thumbnail {
        width: 100%;
        height: 200px;
        margin-right: 0;
        margin-bottom: 1rem;
    }
    
    .result-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .popular-games-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    }
}
</style>

<script>
// Search results filtering and sorting
document.addEventListener('DOMContentLoaded', function() {
    const filterTabs = document.querySelectorAll('.filter-tab');
    const sortSelect = document.getElementById('search-sort');
    const resultItems = document.querySelectorAll('.search-result-item');
    
    // Filter functionality
    filterTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const filter = this.dataset.filter;
            
            // Update active tab
            filterTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
            
            // Filter results
            resultItems.forEach(item => {
                const postType = item.dataset.postType;
                if (filter === 'all' || postType === filter) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    });
    
    // Sort functionality
    if (sortSelect) {
        sortSelect.addEventListener('change', function() {
            const sortBy = this.value;
            const container = document.querySelector('.search-results-container');
            const items = Array.from(resultItems);
            
            items.sort((a, b) => {
                switch(sortBy) {
                    case 'title':
                        const titleA = a.querySelector('.result-title a').textContent;
                        const titleB = b.querySelector('.result-title a').textContent;
                        return titleA.localeCompare(titleB);
                    case 'date':
                        // This would need actual date data attributes
                        return 0;
                    case 'popularity':
                        // This would need popularity data attributes
                        return 0;
                    default: // relevance
                        return 0;
                }
            });
            
            items.forEach(item => container.appendChild(item));
        });
    }
});
</script>
