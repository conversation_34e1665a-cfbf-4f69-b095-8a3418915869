<?php
/**
 * D27 Gaming Theme Customizer
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add customizer settings
 */
function d27_customize_register($wp_customize) {
    
    // Add D27 Gaming Panel
    $wp_customize->add_panel('d27_gaming_panel', array(
        'title' => __('🎮 D27 Gaming Options', 'd27-gaming'),
        'description' => __('Customize your gaming website settings', 'd27-gaming'),
        'priority' => 30,
    ));
    
    // ===== SITE IDENTITY SECTION =====
    $wp_customize->add_section('d27_site_identity', array(
        'title' => __('🏠 Site Identity', 'd27-gaming'),
        'panel' => 'd27_gaming_panel',
        'priority' => 10,
    ));
    
    // Site Logo (Emoji)
    $wp_customize->add_setting('d27_site_logo', array(
        'default' => '🎮',
        'sanitize_callback' => 'sanitize_text_field',
        'transport' => 'refresh',
    ));
    
    $wp_customize->add_control('d27_site_logo', array(
        'label' => __('Site Logo (Emoji)', 'd27-gaming'),
        'description' => __('Enter an emoji to use as your site logo', 'd27-gaming'),
        'section' => 'd27_site_identity',
        'type' => 'text',
    ));
    
    // Site Tagline
    $wp_customize->add_setting('d27_site_tagline', array(
        'default' => 'Play Amazing Geometry Dash Games Online!',
        'sanitize_callback' => 'sanitize_text_field',
        'transport' => 'refresh',
    ));
    
    $wp_customize->add_control('d27_site_tagline', array(
        'label' => __('Site Tagline', 'd27-gaming'),
        'description' => __('A short description of your gaming site', 'd27-gaming'),
        'section' => 'd27_site_identity',
        'type' => 'text',
    ));
    
    // ===== FEATURED GAME SECTION =====
    $wp_customize->add_section('d27_featured_game', array(
        'title' => __('🌟 Featured Game', 'd27-gaming'),
        'panel' => 'd27_gaming_panel',
        'priority' => 20,
    ));
    
    // Featured Game Selection
    $games = get_posts(array(
        'post_type' => 'game',
        'posts_per_page' => -1,
        'post_status' => 'publish',
    ));
    
    $game_choices = array('' => __('Auto (Latest Game)', 'd27-gaming'));
    foreach ($games as $game) {
        $game_choices[$game->ID] = $game->post_title;
    }
    
    $wp_customize->add_setting('d27_featured_game', array(
        'default' => '',
        'sanitize_callback' => 'absint',
        'transport' => 'refresh',
    ));
    
    $wp_customize->add_control('d27_featured_game', array(
        'label' => __('Featured Game', 'd27-gaming'),
        'description' => __('Select which game to feature on the homepage', 'd27-gaming'),
        'section' => 'd27_featured_game',
        'type' => 'select',
        'choices' => $game_choices,
    ));
    
    // Auto-rotate Featured Game
    $wp_customize->add_setting('d27_auto_rotate_featured', array(
        'default' => false,
        'sanitize_callback' => 'wp_validate_boolean',
        'transport' => 'refresh',
    ));
    
    $wp_customize->add_control('d27_auto_rotate_featured', array(
        'label' => __('Auto-rotate Featured Game', 'd27-gaming'),
        'description' => __('Automatically change featured game daily', 'd27-gaming'),
        'section' => 'd27_featured_game',
        'type' => 'checkbox',
    ));
    
    // ===== APPEARANCE SECTION =====
    $wp_customize->add_section('d27_appearance', array(
        'title' => __('🎨 Appearance', 'd27-gaming'),
        'panel' => 'd27_gaming_panel',
        'priority' => 30,
    ));
    
    // Primary Color
    $wp_customize->add_setting('d27_primary_color', array(
        'default' => '#00d4ff',
        'sanitize_callback' => 'sanitize_hex_color',
        'transport' => 'postMessage',
    ));
    
    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'd27_primary_color', array(
        'label' => __('Primary Color', 'd27-gaming'),
        'description' => __('Main accent color for the theme', 'd27-gaming'),
        'section' => 'd27_appearance',
    )));
    
    // Background Color
    $wp_customize->add_setting('d27_background_color', array(
        'default' => '#1a1a2e',
        'sanitize_callback' => 'sanitize_hex_color',
        'transport' => 'postMessage',
    ));
    
    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'd27_background_color', array(
        'label' => __('Background Color', 'd27-gaming'),
        'description' => __('Main background color', 'd27-gaming'),
        'section' => 'd27_appearance',
    )));
    
    // Header Background
    $wp_customize->add_setting('d27_header_background', array(
        'default' => 'rgba(0, 0, 0, 0.95)',
        'sanitize_callback' => 'sanitize_text_field',
        'transport' => 'postMessage',
    ));
    
    $wp_customize->add_control('d27_header_background', array(
        'label' => __('Header Background', 'd27-gaming'),
        'description' => __('CSS background value for header', 'd27-gaming'),
        'section' => 'd27_appearance',
        'type' => 'text',
    ));
    
    // ===== NAVIGATION SECTION =====
    $wp_customize->add_section('d27_navigation', array(
        'title' => __('🧭 Navigation Menu', 'd27-gaming'),
        'panel' => 'd27_gaming_panel',
        'priority' => 35,
    ));

    // Menu Sync Information
    $wp_customize->add_setting('d27_menu_sync_info', array(
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('d27_menu_sync_info', array(
        'label' => __('🔄 Auto-Sync with WordPress Menus', 'd27-gaming'),
        'description' => __('✅ CONNECTED: Your navigation is now linked with WordPress Menus! Go to Customizer ▸ Menus to manage your navigation. Changes there will automatically sync with your website interface. The custom JSON navigation below is used as fallback only.', 'd27-gaming'),
        'section' => 'd27_navigation',
        'type' => 'hidden',
    ));

    // Navigation Items (now as fallback)
    $wp_customize->add_setting('d27_nav_items', array(
        'default' => json_encode(array(
            array('name' => 'Home', 'url' => home_url('/'), 'target' => '_self'),
            array('name' => 'All Games', 'url' => get_post_type_archive_link('game'), 'target' => '_self'),
            array('name' => 'Popular', 'url' => add_query_arg('orderby', 'popularity', get_post_type_archive_link('game')), 'target' => '_self'),
            array('name' => 'Latest', 'url' => add_query_arg('orderby', 'date', get_post_type_archive_link('game')), 'target' => '_self'),
        )),
        'sanitize_callback' => 'd27_sanitize_nav_items',
        'transport' => 'refresh',
    ));

    $wp_customize->add_control('d27_nav_items', array(
        'label' => __('Fallback Navigation Items (JSON)', 'd27-gaming'),
        'description' => __('⚠️ FALLBACK ONLY: This is used only when no WordPress menu is assigned to "Primary Menu" location. Prefer using Customizer ▸ Menus instead. Format: [{"name":"Home","url":"' . home_url('/') . '","target":"_self"}]', 'd27-gaming'),
        'section' => 'd27_navigation',
        'type' => 'textarea',
    ));

    // ===== SIDEBAR SECTION =====
    $wp_customize->add_section('d27_sidebar', array(
        'title' => __('📋 Sidebar Content', 'd27-gaming'),
        'panel' => 'd27_gaming_panel',
        'priority' => 36,
    ));

    // Sidebar Items
    $wp_customize->add_setting('d27_sidebar_items', array(
        'default' => json_encode(array(
            array(
                'type' => 'popular_games',
                'title' => '🔥 Most Played Games',
                'count' => 6,
                'enabled' => true
            ),
            array(
                'type' => 'game_categories',
                'title' => '🏷️ Game Categories',
                'count' => 8,
                'enabled' => true
            ),
            array(
                'type' => 'latest_games',
                'title' => '🆕 Latest Games',
                'count' => 5,
                'enabled' => true
            ),
            array(
                'type' => 'game_stats',
                'title' => '📊 Game Stats',
                'enabled' => true
            ),
        )),
        'sanitize_callback' => 'd27_sanitize_sidebar_items',
        'transport' => 'refresh',
    ));

    $wp_customize->add_control('d27_sidebar_items', array(
        'label' => __('Sidebar Sections (JSON)', 'd27-gaming'),
        'description' => __('Edit sidebar sections in JSON format. Use the theme options page for easier editing.', 'd27-gaming'),
        'section' => 'd27_sidebar',
        'type' => 'textarea',
    ));

    // ===== LAYOUT SECTION =====
    $wp_customize->add_section('d27_layout', array(
        'title' => __('📐 Layout', 'd27-gaming'),
        'panel' => 'd27_gaming_panel',
        'priority' => 40,
    ));

    // Games per Page
    $wp_customize->add_setting('d27_games_per_page', array(
        'default' => 12,
        'sanitize_callback' => 'absint',
        'transport' => 'refresh',
    ));

    $wp_customize->add_control('d27_games_per_page', array(
        'label' => __('Games per Page', 'd27-gaming'),
        'description' => __('Number of games to show on archive pages', 'd27-gaming'),
        'section' => 'd27_layout',
        'type' => 'number',
        'input_attrs' => array(
            'min' => 6,
            'max' => 48,
            'step' => 6,
        ),
    ));

    // Popular Games Count
    $wp_customize->add_setting('d27_popular_games_count', array(
        'default' => 6,
        'sanitize_callback' => 'absint',
        'transport' => 'refresh',
    ));

    $wp_customize->add_control('d27_popular_games_count', array(
        'label' => __('Popular Games Count', 'd27-gaming'),
        'description' => __('Number of popular games to show in sidebar', 'd27-gaming'),
        'section' => 'd27_layout',
        'type' => 'number',
        'input_attrs' => array(
            'min' => 3,
            'max' => 12,
            'step' => 1,
        ),
    ));
    
    // ===== FEATURES SECTION =====
    $wp_customize->add_section('d27_features', array(
        'title' => __('⚙️ Features', 'd27-gaming'),
        'panel' => 'd27_gaming_panel',
        'priority' => 50,
    ));
    
    // Enable Comments
    $wp_customize->add_setting('d27_enable_comments', array(
        'default' => true,
        'sanitize_callback' => 'wp_validate_boolean',
        'transport' => 'refresh',
    ));
    
    $wp_customize->add_control('d27_enable_comments', array(
        'label' => __('Enable Comments', 'd27-gaming'),
        'description' => __('Allow users to comment on games', 'd27-gaming'),
        'section' => 'd27_features',
        'type' => 'checkbox',
    ));
    
    // Enable Game Ratings
    $wp_customize->add_setting('d27_enable_ratings', array(
        'default' => true,
        'sanitize_callback' => 'wp_validate_boolean',
        'transport' => 'refresh',
    ));
    
    $wp_customize->add_control('d27_enable_ratings', array(
        'label' => __('Enable Game Ratings', 'd27-gaming'),
        'description' => __('Allow users to rate games', 'd27-gaming'),
        'section' => 'd27_features',
        'type' => 'checkbox',
    ));
    
    // Enable Favorites
    $wp_customize->add_setting('d27_enable_favorites', array(
        'default' => true,
        'sanitize_callback' => 'wp_validate_boolean',
        'transport' => 'refresh',
    ));
    
    $wp_customize->add_control('d27_enable_favorites', array(
        'label' => __('Enable Favorites', 'd27-gaming'),
        'description' => __('Allow users to favorite games', 'd27-gaming'),
        'section' => 'd27_features',
        'type' => 'checkbox',
    ));
    
    // ===== FOOTER SECTION =====
    $wp_customize->add_section('d27_footer', array(
        'title' => __('🦶 Footer', 'd27-gaming'),
        'panel' => 'd27_gaming_panel',
        'priority' => 60,
    ));
    
    // Footer Description
    $wp_customize->add_setting('d27_footer_description', array(
        'default' => 'Play the best Geometry Dash games online for free! Enjoy challenging levels, amazing music, and addictive gameplay.',
        'sanitize_callback' => 'wp_kses_post',
        'transport' => 'postMessage',
    ));
    
    $wp_customize->add_control('d27_footer_description', array(
        'label' => __('Footer Description', 'd27-gaming'),
        'description' => __('Text to display in the footer', 'd27-gaming'),
        'section' => 'd27_footer',
        'type' => 'textarea',
    ));
    
    // Footer Copyright
    $wp_customize->add_setting('d27_footer_copyright', array(
        'default' => '',
        'sanitize_callback' => 'wp_kses_post',
        'transport' => 'postMessage',
    ));
    
    $wp_customize->add_control('d27_footer_copyright', array(
        'label' => __('Custom Copyright Text', 'd27-gaming'),
        'description' => __('Leave empty to use default copyright', 'd27-gaming'),
        'section' => 'd27_footer',
        'type' => 'textarea',
    ));
    
    // ===== SEO SECTION =====
    $wp_customize->add_section('d27_seo', array(
        'title' => __('🔍 SEO Settings', 'd27-gaming'),
        'panel' => 'd27_gaming_panel',
        'priority' => 70,
    ));
    
    // SEO Title
    $wp_customize->add_setting('d27_seo_title', array(
        'default' => 'Geometry Dash Full Version - Play Free Online Games',
        'sanitize_callback' => 'sanitize_text_field',
        'transport' => 'refresh',
    ));
    
    $wp_customize->add_control('d27_seo_title', array(
        'label' => __('SEO Title', 'd27-gaming'),
        'description' => __('Custom title for search engines (leave empty to use site title)', 'd27-gaming'),
        'section' => 'd27_seo',
        'type' => 'text',
    ));
    
    // SEO Description
    $wp_customize->add_setting('d27_seo_description', array(
        'default' => 'Play Geometry Dash Full Version and thousands of other free games online. No downloads required!',
        'sanitize_callback' => 'sanitize_textarea_field',
        'transport' => 'refresh',
    ));
    
    $wp_customize->add_control('d27_seo_description', array(
        'label' => __('SEO Description', 'd27-gaming'),
        'description' => __('Meta description for search engines', 'd27-gaming'),
        'section' => 'd27_seo',
        'type' => 'textarea',
    ));
    
    // SEO Keywords
    $wp_customize->add_setting('d27_seo_keywords', array(
        'default' => 'geometry dash, online games, free games, browser games',
        'sanitize_callback' => 'sanitize_text_field',
        'transport' => 'refresh',
    ));
    
    $wp_customize->add_control('d27_seo_keywords', array(
        'label' => __('SEO Keywords', 'd27-gaming'),
        'description' => __('Comma-separated keywords for search engines', 'd27-gaming'),
        'section' => 'd27_seo',
        'type' => 'text',
    ));
}
add_action('customize_register', 'd27_customize_register');

/**
 * Bind JS handlers to instantly live-preview changes
 */
function d27_customize_preview_js() {
    wp_enqueue_script('d27-customizer', get_template_directory_uri() . '/assets/js/customizer.js', array('customize-preview'), '1.0.0', true);
}
add_action('customize_preview_init', 'd27_customize_preview_js');

/**
 * Add custom CSS based on customizer settings
 */
function d27_customizer_css() {
    $primary_color = get_theme_mod('d27_primary_color', '#00d4ff');
    $background_color = get_theme_mod('d27_background_color', '#1a1a2e');
    $header_background = get_theme_mod('d27_header_background', 'rgba(0, 0, 0, 0.95)');
    
    ?>
    <style type="text/css">
        :root {
            --d27-primary-color: <?php echo esc_html($primary_color); ?>;
            --d27-background-color: <?php echo esc_html($background_color); ?>;
            --d27-header-background: <?php echo esc_html($header_background); ?>;
        }
        
        body {
            background-color: var(--d27-background-color);
        }
        
        .site-header {
            background: var(--d27-header-background);
        }
        
        .site-title,
        .sidebar-title,
        .archive-title,
        .game-title,
        .comments-title,
        .main-navigation a:hover,
        .tag,
        .btn,
        .play-button {
            color: var(--d27-primary-color) !important;
        }
        
        .play-button,
        .btn,
        .submit-btn {
            background: linear-gradient(45deg, var(--d27-primary-color), color-mix(in srgb, var(--d27-primary-color) 80%, #000)) !important;
        }
        
        .game-frame,
        .main-navigation a:hover,
        .search-box input,
        .comment-form-field input:focus,
        .comment-form-field textarea:focus {
            border-color: var(--d27-primary-color) !important;
        }
        
        .tag {
            background: color-mix(in srgb, var(--d27-primary-color) 20%, transparent) !important;
        }
    </style>
    <?php
}
add_action('wp_head', 'd27_customizer_css');

/**
 * Auto-rotate featured game daily
 */
function d27_auto_rotate_featured_game() {
    if (!get_theme_mod('d27_auto_rotate_featured', false)) {
        return;
    }

    $last_rotation = get_option('d27_last_featured_rotation', 0);
    $current_time = time();

    // Check if 24 hours have passed
    if ($current_time - $last_rotation > DAY_IN_SECONDS) {
        $games = get_posts(array(
            'post_type' => 'game',
            'posts_per_page' => -1,
            'post_status' => 'publish',
            'orderby' => 'rand',
        ));

        if (!empty($games)) {
            $random_game = $games[0];
            set_theme_mod('d27_featured_game', $random_game->ID);
            update_option('d27_last_featured_rotation', $current_time);
        }
    }
}
add_action('wp_loaded', 'd27_auto_rotate_featured_game');

/**
 * Sanitize navigation items
 */
function d27_sanitize_nav_items($input) {
    $items = json_decode($input, true);
    if (!is_array($items)) {
        return json_encode(array());
    }

    $sanitized = array();
    foreach ($items as $item) {
        if (isset($item['name']) && isset($item['url'])) {
            $sanitized[] = array(
                'name' => sanitize_text_field($item['name']),
                'url' => esc_url_raw($item['url']),
                'target' => in_array($item['target'], array('_self', '_blank')) ? $item['target'] : '_self',
            );
        }
    }

    return json_encode($sanitized);
}

/**
 * Sanitize sidebar items
 */
function d27_sanitize_sidebar_items($input) {
    $items = json_decode($input, true);
    if (!is_array($items)) {
        return json_encode(array());
    }

    $allowed_types = array('popular_games', 'game_categories', 'latest_games', 'game_stats', 'custom_html', 'search');
    $sanitized = array();

    foreach ($items as $item) {
        if (isset($item['type']) && in_array($item['type'], $allowed_types)) {
            $sanitized_item = array(
                'type' => sanitize_text_field($item['type']),
                'title' => sanitize_text_field($item['title']),
                'enabled' => (bool) $item['enabled'],
            );

            if (isset($item['count'])) {
                $sanitized_item['count'] = absint($item['count']);
            }

            if (isset($item['content'])) {
                $sanitized_item['content'] = wp_kses_post($item['content']);
            }

            $sanitized[] = $sanitized_item;
        }
    }

    return json_encode($sanitized);
}



?>
