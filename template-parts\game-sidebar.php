<?php
/**
 * Template part for displaying game sidebar
 */

// Get custom sidebar items from customizer
$sidebar_items_json = get_theme_mod('d27_sidebar_items', '');
$sidebar_items = json_decode($sidebar_items_json, true);

// Default sidebar items if none configured
if (empty($sidebar_items) || !is_array($sidebar_items)) {
    $sidebar_items = array(
        array(
            'type' => 'popular_games',
            'title' => '🔥 Most Played Games',
            'count' => 6,
            'enabled' => true
        ),
        array(
            'type' => 'game_categories',
            'title' => '🏷️ Game Categories',
            'count' => 12,
            'enabled' => true
        ),
        array(
            'type' => 'latest_games',
            'title' => '🆕 Latest Games',
            'count' => 12,
            'enabled' => true
        ),
        array(
            'type' => 'game_stats',
            'title' => '📊 Game Stats',
            'enabled' => true
        ),
    );
}
?>

<div class="game-sidebar-container">
    <?php foreach ($sidebar_items as $item) : ?>
        <?php if (!$item['enabled']) continue; ?>

        <?php if ($item['type'] === 'popular_games') : ?>
            <!-- Popular Games Section -->
            <div class="sidebar-section popular-games-section">
                <h3 class="sidebar-title"><?php echo esc_html($item['title']); ?></h3>

                <div class="popular-games-list">
                    <?php
                    $count = isset($item['count']) ? intval($item['count']) : 6;
                    $popular_games = d27_get_popular_games($count);
                    if ($popular_games->have_posts()) :
                        $rank = 1;
                        while ($popular_games->have_posts()) : $popular_games->the_post();
                    ?>
                        <div class="popular-game-item">
                            <div class="game-rank"><?php echo esc_html($rank); ?></div>

                            <a href="<?php the_permalink(); ?>" class="popular-game-link">
                                <div class="popular-game-thumb">
                                    <?php if (has_post_thumbnail()) : ?>
                                        <?php the_post_thumbnail('thumbnail'); ?>
                                    <?php else : ?>
                                        <div class="no-thumb">
                                            <span class="game-icon">🎮</span>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="popular-game-info">
                                    <h4 class="popular-game-title"><?php the_title(); ?></h4>

                                    <div class="popular-game-stats">
                                        <?php
                                        $play_count = get_post_meta(get_the_ID(), '_play_count', true);
                                        $rating = get_post_meta(get_the_ID(), '_rating', true);
                                        ?>

                                        <?php if ($play_count) : ?>
                                            <span class="plays-count">
                                                <span class="play-icon">▶️</span>
                                                <?php echo esc_html(d27_format_number($play_count)); ?>
                                            </span>
                                        <?php endif; ?>

                                        <?php if ($rating) : ?>
                                            <span class="rating-display">
                                                <span class="star">⭐</span>
                                                <?php echo esc_html(number_format($rating, 1)); ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </a>
                        </div>
                    <?php
                        $rank++;
                        endwhile;
                        wp_reset_postdata();
                    else :
                    ?>
                        <p class="no-games">No popular games yet. Be the first to play!</p>
                    <?php endif; ?>
                </div>

                <div class="view-all-link">
                    <a href="<?php echo esc_url(add_query_arg('orderby', 'popularity', get_post_type_archive_link('game'))); ?>" class="btn-view-all">
                        View All Popular Games →
                    </a>
                </div>
            </div>

        <?php elseif ($item['type'] === 'game_categories') : ?>
            <!-- Game Categories Section -->
            <div class="sidebar-section game-categories-section">
                <h3 class="sidebar-title"><?php echo esc_html($item['title']); ?></h3>

                <div class="categories-list">
                    <?php
                    $count = isset($item['count']) ? intval($item['count']) : 12;
                    $game_tags = get_terms(array(
                        'taxonomy' => 'game_tag',
                        'hide_empty' => true,
                        'number' => $count,
                        'orderby' => 'count',
                        'order' => 'DESC',
                    ));

                    if (!empty($game_tags) && !is_wp_error($game_tags)) :
                        foreach ($game_tags as $tag) :
                    ?>
                        <a href="<?php echo esc_url(d27_get_tag_link($tag)); ?>" class="category-item">
                            <span class="category-name"><?php echo esc_html($tag->name); ?></span>
                            <span class="category-count"><?php echo esc_html($tag->count); ?></span>
                        </a>
                    <?php
                        endforeach;
                    else :
                    ?>
                        <p class="no-categories">No categories available yet.</p>
                    <?php endif; ?>
                </div>
            </div>

        <?php elseif ($item['type'] === 'latest_games') : ?>
            <!-- Latest Games Section -->
            <div class="sidebar-section latest-games-section">
                <h3 class="sidebar-title"><?php echo esc_html($item['title']); ?></h3>

                <div class="latest-games-list">
                    <?php
                    $count = isset($item['count']) ? intval($item['count']) : 12;
                    $latest_games = d27_get_latest_games($count);
                    if ($latest_games->have_posts()) :
                        while ($latest_games->have_posts()) : $latest_games->the_post();
                            // Skip current game if we're on a single game page
                            if (is_single() && get_the_ID() === get_queried_object_id()) {
                                continue;
                            }
                    ?>
                        <div class="latest-game-item">
                            <a href="<?php the_permalink(); ?>" class="latest-game-link">
                                <div class="latest-game-thumb">
                                    <?php if (has_post_thumbnail()) : ?>
                                        <?php the_post_thumbnail('thumbnail'); ?>
                                    <?php else : ?>
                                        <div class="no-thumb">
                                            <span class="game-icon">🎮</span>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="latest-game-info">
                                    <h4 class="latest-game-title"><?php the_title(); ?></h4>
                                    <span class="latest-game-date"><?php echo human_time_diff(get_the_time('U'), current_time('timestamp')) . ' ago'; ?></span>
                                </div>
                            </a>
                        </div>
                    <?php
                        endwhile;
                        wp_reset_postdata();
                    endif;
                    ?>
                </div>
            </div>
    
    <!-- Random Game Section -->
    <div class="sidebar-section random-game-section">
        <h3 class="sidebar-title">🎲 Random Game</h3>
        
        <?php
        $random_game = new WP_Query(array(
            'post_type' => 'game',
            'posts_per_page' => 1,
            'orderby' => 'rand',
            'post__not_in' => is_single() ? array(get_queried_object_id()) : array(),
        ));
        
        if ($random_game->have_posts()) :
            while ($random_game->have_posts()) : $random_game->the_post();
        ?>
            <div class="random-game-card">
                <a href="<?php the_permalink(); ?>" class="random-game-link">
                    <div class="random-game-thumb">
                        <?php if (has_post_thumbnail()) : ?>
                            <?php the_post_thumbnail('medium'); ?>
                        <?php else : ?>
                            <div class="no-thumb large">
                                <span class="game-icon">🎮</span>
                            </div>
                        <?php endif; ?>
                        
                        <div class="random-overlay">
                            <span class="play-icon">▶️</span>
                            <span class="play-text">Try This!</span>
                        </div>
                    </div>
                    
                    <div class="random-game-info">
                        <h4 class="random-game-title"><?php the_title(); ?></h4>
                        
                        <?php
                        $rating = get_post_meta(get_the_ID(), '_rating', true);
                        if ($rating) :
                        ?>
                            <div class="random-game-rating">
                                <span class="stars">
                                    <?php
                                    for ($i = 1; $i <= 5; $i++) {
                                        echo $i <= $rating ? '⭐' : '☆';
                                    }
                                    ?>
                                </span>
                            </div>
                        <?php endif; ?>
                        
                        <p class="random-game-excerpt">
                            <?php echo wp_trim_words(get_the_excerpt(), 12, '...'); ?>
                        </p>
                    </div>
                </a>
                
                <button class="btn-try-random" onclick="location.reload();">
                    🎲 Try Another Random Game
                </button>
            </div>
        <?php
            endwhile;
            wp_reset_postdata();
        endif;
        ?>
    </div>
    
    <!-- Game Stats Section -->
    <div class="sidebar-section game-stats-section">
        <h3 class="sidebar-title">📊 Game Stats</h3>
        
        <div class="stats-grid">
            <?php
            $total_games = wp_count_posts('game')->publish;
            $total_plays = 0;
            
            // Calculate total plays
            $all_games = get_posts(array(
                'post_type' => 'game',
                'posts_per_page' => -1,
                'fields' => 'ids',
            ));
            
            foreach ($all_games as $game_id) {
                $plays = get_post_meta($game_id, '_play_count', true);
                $total_plays += intval($plays);
            }
            ?>
            
            <div class="stat-item">
                <span class="stat-icon">🎮</span>
                <div class="stat-info">
                    <span class="stat-number"><?php echo esc_html(number_format($total_games)); ?></span>
                    <span class="stat-label">Total Games</span>
                </div>
            </div>
            
            <div class="stat-item">
                <span class="stat-icon">▶️</span>
                <div class="stat-info">
                    <span class="stat-number"><?php echo esc_html(d27_format_number($total_plays)); ?></span>
                    <span class="stat-label">Total Plays</span>
                </div>
            </div>
            
            <div class="stat-item">
                <span class="stat-icon">👥</span>
                <div class="stat-info">
                    <span class="stat-number"><?php echo esc_html(d27_format_number($total_plays * 0.7)); ?></span>
                    <span class="stat-label">Players</span>
                </div>
            </div>

        <?php elseif ($item['type'] === 'search') : ?>
            <!-- Search Section -->
            <div class="sidebar-section search-section">
                <h3 class="sidebar-title"><?php echo esc_html($item['title']); ?></h3>
                <div class="sidebar-search">
                    <?php get_search_form(); ?>
                </div>
            </div>

        <?php elseif ($item['type'] === 'custom_html') : ?>
            <!-- Custom HTML Section -->
            <div class="sidebar-section custom-html-section">
                <h3 class="sidebar-title"><?php echo esc_html($item['title']); ?></h3>
                <div class="custom-html-content">
                    <?php echo wp_kses_post($item['content'] ?? ''); ?>
                </div>
            </div>

        <?php elseif ($item['type'] === 'game_stats') : ?>
            <!-- Game Stats Section -->
            <div class="sidebar-section game-stats-section">
                <h3 class="sidebar-title"><?php echo esc_html($item['title']); ?></h3>

                <div class="stats-grid">
                    <?php
                    $total_games = wp_count_posts('game')->publish;
                    $total_plays = 0;

                    // Calculate total plays
                    $all_games = get_posts(array(
                        'post_type' => 'game',
                        'posts_per_page' => -1,
                        'fields' => 'ids',
                    ));

                    foreach ($all_games as $game_id) {
                        $plays = get_post_meta($game_id, '_play_count', true);
                        $total_plays += intval($plays);
                    }

                    $game_tags = get_terms(array('taxonomy' => 'game_tag', 'hide_empty' => true));
                    $tag_count = is_array($game_tags) ? count($game_tags) : 0;
                    ?>

                    <div class="stat-item">
                        <span class="stat-icon">🎮</span>
                        <div class="stat-info">
                            <span class="stat-number"><?php echo esc_html(number_format($total_games)); ?></span>
                            <span class="stat-label">Total Games</span>
                        </div>
                    </div>

                    <div class="stat-item">
                        <span class="stat-icon">▶️</span>
                        <div class="stat-info">
                            <span class="stat-number"><?php echo esc_html(d27_format_number($total_plays)); ?></span>
                            <span class="stat-label">Total Plays</span>
                        </div>
                    </div>

                    <div class="stat-item">
                        <span class="stat-icon">👥</span>
                        <div class="stat-info">
                            <span class="stat-number"><?php echo esc_html(d27_format_number($total_plays * 0.7)); ?></span>
                            <span class="stat-label">Players</span>
                        </div>
                    </div>

                    <div class="stat-item">
                        <span class="stat-icon">🏆</span>
                        <div class="stat-info">
                            <span class="stat-number"><?php echo esc_html($tag_count); ?></span>
                            <span class="stat-label">Categories</span>
                        </div>
                    </div>
                </div>
            </div>

        <?php endif; ?>

    <?php endforeach; ?>

</div>

<style>
/* Game Sidebar Styles */
.game-sidebar-container {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
    padding: 0;
    overflow: hidden;
}

.sidebar-section {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(0, 212, 255, 0.2);
}

.sidebar-section:last-child {
    border-bottom: none;
}

.sidebar-title {
    color: #00d4ff;
    font-size: 1.2rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #00d4ff;
}

/* Popular Games */
.popular-game-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.8rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.popular-game-item:hover {
    background: rgba(0, 212, 255, 0.1);
}

.game-rank {
    background: #00d4ff;
    color: #000;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
    margin-right: 0.8rem;
    flex-shrink: 0;
}

.popular-game-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: inherit;
    flex: 1;
}

.popular-game-thumb {
    width: 50px;
    height: 50px;
    border-radius: 6px;
    overflow: hidden;
    margin-right: 0.8rem;
    flex-shrink: 0;
}

.popular-game-thumb img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-thumb {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #1a1a2e, #16213e);
    display: flex;
    align-items: center;
    justify-content: center;
}

.no-thumb.large {
    height: 120px;
}

.game-icon {
    font-size: 1.5rem;
    opacity: 0.7;
}

.popular-game-info {
    flex: 1;
}

.popular-game-title {
    color: #fff;
    font-size: 0.9rem;
    margin: 0 0 0.3rem 0;
    line-height: 1.3;
}

.popular-game-stats {
    display: flex;
    gap: 0.8rem;
    font-size: 0.8rem;
    color: #ccc;
}

.plays-count,
.rating-display {
    display: flex;
    align-items: center;
    gap: 0.2rem;
}

/* Categories */
.categories-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.category-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.6rem 0.8rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    text-decoration: none;
    color: #fff;
    transition: all 0.3s ease;
}

.category-item:hover {
    background: rgba(0, 212, 255, 0.2);
    color: #00d4ff;
}

.category-count {
    background: #00d4ff;
    color: #000;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    font-size: 0.8rem;
    font-weight: bold;
}

/* Latest Games */
.latest-game-item {
    margin-bottom: 1rem;
}

.latest-game-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: inherit;
    padding: 0.6rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.latest-game-link:hover {
    background: rgba(0, 212, 255, 0.1);
}

.latest-game-thumb {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    overflow: hidden;
    margin-right: 0.8rem;
    flex-shrink: 0;
}

.latest-game-thumb img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.latest-game-title {
    color: #fff;
    font-size: 0.9rem;
    margin: 0 0 0.2rem 0;
}

.latest-game-date {
    color: #888;
    font-size: 0.8rem;
}

/* Random Game */
.random-game-card {
    text-align: center;
}

.random-game-thumb {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.random-game-thumb img {
    width: 100%;
    height: 120px;
    object-fit: cover;
}

.random-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    color: #00d4ff;
}

.random-game-card:hover .random-overlay {
    opacity: 1;
}

.random-overlay .play-icon {
    font-size: 2rem;
    margin-bottom: 0.3rem;
}

.random-game-title {
    color: #fff;
    font-size: 1rem;
    margin: 0 0 0.5rem 0;
}

.random-game-rating {
    margin-bottom: 0.5rem;
}

.random-game-excerpt {
    color: #ccc;
    font-size: 0.85rem;
    line-height: 1.4;
    margin-bottom: 1rem;
}

.btn-try-random {
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    color: #fff;
    border: none;
    padding: 0.6rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.85rem;
    font-weight: bold;
    transition: all 0.3s ease;
    width: 100%;
}

.btn-try-random:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
}

/* Game Stats */
.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 0.8rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.stat-icon {
    font-size: 1.5rem;
}

.stat-number {
    color: #00d4ff;
    font-size: 1.1rem;
    font-weight: bold;
    display: block;
}

.stat-label {
    color: #ccc;
    font-size: 0.8rem;
}

.view-all-link {
    text-align: center;
    margin-top: 1rem;
}

.btn-view-all {
    color: #00d4ff;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.btn-view-all:hover {
    color: #fff;
}

.no-games,
.no-categories {
    color: #888;
    font-style: italic;
    text-align: center;
    padding: 1rem;
}

/* Search Section */
.sidebar-search {
    margin-top: 1rem;
}

.sidebar-search .search-form {
    max-width: 100%;
}

.sidebar-search .search-field {
    width: 100%;
    padding: 0.6rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(0, 212, 255, 0.5);
    border-radius: 20px;
    color: #fff;
    font-size: 0.9rem;
}

.sidebar-search .search-submit {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: #00d4ff;
    color: #000;
    border: none;
    padding: 0.3rem 0.6rem;
    border-radius: 15px;
    cursor: pointer;
    font-size: 0.8rem;
}

/* Custom HTML Section */
.custom-html-content {
    color: #ccc;
    line-height: 1.6;
}

.custom-html-content p {
    margin-bottom: 1rem;
}

.custom-html-content a {
    color: #00d4ff;
    text-decoration: none;
}

.custom-html-content a:hover {
    color: #fff;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar-section {
        padding: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .popular-game-item {
        padding: 0.6rem;
    }
    
    .game-rank {
        width: 20px;
        height: 20px;
        font-size: 0.8rem;
    }
}
</style>
