/* D27 Gaming Theme - Inline Critical CSS */
/* This CSS is inlined in the head for performance optimization */

/* Critical Above-the-fold Styles */
body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: #1a1a2e;
    color: #fff;
    line-height: 1.6;
}

/* Header Critical Styles */
.site-header {
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    position: relative;
    border-bottom: 1px solid rgba(0, 212, 255, 0.3);
    z-index: 10000;
    width: 100% !important; /* Full width without scrollbar - prevents horizontal scroll */
    max-width: 100% !important; /* Ensure no overflow - prevents horizontal scroll */
    margin: 0 !important;
    box-sizing: border-box;
}

.header-container {
    display: flex !important;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    width: 100% !important; /* Full width without scrollbar - prevents horizontal scroll */
    max-width: 100% !important; /* Ensure no overflow - prevents horizontal scroll */
    padding: 0 20px; /* Add horizontal padding for content spacing */
    margin: 0 !important;
    height: 100%;
    box-sizing: border-box;
}

.site-title {
    color: #00d4ff;
    text-decoration: none;
    font-size: 1.8rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.site-logo {
    font-size: 2rem;
}

/* Full Width Navigation Critical Styles */
.main-navigation {
    position: relative;
    z-index: 10001;
    width: 100%;
    height: 100%;
}

.full-width-nav {
    width: 100%;
    height: 100%;
}

.main-navigation ul {
    list-style: none;
    margin: 0;
    padding: 0 1rem;
    display: flex;
    gap: 0;
    flex-wrap: nowrap;
    overflow: visible;
    width: 100%;
    height: 100%;
    align-items: center;
    box-sizing: border-box;
    justify-content: space-between;
}

.nav-menu-dynamic {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
    list-style: none;
    padding: 0;
    margin: 0;
    width: 100%;
    height: 100%;
}

.nav-item {
    white-space: nowrap;
    flex-shrink: 0;
    padding: 0 0.3rem;
    text-align: center;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-search {
    margin-left: auto;
}

.dropdown-toggle {
    display: none; /* will display in JS if needed */
}

.nav-home {
    flex: 0 0 auto;
    margin-right: 1rem;
    padding-left: 0.5rem;
    min-width: auto;
    width: auto;
    position: relative;
    z-index: 10;
}

.home-link {
    display: flex !important;
    align-items: center;
    gap: 0.5rem;
    color: #00d4ff !important;
    font-weight: bold !important;
    background: none !important;
    height: 100%;
    white-space: nowrap;
    padding: 0.5rem 1rem;
}

.site-logo {
    font-size: 1.8rem;
}

.site-title-nav {
    font-size: 1.2rem;
}

.nav-search {
    flex: 0 0 auto;
    margin-left: 1rem;
    padding-right: 0.5rem;
    min-width: auto;
    width: auto;
}

.search-box-nav {
    display: flex;
    align-items: center;
    height: 100%;
}

.search-box-nav input {
    padding: 0.4rem 0.8rem;
    border: none;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    width: 180px;
    font-size: 0.85rem;
}

.main-navigation a {
    color: #fff;
    text-decoration: none;
    padding: 0.5rem 0.8rem;
    border-radius: 20px;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 0.85rem;
    white-space: nowrap;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    height: 100%;
    width: 100%;
}

.main-navigation a:hover {
    background: #00d4ff;
    color: #000;
}

/* Dropdown Menu Critical Styles */
.dropdown-menu {
    position: relative;
}

.dropdown-toggle {
    cursor: pointer;
    user-select: none;
}

.dropdown-link {
    position: absolute;
    top: 100%;
    right: 0;
    background: rgba(0, 0, 0, 0.98);
    border: 1px solid rgba(0, 212, 255, 0.5);
    border-radius: 8px;
    min-width: 200px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 99999;
}

.dropdown-link--menu {
    display: flex;
    flex-direction: column;
    gap: 0;
    padding: 0.5rem 0;
}

.dropdown-menu:hover .dropdown-link {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Search Box Critical Styles */
.search-box input {
    padding: 0.5rem 1rem;
    border: 1px solid rgba(0, 212, 255, 0.5);
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    width: 200px;
}

.search-box input::placeholder {
    color: #ccc;
}

/* Main Content Critical Styles */
.main-content {
    max-width: 1200px;
    margin: 1rem auto 2rem auto; /* Reduced top margin from 2rem to 1rem */
    padding: 0 2rem;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0;
}

/* Game Box & Game Frame Critical Styles - Same Size */
#game-box,
.game-frame {
    background: rgba(0, 0, 0, 0.9);
    border-radius: 15px;
    padding: 2rem;
    margin-top: -1rem; /* Move closer to header */
    margin-bottom: 3rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 212, 255, 0.2);
    position: relative;
    width: 100%;
    max-width: 1136.02px;
    height: 711.38px;
    margin: 0 auto;
    background: #000;
    border-radius: 10px;
    overflow: hidden;
    border: 2px solid #00d4ff;
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    border: none;
    border-radius: 15px;
    min-width: 200px;
    min-height: 80px;
    color: #fff;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    box-shadow: 0 5px 20px rgba(0, 212, 255, 0.4);
    transition: all 0.3s ease;
    z-index: 10;
    font-family: 'Arial', sans-serif;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}

.play-button:hover {
    transform: translate(-50%, -50%) scale(1.05);
    box-shadow: 0 10px 30px rgba(0, 212, 255, 0.6);
    background: linear-gradient(45deg, #0099cc, #00d4ff);
}

.play-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease;
}

.play-button:hover .play-icon {
    transform: scale(1.1);
}

.play-text {
    font-size: 16px;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Allow JavaScript to hide the play button - Enhanced */
.play-button[style*="display: none"],
.play-button.hidden,
.play-button[style*="display:none"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
}

.play-button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 20px;
    transform: translateY(-50%);
    width: 60px;
    height: 60px;
    background-image: var(--game-thumbnail);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 8px;
    opacity: 0.8;
    z-index: 1;
}

.play-button.has-thumbnail::before {
    display: block;
}

.play-button:not(.has-thumbnail)::before {
    display: none;
}

.play-button .play-text {
    font-size: 1.5rem;
    font-weight: bold;
    letter-spacing: 2px;
    text-transform: uppercase;
    position: relative;
    z-index: 2;
    margin-left: 40px;
}

/* Game Controls Box Critical Styles */
.game-controls-box {
    background: rgba(0, 0, 0, 0.9);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    max-width: 1136.02px;
    margin: 2rem auto 2rem auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
    box-shadow: 0 5px 20px rgba(0, 212, 255, 0.1);
}

.game-title {
    flex: 1;
    min-width: 200px;
}

.game-title h1 {
    color: #fff;
    margin: 0;
    font-size: 1.8rem;
    font-weight: 600;
    text-align: left;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.game-action-buttons {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    flex-wrap: wrap;
}

.action-btn {
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    color: #fff;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.action-btn {
    padding: 0.75rem 1.5rem;
    min-width: 120px;
    text-align: center;
    justify-content: center;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
}

.action-btn.fullscreen-btn {
    background: linear-gradient(45deg, #ff6b00, #cc5500) !important;
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 999 !important;
}

.action-btn.fullscreen-btn:hover {
    background: linear-gradient(45deg, #cc5500, #ff6b00);
    box-shadow: 0 5px 15px rgba(255, 107, 0, 0.4);
}

/* Game Description Layout Critical Styles */
.game-stats {
    margin: 1.5rem 0;
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(0, 212, 255, 0.2);
}

.game-content {
    margin-top: 2rem;
}







/* Loading Spinner Critical Styles */
.loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: none;
    text-align: center;
    color: #00d4ff;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(0, 212, 255, 0.3);
    border-top: 4px solid #00d4ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Grid Layout Critical Styles */
.content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

/* Latest Games Section Critical Styles */
.latest-games-section {
    margin-top: 4rem;
    margin-bottom: 3rem;
    padding: 2rem 0;
}

.latest-games-section h2 {
    color: #00d4ff;
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2rem;
    padding-bottom: 1rem;
}

.view-all-games {
    text-align: center;
    margin-top: 3rem;
    padding-top: 2rem;
}

/* Button Critical Styles */
.btn {
    display: inline-block;
    padding: 1rem 2rem;
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    color: #000;
    text-decoration: none;
    border-radius: 25px;
    font-weight: bold;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
    color: #000;
}

.btn-primary {
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    color: #000;
}

.games-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(178px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
    justify-items: center;
}

/* Game Card Critical Styles - Rectangular Design */
.game-card {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    text-decoration: none;
    color: #fff;
    border: 1px solid transparent;
    width: 178px;
    height: 231px;
    display: flex;
    flex-direction: column;
}

.game-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 212, 255, 0.3);
    border-color: #00d4ff;
}

.game-card img,
.game-thumbnail img {
    width: 100%;
    height: 178px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.game-card:hover img,
.game-card:hover .game-thumbnail img {
    transform: scale(1.05);
}

.game-thumbnail {
    width: 100%;
    height: 178px;
    overflow: hidden;
    position: relative;
    flex-shrink: 0;
}

.game-card-content {
    padding: 0.75rem 0.5rem;
    height: 53px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    flex-shrink: 0;
}

.game-card h3 {
    color: #fff;
    margin: 0;
    font-size: 0.85rem;
    line-height: 1.2;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

/* Tag Styles */
.tag {
    display: inline-block;
    background: rgba(0, 212, 255, 0.2);
    color: #00d4ff;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.75rem;
    margin: 0.2rem 0.2rem 0 0;
    text-decoration: none;
    font-weight: 500;
}

/* Footer Critical Styles */
.site-footer {
    background: rgba(0, 0, 0, 0.95);
    color: #fff;
    padding: 2rem 0;
    text-align: center;
    margin-top: 3rem;
    border-top: 1px solid rgba(0, 212, 255, 0.3);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.footer-links a {
    color: #00d4ff;
    text-decoration: none;
    margin: 0 1rem;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #fff;
}

/* Responsive Critical Styles */
@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }
    
    .main-navigation ul {
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.5rem;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .content-grid {
        grid-template-columns: 1fr;
    }
    
    .games-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }
    
    #game-box,
    .game-frame {
        max-width: 100%;
        height: 480px;
        padding: 1rem;
    }

    .play-button {
        width: 180px;
        height: 70px;
        font-size: 1.3rem;
    }

    .play-button::before {
        width: 50px;
        height: 50px;
        left: 15px;
    }

    .play-button .play-text {
        margin-left: 30px;
    }

    .game-controls-box {
        max-width: 100%;
        padding: 1rem;
        flex-direction: column;
        text-align: center;
        margin: 1.5rem auto 2rem auto;
    }

    .game-title h1 {
        font-size: 1.4rem;
        text-align: center;
        margin-bottom: 1rem;
    }

    .game-action-buttons {
        justify-content: center;
    }
    
    .search-box input {
        width: 150px;
    }
}

@media (max-width: 480px) {
    .site-title {
        font-size: 1.5rem;
    }
    
    .games-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    }
    
    #game-box,
    .game-frame {
        height: 380px;
        padding: 0.5rem;
    }

    .game-controls-box {
        margin: 1rem auto 2rem auto;
        padding: 1rem;
    }

    .game-title h1 {
        font-size: 1.2rem;
    }

    .action-btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
        min-width: 70px;
    }

    .action-btn .like-count,
    .action-btn .favorite-count,
    .action-btn .share-count {
        font-size: 0.7rem;
        padding: 0.1rem 0.4rem;
    }

    .play-button {
        width: 160px;
        height: 60px;
        font-size: 1.1rem;
    }

    .play-button::before {
        width: 40px;
        height: 40px;
        left: 12px;
    }

    .play-button .play-text {
        margin-left: 20px;
    }
    
    .main-navigation ul {
        flex-direction: column;
        width: 100%;
    }
    
    .main-navigation a {
        text-align: center;
        padding: 0.8rem;
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Performance Optimizations */
* {
    box-sizing: border-box;
}

img {
    max-width: 100%;
    height: auto;
}

/* Lazy loading placeholder */
img.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

img.lazy.loaded {
    opacity: 1;
}

/* Preload critical fonts */
@font-face {
    font-family: 'system-ui';
    src: local('system-ui'), local('-apple-system'), local('BlinkMacSystemFont');
    font-display: swap;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .game-card {
        border: 2px solid #00d4ff;
    }
    
    .play-button {
        border: 3px solid #fff;
    }
}

/* Focus styles for accessibility */
button:focus,
a:focus,
input:focus,
textarea:focus,
select:focus {
    outline: 2px solid #00d4ff;
    outline-offset: 2px;
}

/* Dropdown Menu Styles for Overflow */
.dropdown-toggle {
    position: relative;
    flex-grow: 0 !important;
    flex-shrink: 0 !important;
    flex-basis: auto !important;
    z-index: 10000;
}

.dropdown-toggle button {
    background: transparent;
    color: white;
    border: none;
    cursor: pointer;
    padding: 10px;
    font-size: 16px;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.dropdown-toggle button:hover {
    background: #00d4ff;
    color: #000;
}

.dropdown-content {
    position: absolute;
    top: 100%;
    right: 0;
    background: rgba(17, 17, 17, 0.98);
    border: 1px solid rgba(0, 212, 255, 0.5);
    display: none;
    flex-direction: column;
    min-width: 200px;
    z-index: 99999;
    border-radius: 10px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    padding: 0.5rem 0;
    margin-top: 5px;
}

.dropdown-content.show {
    display: flex !important;
}

.dropdown-content > * {
    display: block !important;
    margin: 0;
    padding: 0;
}

.dropdown-content a {
    display: block !important;
    padding: 12px 16px !important;
    color: #fff !important;
    text-decoration: none !important;
    border-radius: 0 !important;
    transition: all 0.3s ease !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

.dropdown-content a:hover {
    background: rgba(0, 212, 255, 0.2) !important;
    color: #00d4ff !important;
}

.dropdown-content .nav-item {
    flex-grow: 0;
    flex-shrink: 0;
    flex-basis: auto;
    padding: 0;
    text-align: left;
    justify-content: flex-start;
    border-bottom: 1px solid #333;
}

.dropdown-content .nav-item:last-child {
    border-bottom: none;
}

.dropdown-content .nav-item a {
    padding: 12px 16px;
    display: block;
    width: 100%;
    text-align: left;
    border-radius: 0;
}

.dropdown-content .nav-item a:hover {
    background: #00d4ff;
    color: #000;
}

/* Print styles */
@media print {
    .site-header,
    .site-footer,
    .game-frame,
    .sidebar {
        display: none;
    }

    body {
        background: white;
        color: black;
    }
}

/* Force full-width header on all pages - highest specificity */
body .site-header,
body.home .site-header,
body.archive .site-header,
body.search .site-header,
body.single .site-header,
body.page .site-header,
body.tax-game_tag .site-header,
body.post-type-archive-game .site-header,
body.error404 .site-header {
    width: 100% !important; /* Full width without scrollbar - prevents horizontal scroll */
    max-width: 100% !important; /* Ensure no overflow - prevents horizontal scroll */
    margin: 0 !important;
    box-sizing: border-box !important;
}

body .header-container,
body.home .header-container,
body.archive .header-container,
body.search .header-container,
body.single .header-container,
body.page .header-container,
body.tax-game_tag .header-container,
body.post-type-archive-game .header-container,
body.error404 .header-container {
    width: 100% !important; /* Full width without scrollbar - prevents horizontal scroll */
    max-width: 100% !important; /* Ensure no overflow - prevents horizontal scroll */
    margin: 0 !important;
    display: flex !important;
    box-sizing: border-box !important;
}

/* Interactive Star Rating Critical Styles - Updated to match user's approach */
.rating-star {
    cursor: pointer;
    font-size: 20px;
    color: gold;
    margin: 0 2px;
    transition: transform 0.2s;
}

.rating-star.hover {
    transform: scale(1.2);
}

.rating-star.filled {
    color: orange;
}

.rating-result {
    margin-left: 10px;
    font-weight: bold;
    color: #00bfff;
}

.game-stats .play-count {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.play-count strong {
    color: #00d4ff;
}

#play-count-display {
    color: #fff;
    font-weight: bold;
}
