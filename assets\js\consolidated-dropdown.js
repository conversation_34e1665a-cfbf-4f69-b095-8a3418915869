/**
 * D27 Gaming Theme - Consolidated Dropdown Handler
 * Optimized and consolidated JavaScript for dropdown functionality
 * Combines: dropdown-handler.js, responsive-dropdown.js, menu-handler.js, strict-menu-handler.js
 */

(function() {
    'use strict';

    // Global variables
    let dropdownActive = false;
    let dropdownButton = null;
    let dropdownContainer = null;
    let touchDevice = false;
    let isInitialized = false;

    // Constants
    const MAX_VISIBLE_ITEMS = 6;
    const DROPDOWN_DELAY = 100;

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', init);
    window.addEventListener('load', init);
    
    // Also run immediately if page is already loaded
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        setTimeout(init, DROPDOWN_DELAY);
    }

    function init() {
        if (isInitialized) return;
        
        console.log('🎮 Initializing consolidated dropdown system');
        
        // Detect touch devices
        touchDevice = ('ontouchstart' in window) || 
                      (navigator.maxTouchPoints > 0) || 
                      (navigator.msMaxTouchPoints > 0);

        // Clean up any duplicates first
        removeDuplicateElements();
        
        // Create dropdown system
        createDropdownSystem();
        
        // Set up event handlers
        setupEventHandlers();
        
        // Apply menu limits
        enforceMenuLimits();
        
        isInitialized = true;
        console.log('✅ Dropdown system initialized successfully');
    }

    function removeDuplicateElements() {
        // Remove duplicate dropdown toggles
        const toggles = document.querySelectorAll('#dropdownToggle, .dropdown-toggle');
        if (toggles.length > 1) {
            console.log('🧹 Removing', toggles.length - 1, 'duplicate dropdown toggles');
            for (let i = 1; i < toggles.length; i++) {
                if (toggles[i] && toggles[i].parentNode) {
                    toggles[i].parentNode.removeChild(toggles[i]);
                }
            }
        }

        // Remove duplicate dropdown contents
        const contents = document.querySelectorAll('#dropdownContent, #inlineDropdown, #responsive-dropdown');
        if (contents.length > 1) {
            console.log('🧹 Removing', contents.length - 1, 'duplicate dropdown contents');
            for (let i = 1; i < contents.length; i++) {
                if (contents[i] && contents[i].parentNode) {
                    contents[i].parentNode.removeChild(contents[i]);
                }
            }
        }
    }

    function createDropdownSystem() {
        // Find or create dropdown toggle
        let dropdownToggle = document.getElementById('dropdownToggle');
        if (!dropdownToggle) {
            dropdownToggle = createDropdownToggle();
        }

        // Find or create dropdown content
        dropdownContainer = document.getElementById('dropdownContent') || 
                           document.getElementById('inlineDropdown') || 
                           document.getElementById('responsive-dropdown');
        
        if (!dropdownContainer) {
            dropdownContainer = createDropdownContent();
        }

        // Find the dropdown button
        dropdownButton = dropdownToggle.querySelector('button');
        
        if (!dropdownButton) {
            console.warn('⚠️ Dropdown button not found');
            return;
        }

        console.log('✅ Dropdown system created');
    }

    function createDropdownToggle() {
        const menu = document.querySelector('.nav-menu-dynamic') || document.querySelector('#primary-menu');
        if (!menu) return null;

        const toggle = document.createElement('li');
        toggle.id = 'dropdownToggle';
        toggle.className = 'dropdown-toggle nav-item';
        toggle.innerHTML = '<button type="button" onclick="toggleConsolidatedDropdown()">More ▼</button>';
        
        menu.appendChild(toggle);
        return toggle;
    }

    function createDropdownContent() {
        const container = document.createElement('div');
        container.id = 'consolidatedDropdown';
        container.className = 'dropdown-content';
        
        // Position relative to dropdown toggle
        const toggle = document.getElementById('dropdownToggle');
        if (toggle) {
            toggle.appendChild(container);
        } else {
            document.body.appendChild(container);
        }
        
        return container;
    }

    function setupEventHandlers() {
        if (!dropdownButton || !dropdownContainer) return;

        // Click handler for dropdown button
        dropdownButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            toggleDropdown();
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            if (dropdownActive && 
                !dropdownButton.contains(event.target) && 
                !dropdownContainer.contains(event.target)) {
                closeDropdown();
            }
        });

        // Keyboard support
        dropdownButton.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                toggleDropdown();
            }
        });

        // Resize handler
        let resizeTimeout;
        window.addEventListener('resize', function() {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(function() {
                enforceMenuLimits();
                if (dropdownActive) {
                    positionDropdown();
                }
            }, 250);
        });

        console.log('✅ Event handlers set up');
    }

    function enforceMenuLimits() {
        const menu = document.querySelector('.nav-menu-dynamic') || document.querySelector('#primary-menu');
        if (!menu) return;

        const allItems = Array.from(menu.querySelectorAll('li:not(#dropdownToggle):not(.dropdown-toggle)'));
        const dropdownToggle = document.getElementById('dropdownToggle');
        
        if (!dropdownToggle) return;

        // Clear dropdown content
        if (dropdownContainer) {
            dropdownContainer.innerHTML = '';
        }

        if (allItems.length <= MAX_VISIBLE_ITEMS) {
            // All items fit, hide dropdown
            dropdownToggle.style.display = 'none';
            allItems.forEach(item => item.style.display = 'flex');
        } else {
            // Show dropdown and limit visible items
            dropdownToggle.style.display = 'flex';
            
            allItems.forEach((item, index) => {
                if (index < MAX_VISIBLE_ITEMS) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                    addItemToDropdown(item);
                }
            });
        }

        console.log(`📊 Menu limited to ${MAX_VISIBLE_ITEMS} items, ${Math.max(0, allItems.length - MAX_VISIBLE_ITEMS)} in dropdown`);
    }

    function addItemToDropdown(menuItem) {
        if (!dropdownContainer) return;

        const link = menuItem.querySelector('a');
        if (!link) return;

        const dropdownItem = document.createElement('div');
        dropdownItem.className = 'dropdown-item';
        
        const dropdownLink = document.createElement('a');
        dropdownLink.href = link.href;
        dropdownLink.textContent = link.textContent;
        dropdownLink.target = link.target || '_self';
        
        // Add click handler
        dropdownLink.addEventListener('click', function() {
            closeDropdown();
        });

        dropdownItem.appendChild(dropdownLink);
        dropdownContainer.appendChild(dropdownItem);
    }

    function toggleDropdown() {
        if (dropdownActive) {
            closeDropdown();
        } else {
            openDropdown();
        }
    }

    function openDropdown() {
        if (!dropdownContainer) return;

        dropdownActive = true;
        dropdownContainer.classList.add('show');
        dropdownContainer.style.display = 'block';
        dropdownContainer.style.visibility = 'visible';
        dropdownContainer.style.opacity = '1';
        
        positionDropdown();
        
        // Add active class to button
        if (dropdownButton) {
            dropdownButton.classList.add('active');
        }

        console.log('📂 Dropdown opened');
    }

    function closeDropdown() {
        if (!dropdownContainer) return;

        dropdownActive = false;
        dropdownContainer.classList.remove('show');
        dropdownContainer.style.display = 'none';
        dropdownContainer.style.visibility = 'hidden';
        dropdownContainer.style.opacity = '0';
        
        // Remove active class from button
        if (dropdownButton) {
            dropdownButton.classList.remove('active');
        }

        console.log('📁 Dropdown closed');
    }

    function positionDropdown() {
        if (!dropdownContainer || !dropdownButton) return;

        const buttonRect = dropdownButton.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        
        // Position dropdown
        if (buttonRect.right + 200 > viewportWidth) {
            // Position to the left if not enough space on the right
            dropdownContainer.style.right = '0';
            dropdownContainer.style.left = 'auto';
        } else {
            dropdownContainer.style.left = '0';
            dropdownContainer.style.right = 'auto';
        }
    }

    // Global functions for backward compatibility
    window.toggleConsolidatedDropdown = toggleDropdown;
    window.toggleDropdown = toggleDropdown; // Fallback
    window.toggleResponsiveDropdown = toggleDropdown; // Fallback

    // Expose API for external use
    window.D27Dropdown = {
        toggle: toggleDropdown,
        open: openDropdown,
        close: closeDropdown,
        refresh: enforceMenuLimits,
        isActive: () => dropdownActive
    };

})();
