<?php
/**
 * The main template file
 *
 * @package D27_Gaming_Theme
 * @since 1.0.0
 */

get_header(); ?>

<main class="content-area" role="main">
    <?php if (have_posts()) : ?>

        <div class="games-grid" itemscope itemtype="https://schema.org/ItemList">
            <?php
            $post_count = 0;
            while (have_posts()) : the_post();
                $post_count++;
                $game_id = get_the_ID();
                $play_count = get_post_meta($game_id, '_play_count', true);
                $total_votes = (int) get_post_meta($game_id, '_total_votes', true);
                $total_score = (int) get_post_meta($game_id, '_total_score', true);
                $avg_rating = $total_votes > 0 ? round($total_score / $total_votes, 1) : 0;
            ?>
                <article
                    id="post-<?php the_ID(); ?>"
                    <?php post_class('game-card'); ?>
                    itemscope
                    itemtype="https://schema.org/Game"
                    itemprop="itemListElement">

                    <meta itemprop="position" content="<?php echo esc_attr($post_count); ?>">

                    <a href="<?php the_permalink(); ?>" itemprop="url" aria-label="Play <?php echo esc_attr(get_the_title()); ?>">
                        <div class="game-thumbnail">
                            <?php if (has_post_thumbnail()) : ?>
                                <?php
                                the_post_thumbnail('game-thumbnail', array(
                                    'alt' => get_the_title(),
                                    'loading' => 'lazy',
                                    'itemprop' => 'image'
                                ));
                                ?>
                            <?php else : ?>
                                <div class="no-image" role="img" aria-label="Game thumbnail placeholder">
                                    <span class="game-icon" aria-hidden="true">🎮</span>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="game-card-content">
                            <h3 itemprop="name"><?php the_title(); ?></h3>

                            <?php if ($avg_rating > 0 || $play_count > 0) : ?>
                                <div class="game-stats">
                                    <?php if ($avg_rating > 0) : ?>
                                        <span class="rating" itemprop="aggregateRating" itemscope itemtype="https://schema.org/AggregateRating">
                                            <meta itemprop="ratingValue" content="<?php echo esc_attr($avg_rating); ?>">
                                            <meta itemprop="bestRating" content="5">
                                            <meta itemprop="ratingCount" content="<?php echo esc_attr($total_votes); ?>">
                                            ⭐ <?php echo esc_html($avg_rating); ?>/5
                                        </span>
                                    <?php endif; ?>

                                    <?php if ($play_count > 0) : ?>
                                        <span class="play-count">
                                            🎮 <?php echo esc_html(number_format($play_count)); ?> plays
                                        </span>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </a>
                </article>
            <?php endwhile; ?>
        </div>
        
        <div class="pagination">
            <?php
            the_posts_pagination(array(
                'mid_size' => 2,
                'prev_text' => __('← Previous', 'd27-gaming'),
                'next_text' => __('Next →', 'd27-gaming'),
            ));
            ?>
        </div>
        
    <?php else : ?>
        
        <div class="no-content">
            <h2><?php _e('Nothing Found', 'd27-gaming'); ?></h2>
            <p><?php _e('It looks like nothing was found at this location. Maybe try a search?', 'd27-gaming'); ?></p>
            <?php get_search_form(); ?>
        </div>
        
    <?php endif; ?>
</div>

<?php get_footer(); ?>

<style>
/* Additional styles for index page */
.content-area {
    margin-bottom: 2rem;
}

.game-thumbnail.no-image {
    height: 150px;
    background: linear-gradient(45deg, #1a1a2e, #16213e);
    display: flex;
    align-items: center;
    justify-content: center;
}

.game-icon {
    font-size: 3rem;
    opacity: 0.7;
}

.game-meta {
    margin: 0.5rem 0;
    font-size: 0.9rem;
}

.game-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.3rem;
}

.rating-stars {
    color: #ffd700;
}

.rating-number {
    color: #00d4ff;
    font-weight: bold;
}

.play-count {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    color: #ccc;
    font-size: 0.8rem;
}

.post-excerpt {
    color: #ccc;
    font-size: 0.9rem;
    line-height: 1.4;
}

.post-meta {
    margin-top: 0.5rem;
    color: #888;
    font-size: 0.8rem;
}

.pagination {
    text-align: center;
    margin: 2rem 0;
}

.pagination .nav-links {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.pagination a,
.pagination span {
    padding: 0.5rem 1rem;
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
    text-decoration: none;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: #00d4ff;
    color: #000;
}

.pagination .current {
    background: #00d4ff;
    color: #000;
}

.no-content {
    text-align: center;
    padding: 3rem 2rem;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
    color: #fff;
}

.no-content h2 {
    color: #00d4ff;
    margin-bottom: 1rem;
}

@media (max-width: 768px) {
    .games-grid {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: 1rem;
    }
    
    .game-card-content {
        padding: 0.8rem;
    }
    
    .game-card h3 {
        font-size: 0.9rem;
    }
}
</style>
