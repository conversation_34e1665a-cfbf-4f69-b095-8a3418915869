<?php
/**
 * D27 Gaming Theme WebP Image Converter
 * Automatically converts uploaded images to WebP format
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class D27_WebP_Converter {
    
    public function __construct() {
        // Hook into WordPress upload process
        add_filter('wp_handle_upload', array($this, 'convert_to_webp'), 10, 2);
        add_filter('wp_generate_attachment_metadata', array($this, 'convert_thumbnails_to_webp'), 10, 2);
        
        // Serve WebP images when available
        add_filter('wp_get_attachment_image_src', array($this, 'serve_webp_if_available'), 10, 4);
        add_filter('wp_get_attachment_url', array($this, 'serve_webp_url_if_available'), 10, 2);
        
        // Add WebP support to allowed mime types
        add_filter('upload_mimes', array($this, 'add_webp_mime_type'));
        
        // Clean up old images when attachment is deleted
        add_action('delete_attachment', array($this, 'cleanup_webp_files'));
    }
    
    /**
     * Check if WebP conversion is supported
     */
    public function is_webp_supported() {
        return function_exists('imagewebp') && function_exists('imagecreatefromjpeg') && function_exists('imagecreatefrompng');
    }
    
    /**
     * Convert uploaded image to WebP
     */
    public function convert_to_webp($upload, $context) {
        if (!$this->is_webp_supported()) {
            return $upload;
        }
        
        $file_path = $upload['file'];
        $file_type = $upload['type'];
        
        // Only convert JPEG and PNG images
        if (!in_array($file_type, array('image/jpeg', 'image/png'))) {
            return $upload;
        }
        
        $webp_path = $this->create_webp_version($file_path, $file_type);
        
        if ($webp_path) {
            // Store WebP path in upload data for reference
            $upload['webp_file'] = $webp_path;
        }
        
        return $upload;
    }
    
    /**
     * Convert image thumbnails to WebP
     */
    public function convert_thumbnails_to_webp($metadata, $attachment_id) {
        if (!$this->is_webp_supported()) {
            return $metadata;
        }
        
        $upload_dir = wp_upload_dir();
        $file_path = get_attached_file($attachment_id);
        
        if (!$file_path || !file_exists($file_path)) {
            return $metadata;
        }
        
        $file_type = get_post_mime_type($attachment_id);
        
        // Only convert JPEG and PNG images
        if (!in_array($file_type, array('image/jpeg', 'image/png'))) {
            return $metadata;
        }
        
        // Convert main image
        $this->create_webp_version($file_path, $file_type);
        
        // Convert thumbnails
        if (isset($metadata['sizes']) && is_array($metadata['sizes'])) {
            $file_dir = dirname($file_path);
            
            foreach ($metadata['sizes'] as $size => $size_data) {
                $thumbnail_path = $file_dir . '/' . $size_data['file'];
                if (file_exists($thumbnail_path)) {
                    $this->create_webp_version($thumbnail_path, $file_type);
                }
            }
        }
        
        return $metadata;
    }
    
    /**
     * Create WebP version of an image
     */
    public function create_webp_version($file_path, $file_type) {
        if (!file_exists($file_path)) {
            return false;
        }
        
        $webp_path = preg_replace('/\.(jpe?g|png)$/i', '.webp', $file_path);
        
        // Skip if WebP already exists and is newer
        if (file_exists($webp_path) && filemtime($webp_path) >= filemtime($file_path)) {
            return $webp_path;
        }
        
        $image = null;
        
        // Create image resource based on type
        switch ($file_type) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg($file_path);
                break;
            case 'image/png':
                $image = imagecreatefrompng($file_path);
                // Preserve transparency for PNG
                imagealphablending($image, false);
                imagesavealpha($image, true);
                break;
        }
        
        if (!$image) {
            return false;
        }
        
        // Convert to WebP with high quality
        $success = imagewebp($image, $webp_path, 85);
        imagedestroy($image);
        
        if ($success) {
            // Set same permissions as original file
            $perms = fileperms($file_path);
            if ($perms !== false) {
                chmod($webp_path, $perms);
            }
            return $webp_path;
        }
        
        return false;
    }
    
    /**
     * Serve WebP image if available and browser supports it
     */
    public function serve_webp_if_available($image, $attachment_id, $size, $icon) {
        // Temporarily disable WebP serving to fix image issues
        return $image;

        if (!$this->browser_supports_webp()) {
            return $image;
        }

        if (!$image || !isset($image[0])) {
            return $image;
        }

        $original_url = $image[0];

        // Only process JPEG and PNG images
        if (!preg_match('/\.(jpe?g|png)$/i', $original_url)) {
            return $image;
        }

        $webp_url = preg_replace('/\.(jpe?g|png)$/i', '.webp', $original_url);

        // Check if WebP file exists
        $upload_dir = wp_upload_dir();
        $webp_path = str_replace($upload_dir['baseurl'], $upload_dir['basedir'], $webp_url);

        if (file_exists($webp_path) && filesize($webp_path) > 0) {
            $image[0] = $webp_url;
        }

        return $image;
    }
    
    /**
     * Serve WebP URL if available and browser supports it
     */
    public function serve_webp_url_if_available($url, $attachment_id) {
        // Temporarily disable WebP serving to fix image issues
        return $url;

        if (!$this->browser_supports_webp()) {
            return $url;
        }

        $file_type = get_post_mime_type($attachment_id);

        // Only convert JPEG and PNG URLs
        if (!in_array($file_type, array('image/jpeg', 'image/png'))) {
            return $url;
        }

        $webp_url = preg_replace('/\.(jpe?g|png)$/i', '.webp', $url);

        // Check if WebP file exists
        $upload_dir = wp_upload_dir();
        $webp_path = str_replace($upload_dir['baseurl'], $upload_dir['basedir'], $webp_url);

        if (file_exists($webp_path) && filesize($webp_path) > 0) {
            return $webp_url;
        }

        return $url;
    }
    
    /**
     * Check if browser supports WebP
     */
    private function browser_supports_webp() {
        return isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'image/webp') !== false;
    }
    
    /**
     * Add WebP to allowed mime types
     */
    public function add_webp_mime_type($mimes) {
        $mimes['webp'] = 'image/webp';
        return $mimes;
    }
    
    /**
     * Clean up WebP files when attachment is deleted
     */
    public function cleanup_webp_files($attachment_id) {
        $file_path = get_attached_file($attachment_id);
        
        if (!$file_path) {
            return;
        }
        
        // Delete main WebP file
        $webp_path = preg_replace('/\.(jpe?g|png)$/i', '.webp', $file_path);
        if (file_exists($webp_path)) {
            unlink($webp_path);
        }
        
        // Delete WebP thumbnails
        $metadata = wp_get_attachment_metadata($attachment_id);
        if (isset($metadata['sizes']) && is_array($metadata['sizes'])) {
            $file_dir = dirname($file_path);
            
            foreach ($metadata['sizes'] as $size => $size_data) {
                $thumbnail_path = $file_dir . '/' . $size_data['file'];
                $webp_thumbnail = preg_replace('/\.(jpe?g|png)$/i', '.webp', $thumbnail_path);
                if (file_exists($webp_thumbnail)) {
                    unlink($webp_thumbnail);
                }
            }
        }
    }
}

// Initialize the WebP converter
new D27_WebP_Converter();

/**
 * Bulk convert existing images to WebP
 */
function d27_bulk_convert_to_webp() {
    if (!current_user_can('manage_options')) {
        wp_die('Unauthorized');
    }
    
    $converter = new D27_WebP_Converter();
    
    if (!$converter->is_webp_supported()) {
        wp_die('WebP is not supported on this server');
    }
    
    $args = array(
        'post_type' => 'attachment',
        'post_mime_type' => array('image/jpeg', 'image/png'),
        'post_status' => 'inherit',
        'posts_per_page' => -1,
    );
    
    $attachments = get_posts($args);
    $converted = 0;
    
    foreach ($attachments as $attachment) {
        $file_path = get_attached_file($attachment->ID);
        if ($file_path && file_exists($file_path)) {
            $webp_path = $converter->create_webp_version($file_path, $attachment->post_mime_type);
            if ($webp_path) {
                $converted++;
            }
            
            // Convert thumbnails
            $metadata = wp_get_attachment_metadata($attachment->ID);
            if (isset($metadata['sizes']) && is_array($metadata['sizes'])) {
                $file_dir = dirname($file_path);
                
                foreach ($metadata['sizes'] as $size => $size_data) {
                    $thumbnail_path = $file_dir . '/' . $size_data['file'];
                    if (file_exists($thumbnail_path)) {
                        $converter->create_webp_version($thumbnail_path, $attachment->post_mime_type);
                    }
                }
            }
        }
    }
    
    wp_redirect(admin_url('admin.php?page=d27-webp-converter&converted=' . $converted));
    exit;
}
