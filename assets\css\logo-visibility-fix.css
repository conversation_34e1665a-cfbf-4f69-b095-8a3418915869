/**
 * Special CSS to ensure logo name visibility
 * This file ensures the logo name (site title) is always visible
 * while maintaining the beautiful spacing between sections
 */

/* Core fixes for logo visibility */
.header-left {
    min-width: 220px !important;
    max-width: 280px !important;
    width: auto !important;
}

.header-left .site-title-nav {
    display: inline-block !important;
    visibility: visible !important;
    min-width: 140px !important;
    width: auto !important;
    max-width: 200px !important;
}

/* Condensed menu items spacing */
.nav-item {
    padding-left: 3px !important;
    padding-right: 3px !important;
}

.nav-menu-dynamic {
    justify-content: flex-start !important; /* Left-align menu items */
}

/* Maintain beautiful spacing */
.header-left {
    margin-right: 20px !important; /* Beautiful spacing to menu */
}

.header-right {
    margin-left: 20px !important; /* Beautiful spacing from menu */
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    .header-left {
        min-width: 200px !important;
        max-width: 240px !important;
    }
    
    .header-left .site-title-nav {
        min-width: 120px !important;
        max-width: 180px !important;
    }
}

@media (max-width: 768px) {
    .header-left {
        min-width: 160px !important;
        max-width: 220px !important;
    }
    
    .header-left .site-title-nav {
        min-width: 100px !important;
        max-width: 150px !important;
    }
    
    .header-left {
        margin-right: 10px !important; /* Slightly reduced but still beautiful */
    }
    
    .header-right {
        margin-left: 10px !important; /* Slightly reduced but still beautiful */
    }
}

/* Extra small screens */
@media (max-width: 480px) {
    .header-left {
        min-width: 140px !important;
        max-width: 160px !important;
    }
    
    .header-left .site-title-nav {
        min-width: 80px !important;
        max-width: 100px !important;
    }
    
    .nav-item {
        padding-left: 2px !important;
        padding-right: 2px !important;
    }
    
    .header-left {
        margin-right: 5px !important; /* Minimum spacing */
    }
    
    .header-right {
        margin-left: 5px !important; /* Minimum spacing */
    }
}