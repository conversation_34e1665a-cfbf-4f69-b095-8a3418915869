/**
 * Critical Header Layout Fixes
 * These styles ensure the header stays on a single row and enforces the 6-item limit
 */

/* Force single row layout */
.main-header {
    flex-wrap: nowrap !important;
    height: 60px !important;
    min-height: 60px !important;
    max-height: 60px !important;
    overflow: visible !important; /* Allow dropdown menu to be visible */
}

/* Ensure header items stay in a row */
.header-left, 
.header-center,
.header-right {
    flex-shrink: 0;
}

/* Set fixed width for logo area - increased width for logo name visibility */
.header-left {
    width: auto !important;
    flex: 0 0 auto !important;
    min-width: 200px !important; /* Ensure minimum width for logo area */
}

/* Ensure center area takes just enough space but not too much */
.header-center {
    flex: 0 1 auto !important; 
    max-width: calc(100% - 350px) !important; /* Adjusted to allow more space for logo */
    overflow: hidden !important;
}

/* Set fixed width for search area */
.header-right {
    width: auto !important;
    flex: 0 0 auto !important;
}

/* Strictly enforce 6-item limit */
.nav-menu-dynamic > li:nth-child(n+7):not(.dropdown-toggle) {
    display: none !important;
}

/* Always show dropdown when there are more than 6 items */
.nav-menu-dynamic:has(> li:nth-child(7)) .dropdown-toggle,
.nav-menu-dynamic:has(> li:nth-child(7)) ~ .dropdown-toggle {
    display: flex !important;
}

/* Ensure items take up just enough space and are closer together */
.nav-item {
    flex: 0 1 auto !important;
    padding: 0 4px !important; /* Reduced padding between menu items */
}

/* Make sure the site title in logo is visible */
.header-left .site-title-nav {
    display: block !important;
    visibility: visible !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    min-width: 100px !important;
    max-width: 150px !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .header-left {
        max-width: 160px !important;
        min-width: 140px !important;
    }
    
    .header-center {
        max-width: calc(100% - 240px) !important;
    }
    
    .header-right {
        max-width: 80px !important;
    }
    
    /* Reduce padding between items on smaller screens */
    .nav-item {
        padding: 0 2px !important;
    }
}

@media (max-width: 480px) {
    .header-left {
        max-width: 120px !important;
    }
    
    .header-center {
        max-width: calc(100% - 200px) !important;
    }
    
    .header-right {
        max-width: 80px !important;
    }
}