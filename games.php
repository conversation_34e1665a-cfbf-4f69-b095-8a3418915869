<?php
/**
 * Template Name: Games Page
 * This template handles all game listing with clean URL parameters
 */

get_header(); ?>

<main class="main-content">
    <div class="container">
        <?php
        // Get URL parameters
        $orderby = isset($_GET['orderby']) ? sanitize_text_field($_GET['orderby']) : '';
        $game_category = isset($_GET['game_category']) ? sanitize_text_field($_GET['game_category']) : '';
        $game_tag = isset($_GET['game_tag']) ? sanitize_text_field($_GET['game_tag']) : '';
        $search = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';
        
        // Set up query arguments
        $args = array(
            'post_type' => 'game',
            'posts_per_page' => 24,
            'post_status' => 'publish',
            'paged' => get_query_var('paged') ? get_query_var('paged') : 1,
        );
        
        // Handle orderby parameter
        $page_title = 'All Games';
        if ($orderby) {
            switch ($orderby) {
                case 'popularity':
                    $args['meta_key'] = '_play_count';
                    $args['orderby'] = 'meta_value_num';
                    $args['order'] = 'DESC';
                    $page_title = 'Most Popular Games';
                    break;
                case 'date':
                    $args['orderby'] = 'date';
                    $args['order'] = 'DESC';
                    $page_title = 'Newest Games';
                    break;
                case 'trending':
                    // Trending = popular in last 30 days
                    $args['meta_key'] = '_play_count';
                    $args['orderby'] = 'meta_value_num';
                    $args['order'] = 'DESC';
                    $args['date_query'] = array(
                        array(
                            'after' => '30 days ago',
                        ),
                    );
                    $page_title = 'Trending Games';
                    break;
                case 'most_played':
                    $args['meta_key'] = '_play_count';
                    $args['orderby'] = 'meta_value_num';
                    $args['order'] = 'DESC';
                    $args['posts_per_page'] = 12;
                    $page_title = 'Most Played Games';
                    break;
                case 'rand':
                    $args['orderby'] = 'rand';
                    $page_title = 'Random Games';
                    break;
            }
        }
        
        // Handle category parameter
        if ($game_category) {
            $args['tax_query'] = array(
                array(
                    'taxonomy' => 'game_category',
                    'field'    => 'slug',
                    'terms'    => $game_category,
                ),
            );
            $category = get_term_by('slug', $game_category, 'game_category');
            $page_title = $category ? $category->name . ' Games' : 'Category Games';
        }
        
        // Handle tag parameter
        if ($game_tag) {
            $args['tax_query'] = array(
                array(
                    'taxonomy' => 'game_tag',
                    'field'    => 'slug',
                    'terms'    => $game_tag,
                ),
            );
            $tag = get_term_by('slug', $game_tag, 'game_tag');
            $page_title = $tag ? $tag->name . ' Games' : 'Tagged Games';
        }
        
        // Handle search parameter
        if ($search) {
            $args['s'] = urldecode($search);
            $page_title = 'Search Results for: ' . esc_html($args['s']);
        }
        
        // Get games
        $games_query = new WP_Query($args);
        ?>
        
        <div class="games-header">
            <h1 class="page-title"><?php echo esc_html($page_title); ?></h1>
            
            <!-- Filter Navigation -->
            <div class="games-filter-nav">
                <a href="/popularity/" class="filter-link <?php echo ($orderby === 'popularity') ? 'active' : ''; ?>">
                    Popular
                </a>
                <a href="/newest/" class="filter-link <?php echo ($orderby === 'date') ? 'active' : ''; ?>">
                    Newest
                </a>
                <a href="/trending/" class="filter-link <?php echo ($orderby === 'trending') ? 'active' : ''; ?>">
                    Trending
                </a>
                <a href="/most-played/" class="filter-link <?php echo ($orderby === 'most_played') ? 'active' : ''; ?>">
                    Most Played
                </a>
                <a href="/random/" class="filter-link <?php echo ($orderby === 'rand') ? 'active' : ''; ?>">
                    Random
                </a>
            </div>
        </div>
        
        <?php if ($games_query->have_posts()) : ?>
            <div class="games-grid">
                <?php while ($games_query->have_posts()) : $games_query->the_post(); ?>
                    <?php get_template_part('template-parts/game-box'); ?>
                <?php endwhile; ?>
            </div>
            
            <!-- Pagination -->
            <div class="pagination-wrapper">
                <?php
                $big = 999999999; // need an unlikely integer
                echo paginate_links(array(
                    'base' => str_replace($big, '%#%', esc_url(get_pagenum_link($big))),
                    'format' => '?paged=%#%',
                    'current' => max(1, get_query_var('paged')),
                    'total' => $games_query->max_num_pages,
                    'show_all' => false,
                    'end_size' => 1,
                    'mid_size' => 2,
                    'prev_next' => true,
                    'prev_text' => '← Previous',
                    'next_text' => 'Next →',
                    'type' => 'list',
                ));
                ?>
            </div>
        <?php else : ?>
            <div class="no-games-found">
                <h2>No games found</h2>
                <p>Sorry, no games match your criteria. Try browsing our <a href="<?php echo home_url('/'); ?>">homepage</a> for popular games.</p>
            </div>
        <?php endif; ?>
        
        <?php wp_reset_postdata(); ?>
    </div>
</main>

<?php get_footer(); ?>
