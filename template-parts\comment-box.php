<?php
/**
 * Template part for displaying comments
 */

// Check if comments are enabled in customizer
if (!get_theme_mod('d27_enable_comments', true)) {
    return;
}

// Check if comments are open or if there are comments
if (!comments_open() && !get_comments_number()) {
    return;
}
?>

<div class="comments-container" id="comments">
    
    <!-- Comments Header -->
    <div class="comments-header">
        <h3 class="comments-title">
            💬 Comments 
            <?php if (get_comments_number()) : ?>
                <span class="comments-count">(<?php echo esc_html(get_comments_number()); ?>)</span>
            <?php endif; ?>
        </h3>
        
        <?php if (get_comments_number()) : ?>
            <div class="comments-sort">
                <label for="comment-sort">Sort by:</label>
                <select id="comment-sort" onchange="sortComments(this.value)">
                    <option value="newest">Newest First</option>
                    <option value="oldest">Oldest First</option>
                    <option value="rating">Highest Rated</option>
                </select>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Comment Form -->
    <?php if (comments_open()) : ?>
        <div class="comment-form-container">
            <?php
            $comment_form_args = array(
                'title_reply' => '✍️ Leave a Comment',
                'title_reply_to' => '↩️ Reply to %s',
                'cancel_reply_link' => '✖️ Cancel Reply',
                'label_submit' => '🚀 Post Comment',
                'comment_field' => '<div class="comment-form-field">
                    <label for="comment">Your Comment *</label>
                    <textarea id="comment" name="comment" cols="45" rows="6" maxlength="65525" required placeholder="Share your thoughts about this game..."></textarea>
                </div>',
                'fields' => array(
                    'author' => '<div class="comment-form-field">
                        <label for="author">Name *</label>
                        <input id="author" name="author" type="text" value="' . esc_attr($commenter['comment_author']) . '" size="30" maxlength="245" required placeholder="Your name" />
                    </div>',
                    'email' => '<div class="comment-form-field">
                        <label for="email">Email *</label>
                        <input id="email" name="email" type="email" value="' . esc_attr($commenter['comment_author_email']) . '" size="30" maxlength="100" required placeholder="<EMAIL>" />
                        <small>Your email will not be published</small>
                    </div>',
                    'url' => '<div class="comment-form-field">
                        <label for="url">Website</label>
                        <input id="url" name="url" type="url" value="' . esc_attr($commenter['comment_author_url']) . '" size="30" maxlength="200" placeholder="https://yourwebsite.com" />
                    </div>',
                ),
                'class_form' => 'comment-form',
                'class_submit' => 'submit-btn',
                'comment_notes_before' => '<p class="comment-notes">Your email address will not be published. Required fields are marked *</p>',
                'comment_notes_after' => '',
            );
            
            comment_form($comment_form_args);
            ?>
        </div>
    <?php endif; ?>
    
    <!-- Comments List -->
    <?php if (have_comments()) : ?>
        <div class="comments-list-container">
            <ol class="comments-list">
                <?php
                wp_list_comments(array(
                    'style' => 'ol',
                    'short_ping' => true,
                    'callback' => 'd27_custom_comment',
                ));
                ?>
            </ol>
            
            <!-- Comments Pagination -->
            <?php if (get_comment_pages_count() > 1 && get_option('page_comments')) : ?>
                <div class="comments-pagination">
                    <?php
                    paginate_comments_links(array(
                        'prev_text' => '← Previous',
                        'next_text' => 'Next →',
                    ));
                    ?>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>
    
    <!-- No Comments Message -->
    <?php if (!have_comments() && comments_open()) : ?>
        <div class="no-comments">
            <div class="no-comments-icon">💭</div>
            <h4>Be the first to comment!</h4>
            <p>Share your thoughts about this game with other players.</p>
        </div>
    <?php endif; ?>
    
    <!-- Comments Closed Message -->
    <?php if (!comments_open() && get_comments_number()) : ?>
        <div class="comments-closed">
            <p>🔒 Comments are closed for this game.</p>
        </div>
    <?php endif; ?>
    
</div>

<style>
/* Comments Container */
.comments-container {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 12px;
    padding: 2rem;
    margin: 2rem 0;
    color: #fff;
}

.comments-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #00d4ff;
    flex-wrap: wrap;
    gap: 1rem;
}

.comments-title {
    color: #00d4ff;
    font-size: 1.5rem;
    margin: 0;
}

.comments-count {
    color: #ccc;
    font-weight: normal;
}

.comments-sort label {
    color: #ccc;
    margin-right: 0.5rem;
}

.comments-sort select {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border: 1px solid #00d4ff;
    border-radius: 5px;
    padding: 0.3rem 0.5rem;
}

/* Comment Form */
.comment-form-container {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.comment-form h3 {
    color: #00d4ff;
    margin-bottom: 1rem;
}

.comment-form-field {
    margin-bottom: 1rem;
}

.comment-form-field label {
    display: block;
    color: #00d4ff;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.comment-form-field input,
.comment-form-field textarea {
    width: 100%;
    padding: 0.8rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(0, 212, 255, 0.5);
    border-radius: 6px;
    color: #fff;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.comment-form-field input:focus,
.comment-form-field textarea:focus {
    outline: none;
    border-color: #00d4ff;
    box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2);
}

.comment-form-field input::placeholder,
.comment-form-field textarea::placeholder {
    color: #888;
}

.comment-form-field small {
    color: #888;
    font-size: 0.8rem;
    display: block;
    margin-top: 0.3rem;
}

.comment-notes {
    color: #ccc;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.submit-btn {
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    color: #fff;
    border: none;
    padding: 0.8rem 2rem;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: bold;
    transition: all 0.3s ease;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
}

/* Comments List */
.comments-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.comment {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border-left: 4px solid #00d4ff;
}

.comment-author {
    display: flex;
    align-items: center;
    margin-bottom: 0.8rem;
}

.comment-author-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 1rem;
    border: 2px solid #00d4ff;
}

.comment-author-name {
    color: #00d4ff;
    font-weight: bold;
    margin-right: 1rem;
}

.comment-meta {
    color: #888;
    font-size: 0.9rem;
}

.comment-content {
    margin: 1rem 0;
    line-height: 1.6;
    color: #fff;
}

.comment-content p {
    margin-bottom: 1rem;
}

.comment-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin-top: 1rem;
}

.comment-reply-link {
    color: #00d4ff;
    text-decoration: none;
    font-size: 0.9rem;
    padding: 0.3rem 0.8rem;
    border: 1px solid #00d4ff;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.comment-reply-link:hover {
    background: #00d4ff;
    color: #000;
}

.comment-rating {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.rating-btn {
    background: none;
    border: 1px solid #ccc;
    color: #ccc;
    padding: 0.2rem 0.5rem;
    border-radius: 3px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.rating-btn:hover,
.rating-btn.active {
    border-color: #00d4ff;
    color: #00d4ff;
}

/* Nested Comments */
.children {
    list-style: none;
    padding-left: 2rem;
    margin-top: 1rem;
}

.children .comment {
    background: rgba(255, 255, 255, 0.03);
    border-left-color: #888;
}

/* Comments Pagination */
.comments-pagination {
    text-align: center;
    margin-top: 2rem;
}

.comments-pagination .nav-links {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}

.comments-pagination a,
.comments-pagination span {
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    text-decoration: none;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.comments-pagination a:hover {
    background: #00d4ff;
    color: #000;
}

.comments-pagination .current {
    background: #00d4ff;
    color: #000;
}

/* No Comments */
.no-comments {
    text-align: center;
    padding: 3rem 2rem;
    color: #ccc;
}

.no-comments-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.7;
}

.no-comments h4 {
    color: #00d4ff;
    margin-bottom: 0.5rem;
}

/* Comments Closed */
.comments-closed {
    text-align: center;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    color: #888;
}

/* Responsive Design */
@media (max-width: 768px) {
    .comments-container {
        padding: 1.5rem;
    }
    
    .comments-header {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }
    
    .comment-form-container {
        padding: 1rem;
    }
    
    .children {
        padding-left: 1rem;
    }
    
    .comment-actions {
        flex-direction: column;
        align-items: stretch;
    }
}
</style>

<script>
function sortComments(sortBy) {
    // This would typically be handled by AJAX
    // For now, we'll just reload the page with the sort parameter
    const url = new URL(window.location);
    url.searchParams.set('comment_sort', sortBy);
    window.location.href = url.toString();
}

// Comment rating functionality
function rateComment(commentId, rating) {
    // AJAX call to rate comment
    fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            action: 'd27_rate_comment',
            comment_id: commentId,
            rating: rating,
            nonce: '<?php echo wp_create_nonce('d27_comment_rating'); ?>'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update the rating display
            const ratingBtns = document.querySelectorAll(`[data-comment-id="${commentId}"] .rating-btn`);
            ratingBtns.forEach(btn => btn.classList.remove('active'));
            document.querySelector(`[data-comment-id="${commentId}"] .rating-btn[data-rating="${rating}"]`).classList.add('active');
        }
    });
}
</script>

<?php
// Custom comment callback function
if (!function_exists('d27_custom_comment')) {
    function d27_custom_comment($comment, $args, $depth) {
        $GLOBALS['comment'] = $comment;
        ?>
        <li <?php comment_class(); ?> id="comment-<?php comment_ID(); ?>" data-comment-id="<?php comment_ID(); ?>">
            <div class="comment">
                <div class="comment-author">
                    <?php echo get_avatar($comment, 40, '', '', array('class' => 'comment-author-avatar')); ?>
                    <span class="comment-author-name"><?php comment_author(); ?></span>
                    <div class="comment-meta">
                        <time datetime="<?php comment_time('c'); ?>">
                            <?php comment_time('F j, Y \a\t g:i a'); ?>
                        </time>
                    </div>
                </div>
                
                <div class="comment-content">
                    <?php comment_text(); ?>
                </div>
                
                <div class="comment-actions">
                    <?php
                    comment_reply_link(array_merge($args, array(
                        'depth' => $depth,
                        'max_depth' => $args['max_depth']
                    )));
                    ?>
                    
                    <div class="comment-rating">
                        <button class="rating-btn" data-rating="up" onclick="rateComment(<?php comment_ID(); ?>, 'up')">👍</button>
                        <button class="rating-btn" data-rating="down" onclick="rateComment(<?php comment_ID(); ?>, 'down')">👎</button>
                    </div>
                </div>
            </div>
        <?php
    }
}
?>
