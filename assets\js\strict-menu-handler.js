/**
 * Strict Menu Handler - Forces 6-item limit
 * This script ensures the menu always shows exactly 6 items maximum and moves the rest to a dropdown
 */

document.addEventListener('DOMContentLoaded', function() {
    enforceStrictMenuLimit();
});

window.addEventListener('load', function() {
    enforceStrictMenuLimit();
    // Run again after a slight delay to ensure DOM is fully ready
    setTimeout(enforceStrictMenuLimit, 200);
});

// Run on resize to maintain proper layout
window.addEventListener('resize', function() {
    enforceStrictMenuLimit();
});

function enforceStrictMenuLimit() {
    console.log('🔒 Enforcing strict 6-item menu limit');
    
    // Find the menu
    const menu = document.querySelector('.nav-menu-dynamic') || document.getElementById('primary-menu');
    if (!menu) {
        console.error('Menu not found');
        return;
    }
    
    // Optimize spacing for logo visibility
    optimizeMenuSpacing();
    
    // Get all menu items (excluding the dropdown toggle)
    const items = Array.from(menu.querySelectorAll('li:not(.dropdown-toggle)'));
    
    // Find or create the dropdown toggle
    let dropdownToggle = menu.querySelector('.dropdown-toggle');
    let dropdownContent;
    
// Check if our responsive dropdown is being used
    const hasResponsiveDropdown = typeof toggleResponsiveDropdown !== 'undefined';
    
    // Only create dropdown elements if not using responsive dropdown
    if (!hasResponsiveDropdown && (!dropdownToggle || !dropdownContent)) {
        console.log('⚠️ Dropdown elements missing, checking if we should create them...');

        // Create dropdown toggle if missing AND we're not using responsive dropdown
        if (!dropdownToggle && !document.querySelector('[onclick*="toggleResponsiveDropdown"]')) {
            console.log('Creating fallback dropdown toggle (not the preferred method)');
            const newDropdownToggle = document.createElement('li');
            newDropdownToggle.className = 'nav-item dropdown-toggle';
            newDropdownToggle.id = 'dropdownToggle';
            newDropdownToggle.style.display = 'none';
            newDropdownToggle.innerHTML = '<button onclick="toggleDropdown()">⋯ More</button><ul class="dropdown-content" id="dropdownContent"></ul>';
            menu.appendChild(newDropdownToggle);
            console.log('✅ Created fallback dropdown toggle');
        }
    }
        console.log('Creating dropdown toggle');
        dropdownToggle = document.createElement('li');
        dropdownToggle.className = 'nav-item dropdown-toggle';
        dropdownToggle.id = 'dropdownToggle';
        
        const button = document.createElement('button');
        button.textContent = '⋯ More';
        button.onclick = function() { toggleDropdown(); };
        
        dropdownContent = document.createElement('ul');
        dropdownContent.className = 'dropdown-content';
        dropdownContent.id = 'dropdownContent';
        
        dropdownToggle.appendChild(button);
        dropdownToggle.appendChild(dropdownContent);
        menu.appendChild(dropdownToggle);
    } else {
        dropdownContent = dropdownToggle.querySelector('.dropdown-content') || document.getElementById('dropdownContent');
    }
    
    // Clear dropdown content
    if (dropdownContent) {
        dropdownContent.innerHTML = '';
    }// STRICTLY enforce 6-item limit
    const MAX_VISIBLE_ITEMS = 6;
    const totalItems = items.length;
    
    console.log(`Found ${totalItems} menu items, limiting to ${MAX_VISIBLE_ITEMS}`);
    
    // Show/hide dropdown toggle based on number of items
    if (totalItems > MAX_VISIBLE_ITEMS) {
        // Show dropdown toggle with flex display
        dropdownToggle.style.display = 'flex';
        
        // Make sure our inline dropdown element exists (for new approach)
        const inlineDropdown = document.getElementById('inlineDropdown');
        
        // Process all items
        items.forEach((item, index) => {
            if (index < MAX_VISIBLE_ITEMS) {
                // Show the first 6 items
                item.style.display = 'flex';
            } else {
                // Hide items beyond the limit
                item.style.display = 'none';
                
                // Add to dropdown content
                if (dropdownContent) {
                    const link = item.querySelector('a');
                    
                    if (link) {
                        // Create dropdown item for the inline approach
                        const dropdownItem = document.createElement('li');
                        
                        // Style the list item directly
                        dropdownItem.style.margin = '0';
                        dropdownItem.style.padding = '0';
                        dropdownItem.style.borderBottom = '1px solid rgba(0, 212, 255, 0.2)';
                        
                        // Create a simple, directly styled link
                        const newLink = document.createElement('a');
                        newLink.href = link.href;
                        newLink.textContent = link.textContent;
                        
                        // Apply direct inline styles to ensure visibility
                        newLink.style.display = 'block';
                        newLink.style.padding = '10px 15px';
                        newLink.style.color = 'white';
                        newLink.style.textDecoration = 'none';
                        newLink.style.whiteSpace = 'nowrap';
                        newLink.style.overflow = 'hidden';
                        newLink.style.textOverflow = 'ellipsis';
                        
                        // Add hover effect
                        newLink.onmouseover = function() {
                            this.style.backgroundColor = 'rgba(0, 212, 255, 0.2)';
                            this.style.color = '#00d4ff';
                        };
                        newLink.onmouseout = function() {
                            this.style.backgroundColor = 'transparent';
                            this.style.color = 'white';
                        };
                        
                        // Append link to the list item
                        dropdownItem.appendChild(newLink);
                        dropdownContent.appendChild(dropdownItem);
                        
                        console.log(`Added item to dropdown: ${link.textContent}`);
                    }
                }
            }
        });
        
        console.log(`✅ Menu limited to ${MAX_VISIBLE_ITEMS} items with ${totalItems - MAX_VISIBLE_ITEMS} in dropdown`);
    } else {
        // No need for dropdown
        dropdownToggle.style.display = 'none';
        console.log('✅ All items fit within limit, no dropdown needed');
    }
}

// Toggle dropdown visibility
function toggleDropdown() {
    console.log('Toggle dropdown called');
    const dropdownContent = document.getElementById('dropdownContent');
    if (dropdownContent) {
        console.log('Current dropdown state:', dropdownContent.classList.contains('show') ? 'shown' : 'hidden');
        dropdownContent.classList.toggle('show');
        // Force style changes
        if (dropdownContent.classList.contains('show')) {
            dropdownContent.style.display = 'block';
            dropdownContent.style.visibility = 'visible';
            dropdownContent.style.opacity = '1';
            console.log('Dropdown shown');
        } else {
            dropdownContent.style.display = 'none';
            console.log('Dropdown hidden');
        }
    } else {
        console.error('Dropdown content element not found');
    }
}

// Close dropdown when clicking outside
document.addEventListener('click', function(event) {
    const dropdown = document.getElementById('dropdownContent');
    const dropdownToggle = document.getElementById('dropdownToggle');
    
    if (dropdown && dropdownToggle && !dropdownToggle.contains(event.target)) {
        dropdown.classList.remove('show');
        dropdown.style.display = 'none';
    }
});

// Make toggleDropdown globally available
window.toggleDropdown = toggleDropdown;

// Add direct click handler to dropdown toggle button
document.addEventListener('DOMContentLoaded', function() {
    const toggleButton = document.querySelector('.dropdown-toggle button');
    if (toggleButton) {
        console.log('Adding click event to dropdown toggle button');
        toggleButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            toggleDropdown();
        });
    }
});

/**
 * Optimize menu item spacing based on available width to ensure logo visibility
 * This function adjusts the spacing between menu items to make sure the logo is visible
 */
function optimizeMenuSpacing() {
    // Get header elements
    const header = document.querySelector('.main-header');
    const headerLeft = document.querySelector('.header-left');
    const siteTitle = document.querySelector('.site-title-nav');
    const menuItems = document.querySelectorAll('.nav-item:not(.dropdown-toggle)');
    
    if (!header || !headerLeft || !siteTitle || !menuItems.length) return;
    
    // Check if site title is visible or truncated
    const siteTitleComputedWidth = parseInt(window.getComputedStyle(siteTitle).width);
    const siteTitleScrollWidth = siteTitle.scrollWidth;
    
    // If site title is truncated, reduce spacing between menu items slightly
    if (siteTitleScrollWidth > siteTitleComputedWidth) {
        console.log('Site title is truncated, optimizing menu spacing');
        
        // Calculate total available width
        const headerWidth = header.offsetWidth;
        const availableWidth = headerWidth * 0.5; // Use about 50% for menu
        
        // Calculate average item width for 6 items + some spacing
        const avgItemWidth = availableWidth / 6;
        
        // Apply slight padding reduction if needed
        menuItems.forEach(item => {
            if (item.offsetWidth > avgItemWidth * 1.2) {
                item.style.padding = '0 2px'; 
            } else if (item.offsetWidth > avgItemWidth * 1.1) {
                item.style.padding = '0 3px';
            } else {
                item.style.padding = '0 4px';
            }
        });
        
        // Increase logo area width if needed
        if (siteTitleComputedWidth < siteTitleScrollWidth * 0.7) {
            headerLeft.style.minWidth = (headerLeft.offsetWidth + 20) + 'px';
        }
    }
}