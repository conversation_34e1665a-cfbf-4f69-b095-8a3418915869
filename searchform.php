<?php
/**
 * Custom search form template
 */
?>

<form role="search" method="get" class="search-form" action="<?php echo esc_url(home_url('/')); ?>">
    <label class="screen-reader-text" for="search-field"><?php _e('Search for:', 'd27-gaming'); ?></label>
    <input 
        type="search" 
        id="search-field" 
        class="search-field" 
        placeholder="<?php esc_attr_e('Search...', 'd27-gaming'); ?>" 
        value="<?php echo get_search_query(); ?>" 
        name="s" 
        autocomplete="off"
        aria-label="<?php esc_attr_e('Search games', 'd27-gaming'); ?>"
    />
    <button type="submit" class="search-submit" aria-label="<?php esc_attr_e('Submit search', 'd27-gaming'); ?>">
        <span class="search-icon">🔍</span>
        <span class="search-text"><?php _e('Search', 'd27-gaming'); ?></span>
    </button>
</form>

<style>
.search-form {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 300px;
}

.search-field {
    flex: 1;
    width: 100%;
    padding: 0.6rem 3rem 0.6rem 1rem;
    border: 1px solid rgba(0, 212, 255, 0.5);
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    min-width: 0; /* Allow shrinking below content width */
}

.search-field:focus {
    outline: none;
    border-color: #00d4ff;
    box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2);
    background: rgba(255, 255, 255, 0.15);
}

.search-field::placeholder {
    color: #ccc;
}

.search-submit {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: #00d4ff;
    color: #000;
    border: none;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    cursor: pointer;
    font-weight: bold;
    font-size: 0.8rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.search-submit:hover {
    background: #0099cc;
    transform: translateY(-50%) scale(1.05);
}

.search-submit:active {
    transform: translateY(-50%) scale(0.95);
}

.search-icon {
    font-size: 0.9rem;
}

.search-text {
    display: none;
}

.screen-reader-text {
    position: absolute !important;
    clip: rect(1px, 1px, 1px, 1px);
    width: 1px !important;
    height: 1px !important;
    overflow: hidden;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .search-form {
        max-width: 200px;
    }
    
    .search-field {
        padding: 0.5rem 2.2rem 0.5rem 0.8rem;
        font-size: 0.8rem;
    }
    
    .search-submit {
        padding: 0.3rem 0.5rem;
        font-size: 0.7rem;
    }
    
    .search-icon {
        font-size: 0.8rem;
    }
    
    .search-text {
        display: none; /* Hide text on medium screens */
    }
}

@media (max-width: 480px) {
    .search-form {
        max-width: 120px;
    }
    
    .search-field {
        padding: 0.4rem 1.8rem 0.4rem 0.5rem;
        font-size: 0.75rem;
        min-width: 0; /* Allow shrinking below content width */
    }
    
    .search-submit {
        padding: 0.2rem 0.4rem;
        right: 2px;
    }
    
    .search-text {
        display: none;
    }
}

/* Enhanced search form for larger screens */
@media (min-width: 1024px) {
    .search-form {
        max-width: 350px;
    }
    
    .search-field {
        padding: 0.7rem 4rem 0.7rem 1.2rem;
        font-size: 1rem;
    }
    
    .search-submit {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
        right: 6px;
    }
    
    .search-text {
        display: inline;
    }
}

/* Focus and accessibility improvements */
.search-form:focus-within .search-field {
    border-color: #00d4ff;
    box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2);
}

.search-submit:focus {
    outline: 2px solid #00d4ff;
    outline-offset: 2px;
}

/* Animation for search icon */
@keyframes search-pulse {
    0% { transform: translateY(-50%) scale(1); }
    50% { transform: translateY(-50%) scale(1.1); }
    100% { transform: translateY(-50%) scale(1); }
}

.search-form.searching .search-submit {
    animation: search-pulse 1s infinite;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .search-field {
        background: rgba(255, 255, 255, 0.05);
        border-color: rgba(0, 212, 255, 0.3);
    }
    
    .search-field:focus {
        background: rgba(255, 255, 255, 0.1);
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .search-field {
        border-width: 2px;
        background: rgba(0, 0, 0, 0.8);
    }
    
    .search-submit {
        border: 2px solid #fff;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .search-field,
    .search-submit {
        transition: none;
    }
    
    .search-submit:hover {
        transform: translateY(-50%);
    }
    
    .search-form.searching .search-submit {
        animation: none;
    }
}
</style>

<script>
// Enhanced search functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchForm = document.querySelector('.search-form');
    const searchField = document.querySelector('.search-field');
    const searchSubmit = document.querySelector('.search-submit');
    
    if (!searchForm || !searchField || !searchSubmit) return;
    
    // Add searching class during form submission
    searchForm.addEventListener('submit', function() {
        searchForm.classList.add('searching');
    });
    
    // Auto-focus search field when clicking anywhere on the form
    searchForm.addEventListener('click', function(e) {
        if (e.target !== searchField) {
            searchField.focus();
        }
    });
    
    // Clear search field with Escape key
    searchField.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            this.value = '';
            this.blur();
        }
    });
    
    // Prevent form submission if search field is empty
    searchForm.addEventListener('submit', function(e) {
        const query = searchField.value.trim();
        if (!query) {
            e.preventDefault();
            searchField.focus();
            return false;
        }
    });
    
    // Add visual feedback for successful search
    if (window.location.search.includes('s=')) {
        searchForm.classList.add('search-success');
        setTimeout(() => {
            searchForm.classList.remove('search-success');
        }, 2000);
    }
});
</script>