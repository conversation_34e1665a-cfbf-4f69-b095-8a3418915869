</main>

<footer class="site-footer">
    <div class="footer-content">
        <?php
        $footer_description = get_theme_mod('d27_footer_description', 'Play the best Geometry Dash games online for free! Enjoy challenging levels, amazing music, and addictive gameplay.');
        ?>
        
        <div class="footer-description">
            <p><?php echo esc_html($footer_description); ?></p>
        </div>
        
        <div class="footer-links">
            <?php
            wp_nav_menu(array(
                'theme_location' => 'footer',
                'menu_id' => 'footer-menu',
                'container' => false,
                'fallback_cb' => 'd27_footer_fallback_menu',
            ));
            ?>
        </div>
        
        <div class="footer-copyright">
            <p>&copy; <?php echo date('Y'); ?> <?php bloginfo('name'); ?>. All rights reserved.</p>
        </div>
    </div>
</footer>

<?php wp_footer(); ?>

<script>
// Inline critical JavaScript for performance
document.addEventListener('DOMContentLoaded', function() {
    // Menu Overflow Handler - Evenly spread with automatic dropdown
    function handleMenuOverflow() {
        const menu = document.querySelector('.nav-menu-dynamic');
        const items = [...menu.querySelectorAll('.nav-item:not(.dropdown-toggle):not(.nav-home):not(.nav-search)')];
        const dropdownToggle = menu.querySelector('.dropdown-toggle');
        const dropdownContent = dropdownToggle.querySelector('.dropdown-content');

        // Reset
        dropdownContent.innerHTML = '';
        dropdownToggle.style.display = 'none';

        // Calculate available space
        const menuWidth = menu.offsetWidth;
        const homeItem = menu.querySelector('.nav-home');
        const searchItem = menu.querySelector('.nav-search');
        const homeWidth = homeItem ? homeItem.offsetWidth : 0;
        const searchWidth = searchItem ? searchItem.offsetWidth : 0;
        const dropdownWidth = 80; // Estimated width for "☰ More" button
        const padding = 20;

        const availableWidth = menuWidth - homeWidth - searchWidth - padding;

        let totalWidth = 0;
        let visibleItems = [];

        // Check which items fit
        for (let item of items) {
            const itemWidth = item.offsetWidth || 120; // Fallback width
            if (totalWidth + itemWidth + dropdownWidth <= availableWidth) {
                totalWidth += itemWidth;
                visibleItems.push(item);
                item.style.display = 'flex';
            } else {
                // Move to dropdown
                dropdownToggle.style.display = 'flex';
                const clonedItem = item.cloneNode(true);
                clonedItem.classList.remove('nav-item');
                dropdownContent.appendChild(clonedItem);
                item.style.display = 'none';
            }
        }

        // If no items were moved to dropdown, hide it
        if (dropdownContent.children.length === 0) {
            dropdownToggle.style.display = 'none';
        }
    }

    // Run on load and resize
    window.addEventListener('load', handleMenuOverflow);
    window.addEventListener('resize', handleMenuOverflow);

    // Mobile menu toggle if needed
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const mainNav = document.querySelector('.main-navigation');

    if (mobileMenuToggle && mainNav) {
        mobileMenuToggle.addEventListener('click', function() {
            mainNav.classList.toggle('active');
        });
    }

    // Smooth scroll for anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                e.preventDefault();
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});
</script>

</body>
</html>

<?php
// Footer fallback menu
function d27_footer_fallback_menu() {
    echo '<a href="' . esc_url(home_url('/')) . '">Home</a>';
    echo '<a href="' . esc_url(home_url('/privacy-policy/')) . '">Privacy Policy</a>';
    echo '<a href="' . esc_url(home_url('/terms-of-service/')) . '">Terms of Service</a>';
    echo '<a href="' . esc_url(home_url('/contact/')) . '">Contact</a>';
}
?>
