/**
 * Inline Dropdown Handler
 * This is a completely independent dropdown implementation using direct DOM manipulation
 * and inline styles to ensure it works regardless of CSS conflicts
 */

// Run on load and DOM ready to ensure it works
document.addEventListener('DOMContentLoaded', initializeInlineDropdown);
window.addEventListener('load', initializeInlineDropdown);

// Also run immediately in case page is already loaded
if (document.readyState === 'complete' || document.readyState === 'interactive') {
    setTimeout(initializeInlineDropdown, 100);
}

function initializeInlineDropdown() {
    console.log('Initializing inline dropdown solution');
    
    // Get main navigation elements
    const menu = document.querySelector('.nav-menu-dynamic') || document.getElementById('primary-menu');
    if (!menu) {
        console.warn('Main menu not found, will retry later');
        setTimeout(initializeInlineDropdown, 500);
        return;
    }
    
    // Find or create dropdown toggle
    let dropdownToggle = document.getElementById('dropdownToggle');
    if (!dropdownToggle) {
        // Create the toggle if it doesn't exist
        dropdownToggle = document.createElement('li');
        dropdownToggle.id = 'dropdownToggle';
        dropdownToggle.className = 'nav-item dropdown-toggle';
        dropdownToggle.style.display = 'none';
        dropdownToggle.style.position = 'relative';
        
        // Create the button
        const button = document.createElement('button');
        button.textContent = '⋯ More';
        button.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            toggleInlineDropdown();
            return false;
        };
        
        // Style the button
        button.style.backgroundColor = 'rgba(0, 212, 255, 0.5)';
        button.style.color = 'white';
        button.style.border = '1px solid rgba(0, 212, 255, 0.7)';
        button.style.padding = '5px 10px';
        button.style.borderRadius = '5px';
        button.style.cursor = 'pointer';
        button.style.fontWeight = 'bold';
        button.style.position = 'relative';
        button.style.zIndex = '999999';
        
        // Create inline dropdown div
        const inlineDropdown = document.createElement('div');
        inlineDropdown.id = 'inlineDropdown';
        inlineDropdown.style.display = 'none';
        inlineDropdown.style.position = 'fixed'; // Changed from absolute to fixed
        inlineDropdown.style.top = '45px'; // Positioned right below header
        inlineDropdown.style.right = '20px'; // Right aligned
        inlineDropdown.style.width = '200px';
        inlineDropdown.style.backgroundColor = 'rgba(0, 0, 0, 0.95)';
        inlineDropdown.style.border = '2px solid #00d4ff';
        inlineDropdown.style.borderRadius = '8px';
        inlineDropdown.style.zIndex = '9999999'; // Very high z-index
        inlineDropdown.style.boxShadow = '0 0 20px #00d4ff';
        inlineDropdown.style.padding = '5px';
        
        // Create dropdown content list
        const dropdownList = document.createElement('ul');
        dropdownList.id = 'dropdownContent';
        dropdownList.style.listStyle = 'none';
        dropdownList.style.margin = '0';
        dropdownList.style.padding = '0';
        
        // Append elements
        inlineDropdown.appendChild(dropdownList);
        dropdownToggle.appendChild(button);
        dropdownToggle.appendChild(inlineDropdown);
        
        // Add to menu
        menu.appendChild(dropdownToggle);
    }
    
    // Get all menu items
    const items = Array.from(menu.querySelectorAll('li:not(#dropdownToggle)'));
    
    // Apply 6-item limit
    const MAX_VISIBLE_ITEMS = 6;
    
    if (items.length > MAX_VISIBLE_ITEMS) {
        // Show dropdown toggle
        dropdownToggle.style.display = 'flex';
        
        // Clear the dropdown content
        const dropdownList = document.getElementById('dropdownContent');
        if (dropdownList) {
            dropdownList.innerHTML = '';
        }
        
        // Process items
        items.forEach((item, index) => {
            if (index < MAX_VISIBLE_ITEMS) {
                // Show first 6 items
                item.style.display = 'flex';
            } else {
                // Hide and move to dropdown
                item.style.display = 'none';
                
                // Get the link
                const link = item.querySelector('a');
                if (link && dropdownList) {
                    // Create new list item
                    const dropdownItem = document.createElement('li');
                    
                    // Style list item
                    dropdownItem.style.margin = '0';
                    dropdownItem.style.padding = '0';
                    dropdownItem.style.borderBottom = '1px solid rgba(0, 212, 255, 0.2)';
                    
                    // Create and style link
                    const newLink = document.createElement('a');
                    newLink.href = link.href;
                    newLink.textContent = link.textContent;
                    
                    // Apply direct styles
                    newLink.style.display = 'block';
                    newLink.style.padding = '10px 15px';
                    newLink.style.color = 'white';
                    newLink.style.textDecoration = 'none';
                    newLink.style.fontSize = '14px';
                    newLink.style.whiteSpace = 'nowrap';
                    newLink.style.overflow = 'hidden';
                    newLink.style.textOverflow = 'ellipsis';
                    
                    // Hover effects
                    newLink.onmouseover = function() {
                        this.style.backgroundColor = 'rgba(0, 212, 255, 0.2)';
                        this.style.color = '#00d4ff';
                    };
                    newLink.onmouseout = function() {
                        this.style.backgroundColor = 'transparent';
                        this.style.color = 'white';
                    };
                    
                    // Append to dropdown
                    dropdownItem.appendChild(newLink);
                    dropdownList.appendChild(dropdownItem);
                }
            }
        });
        
        console.log('Applied 6-item limit with inline dropdown');
    } else {
        // No need for dropdown
        dropdownToggle.style.display = 'none';
    }
}

// Toggle the inline dropdown
function toggleInlineDropdown() {
    const dropdown = document.getElementById('inlineDropdown');
    const toggleButton = document.querySelector('#dropdownToggle button');
    
    if (!dropdown) return;
    
    if (dropdown.style.display === 'none') {
        // Show dropdown
        dropdown.style.display = 'block';
        
        // Get button position to align dropdown
        if (toggleButton) {
            const buttonRect = toggleButton.getBoundingClientRect();
            
            // Position dropdown based on button location
            dropdown.style.position = 'fixed';
            dropdown.style.top = (buttonRect.bottom + 5) + 'px';
            dropdown.style.right = (window.innerWidth - buttonRect.right) + 'px';
        }
        
        // Ensure the dropdown is on top of everything
        dropdown.style.zIndex = '9999999'; 
        console.log('Inline dropdown shown and positioned');
        
        // Add click outside listener
        setTimeout(function() {
            document.addEventListener('click', outsideClickHandler);
        }, 10);
    } else {
        // Hide dropdown
        dropdown.style.display = 'none';
        console.log('Inline dropdown hidden');
        
        // Remove listener
        document.removeEventListener('click', outsideClickHandler);
    }
}

// Handle clicks outside dropdown
function outsideClickHandler(e) {
    const dropdown = document.getElementById('inlineDropdown');
    const toggle = document.querySelector('#dropdownToggle button');
    
    if (dropdown && toggle && 
        !dropdown.contains(e.target) && 
        !toggle.contains(e.target)) {
        
        dropdown.style.display = 'none';
        document.removeEventListener('click', outsideClickHandler);
    }
}

// Make function available globally
window.toggleInlineDropdown = toggleInlineDropdown;