<?php
/**
 * D27 Gaming Theme Performance Optimizations
 * 
 * @package D27_Gaming_Theme
 * @since 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize performance optimizations
 */
function d27_init_performance_optimizations() {
    // Database query optimizations
    add_action('init', 'd27_optimize_database_queries');
    
    // Asset optimization
    add_action('wp_enqueue_scripts', 'd27_optimize_assets', 999);
    
    // Caching optimizations
    add_action('init', 'd27_setup_caching');
    
    // Image optimization
    add_filter('wp_get_attachment_image_attributes', 'd27_optimize_images', 10, 3);
    
    // Remove unnecessary WordPress features
    add_action('init', 'd27_remove_unnecessary_features');
    
    // Optimize admin area
    add_action('admin_init', 'd27_optimize_admin');
    
    // Database cleanup
    add_action('wp_scheduled_delete', 'd27_cleanup_database');
}
add_action('after_setup_theme', 'd27_init_performance_optimizations');

/**
 * Optimize database queries
 */
function d27_optimize_database_queries() {
    // Cache game statistics
    add_action('save_post', 'd27_cache_game_stats', 10, 2);
    
    // Optimize meta queries
    add_filter('posts_clauses', 'd27_optimize_meta_queries', 10, 2);
    
    // Limit post revisions
    if (!defined('WP_POST_REVISIONS')) {
        define('WP_POST_REVISIONS', 3);
    }
    
    // Increase memory limit for complex operations
    if (!defined('WP_MEMORY_LIMIT')) {
        define('WP_MEMORY_LIMIT', '256M');
    }
}

/**
 * Cache game statistics for better performance
 */
function d27_cache_game_stats($post_id, $post) {
    if ($post->post_type !== 'game') {
        return;
    }
    
    // Cache computed values
    $total_votes = (int) get_post_meta($post_id, '_total_votes', true);
    $total_score = (int) get_post_meta($post_id, '_total_score', true);
    $avg_rating = $total_votes > 0 ? round($total_score / $total_votes, 1) : 0;
    
    // Store cached rating
    update_post_meta($post_id, '_cached_rating', $avg_rating);
    update_post_meta($post_id, '_cache_timestamp', time());
}

/**
 * Optimize asset loading
 */
function d27_optimize_assets() {
    // Remove unnecessary WordPress assets
    wp_dequeue_style('wp-block-library');
    wp_dequeue_style('wp-block-library-theme');
    wp_dequeue_style('wc-block-style');
    wp_dequeue_style('global-styles');
    
    // Remove emoji scripts
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');
    
    // Defer non-critical JavaScript
    add_filter('script_loader_tag', 'd27_defer_scripts', 10, 2);
    
    // Preload critical resources
    add_action('wp_head', 'd27_preload_critical_resources', 1);
}

/**
 * Defer non-critical scripts
 */
function d27_defer_scripts($tag, $handle) {
    $defer_scripts = array(
        'd27-consolidated-dropdown',
        'd27-optimized-game-handler',
        'd27-prevent-content-jump'
    );
    
    if (in_array($handle, $defer_scripts)) {
        return str_replace(' src', ' defer src', $tag);
    }
    
    return $tag;
}

/**
 * Preload critical resources
 */
function d27_preload_critical_resources() {
    // Preload critical CSS
    echo '<link rel="preload" href="' . get_stylesheet_uri() . '" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">' . "\n";
    
    // Preload critical fonts (if any)
    // echo '<link rel="preload" href="' . D27_THEME_ASSETS . '/fonts/game-font.woff2" as="font" type="font/woff2" crossorigin>' . "\n";
    
    // DNS prefetch for external resources
    echo '<link rel="dns-prefetch" href="//fonts.googleapis.com">' . "\n";
    echo '<link rel="dns-prefetch" href="//cdnjs.cloudflare.com">' . "\n";
}

/**
 * Setup caching mechanisms
 */
function d27_setup_caching() {
    // Object caching for game data
    add_action('wp_ajax_get_game_stats', 'd27_ajax_get_cached_game_stats');
    add_action('wp_ajax_nopriv_get_game_stats', 'd27_ajax_get_cached_game_stats');
    
    // Fragment caching for game lists
    add_filter('the_content', 'd27_cache_game_content');
    
    // Browser caching headers
    add_action('send_headers', 'd27_set_cache_headers');
}

/**
 * Get cached game statistics
 */
function d27_ajax_get_cached_game_stats() {
    $game_id = intval($_POST['game_id']);
    
    if (!$game_id) {
        wp_die('Invalid game ID');
    }
    
    // Check cache first
    $cache_key = 'game_stats_' . $game_id;
    $cached_stats = wp_cache_get($cache_key);
    
    if ($cached_stats === false) {
        // Generate stats
        $total_votes = (int) get_post_meta($game_id, '_total_votes', true);
        $total_score = (int) get_post_meta($game_id, '_total_score', true);
        $avg_rating = $total_votes > 0 ? round($total_score / $total_votes, 1) : 0;
        $play_count = (int) get_post_meta($game_id, '_play_count', true);
        
        $cached_stats = array(
            'rating' => $avg_rating,
            'votes' => $total_votes,
            'plays' => $play_count
        );
        
        // Cache for 5 minutes
        wp_cache_set($cache_key, $cached_stats, '', 300);
    }
    
    wp_send_json_success($cached_stats);
}

/**
 * Optimize images
 */
function d27_optimize_images($attr, $attachment, $size) {
    // Add loading="lazy" to images
    if (!isset($attr['loading'])) {
        $attr['loading'] = 'lazy';
    }
    
    // Add proper alt text if missing
    if (empty($attr['alt'])) {
        $attr['alt'] = get_the_title($attachment->ID);
    }
    
    return $attr;
}

/**
 * Remove unnecessary WordPress features
 */
function d27_remove_unnecessary_features() {
    // Remove unnecessary head elements
    remove_action('wp_head', 'rsd_link');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'wp_generator');
    remove_action('wp_head', 'start_post_rel_link');
    remove_action('wp_head', 'index_rel_link');
    remove_action('wp_head', 'adjacent_posts_rel_link');
    remove_action('wp_head', 'wp_shortlink_wp_head');
    
    // Remove REST API links if not needed
    remove_action('wp_head', 'rest_output_link_wp_head');
    remove_action('wp_head', 'wp_oembed_add_discovery_links');
    
    // Disable XML-RPC
    add_filter('xmlrpc_enabled', '__return_false');
    
    // Remove query strings from static resources
    add_filter('style_loader_src', 'd27_remove_query_strings', 10, 1);
    add_filter('script_loader_src', 'd27_remove_query_strings', 10, 1);
}

/**
 * Remove query strings from static resources
 */
function d27_remove_query_strings($src) {
    if (strpos($src, 'ver=')) {
        $src = remove_query_arg('ver', $src);
    }
    return $src;
}

/**
 * Optimize admin area
 */
function d27_optimize_admin() {
    // Remove unnecessary admin features
    if (!current_user_can('manage_options')) {
        // Hide admin bar for non-admins
        show_admin_bar(false);
    }
    
    // Limit admin AJAX heartbeat
    add_filter('heartbeat_settings', function($settings) {
        $settings['interval'] = 60; // 60 seconds instead of 15
        return $settings;
    });
}

/**
 * Set browser cache headers
 */
function d27_set_cache_headers() {
    if (!is_admin()) {
        // Set cache headers for static assets
        $expires = 60 * 60 * 24 * 30; // 30 days
        header('Cache-Control: public, max-age=' . $expires);
        header('Expires: ' . gmdate('D, d M Y H:i:s', time() + $expires) . ' GMT');
    }
}

/**
 * Database cleanup
 */
function d27_cleanup_database() {
    global $wpdb;
    
    // Clean up old transients
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_%' AND option_value < UNIX_TIMESTAMP()");
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_%' AND option_name NOT LIKE '_transient_timeout_%' AND option_name NOT IN (SELECT REPLACE(option_name, '_transient_timeout_', '_transient_') FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_%')");
    
    // Clean up old post revisions (keep only latest 3)
    $wpdb->query("DELETE p1 FROM {$wpdb->posts} p1 INNER JOIN {$wpdb->posts} p2 WHERE p1.post_parent = p2.post_parent AND p1.post_type = 'revision' AND p1.ID < p2.ID AND p1.post_parent IN (SELECT ID FROM (SELECT ID FROM {$wpdb->posts} WHERE post_type = 'game') AS temp)");
    
    // Clean up orphaned meta data
    $wpdb->query("DELETE pm FROM {$wpdb->postmeta} pm LEFT JOIN {$wpdb->posts} p ON p.ID = pm.post_id WHERE p.ID IS NULL");
}

/**
 * Optimize meta queries for better performance
 */
function d27_optimize_meta_queries($clauses, $query) {
    global $wpdb;
    
    // Add indexes for commonly queried meta keys
    if ($query->is_main_query() && !is_admin()) {
        // Ensure meta queries use proper indexes
        if (isset($query->query_vars['meta_query'])) {
            foreach ($query->query_vars['meta_query'] as $meta_query) {
                if (isset($meta_query['key'])) {
                    // Add index hint for better performance
                    $clauses['join'] = str_replace(
                        "INNER JOIN {$wpdb->postmeta}",
                        "INNER JOIN {$wpdb->postmeta} USE INDEX (meta_key)",
                        $clauses['join']
                    );
                }
            }
        }
    }
    
    return $clauses;
}

/**
 * Cache game content fragments
 */
function d27_cache_game_content($content) {
    if (is_singular('game')) {
        $cache_key = 'game_content_' . get_the_ID();
        $cached_content = wp_cache_get($cache_key);
        
        if ($cached_content === false) {
            // Process and cache content
            $cached_content = $content;
            wp_cache_set($cache_key, $cached_content, '', 3600); // Cache for 1 hour
        }
        
        return $cached_content;
    }
    
    return $content;
}
