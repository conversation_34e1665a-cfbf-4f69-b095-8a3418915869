/**
 * Ultra Direct Dropdown Styles
 * These styles use !important on everything to override any conflicts
 */

/* Force dropdown display */
#inlineDropdown.force-show {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 9999999 !important;
}

/* Show dropdown toggle when needed */
#dropdownToggle {
    z-index: 999998 !important;
    position: relative !important;
}

/* Style links in dropdown */
#inlineDropdown a {
    display: block !important;
    padding: 10px 15px !important;
    color: white !important;
    text-decoration: none !important;
    background-color: transparent !important;
    border: none !important;
    width: 100% !important;
    text-align: left !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
    font-family: Arial, sans-serif !important;
    box-sizing: border-box !important;
}

#inlineDropdown a:hover {
    background-color: rgba(0, 212, 255, 0.2) !important;
    color: #00d4ff !important;
}

/* More button styles */
#dropdownToggle button {
    position: relative !important;
    z-index: 999999 !important;
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.3) !important;
    outline: none !important;
}

/* Dropdown container */
#inlineDropdown {
    overflow: visible !important;
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.5) !important;
    max-height: 80vh !important;
    overflow-y: auto !important;
}