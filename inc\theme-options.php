<?php
/**
 * D27 Gaming Theme Options
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Register widget areas
 */
function d27_widgets_init() {
    register_sidebar(array(
        'name' => __('Primary Sidebar', 'd27-gaming'),
        'id' => 'sidebar-1',
        'description' => __('Add widgets here to appear in your sidebar.', 'd27-gaming'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget' => '</section>',
        'before_title' => '<h3 class="widget-title">',
        'after_title' => '</h3>',
    ));
    
    register_sidebar(array(
        'name' => __('Footer Widgets', 'd27-gaming'),
        'id' => 'footer-widgets',
        'description' => __('Add widgets here to appear in your footer.', 'd27-gaming'),
        'before_widget' => '<div id="%1$s" class="footer-widget %2$s">',
        'after_widget' => '</div>',
        'before_title' => '<h4 class="footer-widget-title">',
        'after_title' => '</h4>',
    ));
    
    register_sidebar(array(
        'name' => __('Game Sidebar', 'd27-gaming'),
        'id' => 'game-sidebar',
        'description' => __('Widgets for single game pages.', 'd27-gaming'),
        'before_widget' => '<div id="%1$s" class="game-widget %2$s">',
        'after_widget' => '</div>',
        'before_title' => '<h4 class="game-widget-title">',
        'after_title' => '</h4>',
    ));
}
add_action('widgets_init', 'd27_widgets_init');

/**
 * Add theme options page
 */
function d27_add_theme_options_page() {
    add_theme_page(
        __('D27 Gaming Options', 'd27-gaming'),
        __('Theme Options', 'd27-gaming'),
        'manage_options',
        'd27-theme-options',
        'd27_theme_options_page'
    );
}
add_action('admin_menu', 'd27_add_theme_options_page');

/**
 * Theme options page content
 */
function d27_theme_options_page() {
    if (isset($_POST['submit'])) {
        d27_save_theme_options();
        echo '<div class="notice notice-success is-dismissible"><p><strong>' . __('✅ All settings saved successfully!', 'd27-gaming') . '</strong><br>' . __('Your navigation menu and sidebar changes have been applied.', 'd27-gaming') . '</p></div>';
    }
    
    $options = get_option('d27_theme_options', d27_get_default_options());
    ?>
    <div class="wrap">
        <h1><?php _e('🎮 D27 Gaming Theme Options', 'd27-gaming'); ?></h1>

        <div class="nav-tab-wrapper">
            <a href="#general" class="nav-tab nav-tab-active" data-tab="general">General</a>
            <a href="#tools" class="nav-tab" data-tab="tools">Tools</a>
        </div>

        <form method="post" action="">
            <?php wp_nonce_field('d27_theme_options', 'd27_nonce'); ?>

            <!-- General Tab -->
            <div id="general" class="tab-content active">
                <h2>General Settings</h2>
                <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Enable Game Analytics', 'd27-gaming'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="enable_analytics" value="1" <?php checked($options['enable_analytics'], 1); ?> />
                            <?php _e('Track game plays and user interactions', 'd27-gaming'); ?>
                        </label>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row"><?php _e('Game Loading Message', 'd27-gaming'); ?></th>
                    <td>
                        <input type="text" name="loading_message" value="<?php echo esc_attr($options['loading_message']); ?>" class="regular-text" />
                        <p class="description"><?php _e('Message shown while games are loading', 'd27-gaming'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row"><?php _e('Default Game Instructions', 'd27-gaming'); ?></th>
                    <td>
                        <textarea name="default_instructions" rows="4" cols="50"><?php echo esc_textarea($options['default_instructions']); ?></textarea>
                        <p class="description"><?php _e('Default instructions for games without custom instructions', 'd27-gaming'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row"><?php _e('Enable Game Reports', 'd27-gaming'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="enable_reports" value="1" <?php checked($options['enable_reports'], 1); ?> />
                            <?php _e('Allow users to report inappropriate games', 'd27-gaming'); ?>
                        </label>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row"><?php _e('Admin Email for Reports', 'd27-gaming'); ?></th>
                    <td>
                        <input type="email" name="report_email" value="<?php echo esc_attr($options['report_email']); ?>" class="regular-text" />
                        <p class="description"><?php _e('Email address to receive game reports', 'd27-gaming'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row"><?php _e('Cache Game Data', 'd27-gaming'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="enable_cache" value="1" <?php checked($options['enable_cache'], 1); ?> />
                            <?php _e('Cache game data for better performance', 'd27-gaming'); ?>
                        </label>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row"><?php _e('Cache Duration (hours)', 'd27-gaming'); ?></th>
                    <td>
                        <input type="number" name="cache_duration" value="<?php echo esc_attr($options['cache_duration']); ?>" min="1" max="168" />
                        <p class="description"><?php _e('How long to cache game data (1-168 hours)', 'd27-gaming'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row"><?php _e('Enable Lazy Loading', 'd27-gaming'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="enable_lazy_loading" value="1" <?php checked($options['enable_lazy_loading'], 1); ?> />
                            <?php _e('Lazy load game thumbnails for better performance', 'd27-gaming'); ?>
                        </label>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row"><?php _e('Games per Archive Page', 'd27-gaming'); ?></th>
                    <td>
                        <input type="number" name="games_per_page" value="<?php echo esc_attr($options['games_per_page']); ?>" min="6" max="48" step="6" />
                        <p class="description"><?php _e('Number of games to show on archive pages', 'd27-gaming'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row"><?php _e('Enable Game Search', 'd27-gaming'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="enable_game_search" value="1" <?php checked($options['enable_game_search'], 1); ?> />
                            <?php _e('Include games in search results', 'd27-gaming'); ?>
                        </label>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row"><?php _e('Custom CSS', 'd27-gaming'); ?></th>
                    <td>
                        <textarea name="custom_css" rows="10" cols="50" class="large-text code"><?php echo esc_textarea($options['custom_css']); ?></textarea>
                        <p class="description"><?php _e('Add custom CSS to override theme styles', 'd27-gaming'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row"><?php _e('Custom JavaScript', 'd27-gaming'); ?></th>
                    <td>
                        <textarea name="custom_js" rows="10" cols="50" class="large-text code"><?php echo esc_textarea($options['custom_js']); ?></textarea>
                        <p class="description"><?php _e('Add custom JavaScript (without &lt;script&gt; tags)', 'd27-gaming'); ?></p>
                    </td>
                </tr>
            </table>

            </div>

            <!-- Tools Tab -->
            <div id="tools" class="tab-content">
                <h2>🔧 Theme Tools</h2>

                <div class="theme-tools">
                    <p>
                        <a href="<?php echo wp_nonce_url(admin_url('themes.php?page=d27-theme-options&action=clear_cache'), 'd27_clear_cache'); ?>" class="button">
                            <?php _e('Clear Game Cache', 'd27-gaming'); ?>
                        </a>
                        <span class="description"><?php _e('Clear all cached game data', 'd27-gaming'); ?></span>
                    </p>

                    <p>
                        <a href="<?php echo wp_nonce_url(admin_url('themes.php?page=d27-theme-options&action=reset_play_counts'), 'd27_reset_counts'); ?>" class="button" onclick="return confirm('<?php _e('Are you sure? This will reset all play counts to zero.', 'd27-gaming'); ?>')">
                            <?php _e('Reset Play Counts', 'd27-gaming'); ?>
                        </a>
                        <span class="description"><?php _e('Reset all game play counts to zero', 'd27-gaming'); ?></span>
                    </p>

                    <p>
                        <a href="<?php echo wp_nonce_url(admin_url('themes.php?page=d27-theme-options&action=export_settings'), 'd27_export'); ?>" class="button">
                            <?php _e('Export Settings', 'd27-gaming'); ?>
                        </a>
                        <span class="description"><?php _e('Export theme settings as JSON', 'd27-gaming'); ?></span>
                    </p>
                </div>

                <hr>

                <h2><?php _e('📊 Theme Statistics', 'd27-gaming'); ?></h2>

                <?php d27_display_theme_stats(); ?>
            </div>

            <div class="submit-section">
                <?php submit_button('Save All Changes', 'primary', 'submit', false); ?>
                <p class="description">This will save all theme settings and configurations.</p>
            </div>
        </form>
        
        <hr>
        
        <h2><?php _e('🔧 Theme Tools', 'd27-gaming'); ?></h2>
        
        <div class="theme-tools">
            <p>
                <a href="<?php echo wp_nonce_url(admin_url('themes.php?page=d27-theme-options&action=clear_cache'), 'd27_clear_cache'); ?>" class="button">
                    <?php _e('Clear Game Cache', 'd27-gaming'); ?>
                </a>
                <span class="description"><?php _e('Clear all cached game data', 'd27-gaming'); ?></span>
            </p>
            
            <p>
                <a href="<?php echo wp_nonce_url(admin_url('themes.php?page=d27-theme-options&action=reset_play_counts'), 'd27_reset_counts'); ?>" class="button" onclick="return confirm('<?php _e('Are you sure? This will reset all play counts to zero.', 'd27-gaming'); ?>')">
                    <?php _e('Reset Play Counts', 'd27-gaming'); ?>
                </a>
                <span class="description"><?php _e('Reset all game play counts to zero', 'd27-gaming'); ?></span>
            </p>
            
            <p>
                <a href="<?php echo wp_nonce_url(admin_url('themes.php?page=d27-theme-options&action=export_settings'), 'd27_export'); ?>" class="button">
                    <?php _e('Export Settings', 'd27-gaming'); ?>
                </a>
                <span class="description"><?php _e('Export theme settings as JSON', 'd27-gaming'); ?></span>
            </p>
        </div>
        
        <hr>
        
        <h2><?php _e('📊 Theme Statistics', 'd27-gaming'); ?></h2>
        
        <?php d27_display_theme_stats(); ?>
    </div>

    <style>
    /* Tab Navigation */
    .nav-tab-wrapper {
        margin-bottom: 20px;
    }

    .tab-content {
        display: none;
        background: #fff;
        padding: 20px;
        border: 1px solid #ccd0d4;
        border-top: none;
    }

    .tab-content.active {
        display: block;
    }



    /* Loading state */
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }

    /* Submit section */
    .submit-section {
        background: #f9f9f9;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 5px;
        margin-top: 20px;
        text-align: center;
    }

    .submit-section .button {
        font-size: 16px;
        padding: 10px 30px;
        height: auto;
    }

    #changes-indicator {
        margin-bottom: 15px;
        text-align: left;
    }

    #changes-indicator p {
        margin: 0;
    }

    /* Highlight unsaved changes */
    .has-changes {
        border-left: 4px solid #ffb900;
        background: #fff8e5;
    }
    .theme-tools p {
        margin: 1rem 0;
    }
    
    .theme-tools .button {
        margin-right: 1rem;
    }
    
    .theme-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }
    
    .stat-box {
        background: #f1f1f1;
        padding: 1rem;
        border-radius: 5px;
        text-align: center;
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: #0073aa;
    }
    
    .stat-label {
        font-size: 0.9rem;
        color: #666;
    }
    </style>

    <script>
    jQuery(document).ready(function($) {
        // Tab switching
        $('.nav-tab').on('click', function(e) {
            e.preventDefault();
            var tab = $(this).data('tab');

            $('.nav-tab').removeClass('nav-tab-active');
            $(this).addClass('nav-tab-active');

            $('.tab-content').removeClass('active');
            $('#' + tab).addClass('active');
        });












    });
    </script>
    <?php
}

/**
 * Save theme options
 */
function d27_save_theme_options() {
    if (!isset($_POST['d27_nonce']) || !wp_verify_nonce($_POST['d27_nonce'], 'd27_theme_options')) {
        return;
    }

    if (!current_user_can('manage_options')) {
        return;
    }

    $options = array(
        'enable_analytics' => isset($_POST['enable_analytics']) ? 1 : 0,
        'loading_message' => sanitize_text_field($_POST['loading_message']),
        'default_instructions' => sanitize_textarea_field($_POST['default_instructions']),
        'enable_reports' => isset($_POST['enable_reports']) ? 1 : 0,
        'report_email' => sanitize_email($_POST['report_email']),
        'enable_cache' => isset($_POST['enable_cache']) ? 1 : 0,
        'cache_duration' => absint($_POST['cache_duration']),
        'enable_lazy_loading' => isset($_POST['enable_lazy_loading']) ? 1 : 0,
        'games_per_page' => absint($_POST['games_per_page']),
        'enable_game_search' => isset($_POST['enable_game_search']) ? 1 : 0,
        'custom_css' => wp_strip_all_tags($_POST['custom_css']),
        'custom_js' => wp_strip_all_tags($_POST['custom_js']),
    );

    update_option('d27_theme_options', $options);

    // Save navigation items
    if (isset($_POST['nav_items'])) {
        $nav_items = d27_sanitize_nav_items($_POST['nav_items']);
        set_theme_mod('d27_nav_items', $nav_items);

        // Debug: Log what was saved
        error_log('D27 Theme: Saved nav items: ' . $nav_items);
    }

    // Save sidebar items
    if (isset($_POST['sidebar_items'])) {
        $sidebar_items = d27_sanitize_sidebar_items($_POST['sidebar_items']);
        set_theme_mod('d27_sidebar_items', $sidebar_items);

        // Debug: Log what was saved
        error_log('D27 Theme: Saved sidebar items: ' . $sidebar_items);
    } else {
        error_log('D27 Theme: No sidebar_items in POST data');
    }
}

/**
 * Get default theme options
 */
function d27_get_default_options() {
    return array(
        'enable_analytics' => 1,
        'loading_message' => 'Loading awesome game...',
        'default_instructions' => 'Use SPACE or CLICK to jump. Avoid obstacles and reach the end!',
        'enable_reports' => 1,
        'report_email' => get_option('admin_email'),
        'enable_cache' => 1,
        'cache_duration' => 24,
        'enable_lazy_loading' => 1,
        'games_per_page' => 12,
        'enable_game_search' => 1,
        'custom_css' => '',
        'custom_js' => '',
    );
}

/**
 * Display theme statistics
 */
function d27_display_theme_stats() {
    $total_games = wp_count_posts('game')->publish;
    $total_plays = 0;
    $total_comments = 0;
    
    // Calculate total plays
    $games = get_posts(array(
        'post_type' => 'game',
        'posts_per_page' => -1,
        'fields' => 'ids',
    ));
    
    foreach ($games as $game_id) {
        $plays = get_post_meta($game_id, '_play_count', true);
        $total_plays += intval($plays);
        
        $comments = get_comments_number($game_id);
        $total_comments += $comments;
    }
    
    $game_tags = wp_count_terms('game_tag');
    ?>
    <div class="theme-stats">
        <div class="stat-box">
            <div class="stat-number"><?php echo esc_html($total_games); ?></div>
            <div class="stat-label"><?php _e('Total Games', 'd27-gaming'); ?></div>
        </div>
        
        <div class="stat-box">
            <div class="stat-number"><?php echo esc_html(number_format($total_plays)); ?></div>
            <div class="stat-label"><?php _e('Total Plays', 'd27-gaming'); ?></div>
        </div>
        
        <div class="stat-box">
            <div class="stat-number"><?php echo esc_html($total_comments); ?></div>
            <div class="stat-label"><?php _e('Total Comments', 'd27-gaming'); ?></div>
        </div>
        
        <div class="stat-box">
            <div class="stat-number"><?php echo esc_html($game_tags); ?></div>
            <div class="stat-label"><?php _e('Game Categories', 'd27-gaming'); ?></div>
        </div>
    </div>
    <?php
}

/**
 * Handle theme tools actions
 */
function d27_handle_theme_tools() {
    if (!isset($_GET['action']) || !current_user_can('manage_options')) {
        return;
    }
    
    $action = $_GET['action'];
    
    switch ($action) {
        case 'clear_cache':
            if (wp_verify_nonce($_GET['_wpnonce'], 'd27_clear_cache')) {
                wp_cache_flush();
                delete_transient('d27_popular_games');
                delete_transient('d27_latest_games');
                wp_redirect(add_query_arg('cache_cleared', '1', remove_query_arg(array('action', '_wpnonce'))));
                exit;
            }
            break;
            
        case 'reset_play_counts':
            if (wp_verify_nonce($_GET['_wpnonce'], 'd27_reset_counts')) {
                $games = get_posts(array(
                    'post_type' => 'game',
                    'posts_per_page' => -1,
                    'fields' => 'ids',
                ));
                
                foreach ($games as $game_id) {
                    update_post_meta($game_id, '_play_count', 0);
                }
                
                wp_redirect(add_query_arg('counts_reset', '1', remove_query_arg(array('action', '_wpnonce'))));
                exit;
            }
            break;
            
        case 'export_settings':
            if (wp_verify_nonce($_GET['_wpnonce'], 'd27_export')) {
                $settings = array(
                    'theme_options' => get_option('d27_theme_options'),
                    'customizer_settings' => array(),
                );
                
                // Export customizer settings
                $customizer_settings = array(
                    'd27_site_logo',
                    'd27_featured_game',
                    'd27_primary_color',
                    'd27_background_color',
                    'd27_enable_comments',
                    'd27_footer_description',
                );
                
                foreach ($customizer_settings as $setting) {
                    $settings['customizer_settings'][$setting] = get_theme_mod($setting);
                }
                
                header('Content-Type: application/json');
                header('Content-Disposition: attachment; filename="d27-theme-settings.json"');
                echo json_encode($settings, JSON_PRETTY_PRINT);
                exit;
            }
            break;
    }
}
add_action('admin_init', 'd27_handle_theme_tools');

/**
 * Add custom CSS and JS from theme options
 */
function d27_add_custom_css_js() {
    $options = get_option('d27_theme_options', d27_get_default_options());
    
    if (!empty($options['custom_css'])) {
        echo '<style type="text/css">' . $options['custom_css'] . '</style>';
    }
    
    if (!empty($options['custom_js'])) {
        echo '<script type="text/javascript">' . $options['custom_js'] . '</script>';
    }
}
add_action('wp_head', 'd27_add_custom_css_js');

/**
 * Modify games per page based on theme options
 */
function d27_modify_games_per_page($query) {
    if (!is_admin() && $query->is_main_query()) {
        if (is_post_type_archive('game') || is_tax('game_tag')) {
            $options = get_option('d27_theme_options', d27_get_default_options());
            $query->set('posts_per_page', $options['games_per_page']);
        }
    }
}
add_action('pre_get_posts', 'd27_modify_games_per_page');

/**
 * Include games in search if enabled
 */
function d27_include_games_in_search($query) {
    if (!is_admin() && $query->is_main_query() && is_search()) {
        $options = get_option('d27_theme_options', d27_get_default_options());
        if ($options['enable_game_search']) {
            $query->set('post_type', array('post', 'page', 'game'));
        }
    }
}
add_action('pre_get_posts', 'd27_include_games_in_search');

/**
 * Get navigation icon by type
 */
function d27_get_nav_icon($type) {
    $icons = array(
        'home' => '🏠',
        'all_games' => '🎮',
        'popular' => '🔥',
        'latest' => '🆕',
        'categories' => '🏷️',
        'custom' => '🔗',
    );

    return isset($icons[$type]) ? $icons[$type] : '🔗';
}

/**
 * Get sidebar icon by type
 */
function d27_get_sidebar_icon($type) {
    $icons = array(
        'popular_games' => '🔥',
        'game_categories' => '🏷️',
        'latest_games' => '🆕',
        'game_stats' => '📊',
        'search' => '🔍',
        'custom_html' => '📝',
    );

    return isset($icons[$type]) ? $icons[$type] : '📝';
}

/**
 * Get default URL by navigation type
 */
function d27_get_nav_default_url($type) {
    switch ($type) {
        case 'home':
            return home_url('/');
        case 'all_games':
            return get_post_type_archive_link('game');
        case 'popular':
            return add_query_arg('orderby', 'popularity', get_post_type_archive_link('game'));
        case 'latest':
            return add_query_arg('orderby', 'date', get_post_type_archive_link('game'));
        case 'categories':
            return get_post_type_archive_link('game');
        default:
            return home_url('/');
    }
}

/**
 * Get default name by navigation type
 */
function d27_get_nav_default_name($type) {
    $names = array(
        'home' => 'Home',
        'all_games' => 'All Games',
        'popular' => 'Popular Games',
        'latest' => 'Latest Games',
        'categories' => 'Categories',
        'custom' => 'Custom Link',
    );

    return isset($names[$type]) ? $names[$type] : 'Custom Link';
}

/**
 * Get default title by sidebar type
 */
function d27_get_sidebar_default_title($type) {
    $titles = array(
        'popular_games' => '🔥 Most Played Games',
        'game_categories' => '🏷️ Game Categories',
        'latest_games' => '🆕 Latest Games',
        'game_stats' => '📊 Game Statistics',
        'search' => '🔍 Search Games',
        'custom_html' => '📝 Custom Content',
    );

    return isset($titles[$type]) ? $titles[$type] : '📝 Custom Section';
}
?>
