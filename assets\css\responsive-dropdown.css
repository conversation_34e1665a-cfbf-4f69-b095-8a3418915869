/**
 * Responsive Dropdown Styles
 * Optimized for performance and responsiveness
 */

/* Dropdown toggle button styles */
#dropdownToggle button {
    /* Hardware acceleration for better performance */
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
    will-change: transform, background-color;
    transition: background-color 0.1s ease, transform 0.1s ease, box-shadow 0.1s ease;
}

/* Hover state */
#dropdownToggle button:hover {
    background-color: rgba(0, 212, 255, 0.7) !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 212, 255, 0.4) !important;
}

/* Active/pressed state */
#dropdownToggle button:active {
    transform: translateY(1px);
    box-shadow: 0 1px 3px rgba(0, 212, 255, 0.3) !important;
}

/* Make sure dropdown is visible and above everything */
#responsive-dropdown {
    /* Hardware acceleration */
    transform: translateZ(0);
    backface-visibility: hidden;
    will-change: opacity, transform;
    filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.5));
}

/* Smooth hover effects for dropdown links */
#responsive-dropdown a {
    will-change: background-color, padding-left;
    transition: background-color 0.1s ease, color 0.1s ease, padding-left 0.1s ease;
}

/* Clickable area enhancement */
#responsive-dropdown a,
#dropdownToggle button {
    cursor: pointer;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

/* Touch device optimizations */
@media (pointer: coarse) {
    #dropdownToggle button {
        min-height: 34px; /* Larger tap target */
        padding: 6px 12px !important;
    }
    
    #responsive-dropdown a {
        min-height: 44px; /* Standard touch target height */
        line-height: 24px;
        padding-top: 10px !important;
        padding-bottom: 10px !important;
    }
}