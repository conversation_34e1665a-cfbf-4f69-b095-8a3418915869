/**
 * D27 Gaming Theme - Play Handler JavaScript
 * Handles game interactions, fullscreen, and AJAX calls
 */

(function() {
    'use strict';
    
    // Global variables
    let currentGameId = null;
    let isFullscreen = false;
    let gameIframe = null;
    let playButton = null;
    let fullscreenBtn = null;
    
    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        initializeGameHandlers();
        initializeUIHandlers();
        initializeFavorites();
        initializeStarRating();
    });
    
    /**
     * Initialize game-related event handlers
     */
    function initializeGameHandlers() {
        // Find game elements
        gameIframe = document.querySelector('.game-iframe');
        playButton = document.querySelector('.play-button');
        fullscreenBtn = document.querySelector('.fullscreen-btn');

        // Set up iframe load event
        if (gameIframe) {
            gameIframe.addEventListener('load', function() {
                showFullscreenButton();
            });
        }
        
        // Set up fullscreen change events
        document.addEventListener('fullscreenchange', handleFullscreenChange);
        document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
        document.addEventListener('mozfullscreenchange', handleFullscreenChange);
        document.addEventListener('MSFullscreenChange', handleFullscreenChange);
    }
    
    /**
     * Initialize UI-related event handlers
     */
    function initializeUIHandlers() {
        // Controls toggle
        const controlsToggle = document.querySelector('.controls-toggle');
        const controlsPanel = document.querySelector('.controls-panel');
        
        if (controlsToggle && controlsPanel) {
            controlsToggle.addEventListener('click', function() {
                const isVisible = controlsPanel.style.display === 'block';
                controlsPanel.style.display = isVisible ? 'none' : 'block';
            });
            
            // Hide controls panel when clicking outside
            document.addEventListener('click', function(e) {
                if (!controlsToggle.contains(e.target) && !controlsPanel.contains(e.target)) {
                    controlsPanel.style.display = 'none';
                }
            });
        }
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // ESC to exit fullscreen
            if (e.key === 'Escape' && isFullscreen) {
                exitFullscreen();
            }
            
            // F key for fullscreen
            if (e.key === 'f' || e.key === 'F') {
                if (gameIframe && gameIframe.style.display !== 'none') {
                    e.preventDefault();
                    toggleFullscreen();
                }
            }
        });
    }
    
    /**
     * Initialize favorites functionality
     */
    function initializeFavorites() {
        // Load favorites from localStorage
        const favorites = getFavorites();

        // Update favorite buttons
        document.querySelectorAll('.favorite-btn').forEach(btn => {
            const gameId = btn.getAttribute('onclick')?.match(/\d+/)?.[0];
            if (gameId && favorites.includes(gameId)) {
                btn.classList.add('active');
                btn.querySelector('.heart-icon').textContent = '❤️';
            }
        });
    }

    /**
     * Initialize star rating functionality
     */
    function initializeStarRating() {
        const starRatings = document.querySelectorAll('.star-rating');

        starRatings.forEach(rating => {
            const stars = rating.querySelectorAll('.rating-star');
            const gameId = rating.getAttribute('data-game-id');

            if (!gameId) return;

            // Add hover effects
            stars.forEach((star, index) => {
                star.addEventListener('mouseenter', function() {
                    highlightStars(stars, index + 1);
                });

                star.addEventListener('click', function() {
                    const ratingValue = parseInt(star.getAttribute('data-rating'));
                    submitRating(gameId, ratingValue, rating);
                });
            });

            // Reset hover effect when leaving rating container
            rating.addEventListener('mouseleave', function() {
                resetStarHighlight(stars);
            });
        });
    }

    /**
     * Highlight stars up to the given index
     */
    function highlightStars(stars, count) {
        stars.forEach((star, index) => {
            if (index < count) {
                star.classList.add('hover');
                star.textContent = '⭐';
            } else {
                star.classList.remove('hover');
                star.textContent = '☆';
            }
        });
    }

    /**
     * Reset star highlighting to current rating
     */
    function resetStarHighlight(stars) {
        stars.forEach(star => {
            star.classList.remove('hover');
            if (star.classList.contains('filled')) {
                star.textContent = '⭐';
            } else {
                star.textContent = '☆';
            }
        });
    }/**
 * Submit rating via AJAX - Updated to match user's preferred approach
 */
function submitRating(gameId, ratingValue, container) {
    console.log('Submitting rating:', gameId, ratingValue);
    
    if (!d27_ajax) {
        console.error('AJAX configuration missing for rating submission');
        return;
    }
    
    fetch(d27_ajax.ajax_url, {
        method: "POST",
        headers: {
            "Content-Type": "application/x-www-form-urlencoded"
        },
        body: `action=submit_game_rating&game_id=${gameId}&rating=${ratingValue}&nonce=${d27_ajax.nonce}`
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const stars = container.querySelectorAll(".rating-star");
                stars.forEach((star, index) => {
                    star.classList.toggle("filled", index < ratingValue);
                });
                container.querySelector(".rating-result").innerText = `${data.new_average}/5 (${data.total_votes} votes)`;
                showNotification(`You rated ${ratingValue} stars! New average: ${data.new_average}/5`, 'success');
            } else {
                showNotification("You have already voted or there is a server error.", 'error');
            }
        })
        .catch(error => {
            console.error('Error submitting rating:', error);
            showNotification('Error submitting rating. Please try again.', 'error');
        });
    }


    
    /**
     * Play game function - called when play button is clicked
     */
    window.playGame = function(gameId) {
        console.log('Play game called with ID:', gameId);

        if (!gameIframe) {
            console.error('Game iframe not found');
            return;
        }

        currentGameId = gameId;

        // Hide play button
        console.log('About to hide play button...');
        hidePlayButton();

        // Verify the button is hidden
        setTimeout(() => {
            const checkButton = document.querySelector('.play-button');
            if (checkButton) {
                console.log('Play button after hide attempt:', {
                    display: checkButton.style.display,
                    computedDisplay: window.getComputedStyle(checkButton).display,
                    classList: checkButton.classList.toString()
                });
            }
        }, 100);

        // Show iframe
        gameIframe.style.display = 'block';

        // Increment play count via AJAX
        incrementPlayCount(gameId);

        // Show fullscreen button immediately
        showFullscreenButton();

        // Update play count display optimistically
        const playCountDisplay = document.getElementById('play-count-display');
        if (playCountDisplay) {
            const currentCount = parseInt(playCountDisplay.textContent.replace(/,/g, '')) || 0;
            playCountDisplay.textContent = formatNumber(currentCount + 1);
        }
    };
    
    /**
     * Toggle fullscreen mode
     */
    window.toggleFullscreen = function() {
        if (!gameIframe) return;
        
        const gameFrame = gameIframe.closest('.game-frame');
        if (!gameFrame) return;
        
        if (!isFullscreen) {
            enterFullscreen(gameFrame);
        } else {
            exitFullscreen();
        }
    };
    
    /**
     * Enter fullscreen mode
     */
    function enterFullscreen(element) {
        if (element.requestFullscreen) {
            element.requestFullscreen();
        } else if (element.webkitRequestFullscreen) {
            element.webkitRequestFullscreen();
        } else if (element.mozRequestFullScreen) {
            element.mozRequestFullScreen();
        } else if (element.msRequestFullscreen) {
            element.msRequestFullscreen();
        }
    }
    
    /**
     * Exit fullscreen mode
     */
    function exitFullscreen() {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
        } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
        }
    }
    
    /**
     * Handle fullscreen change events
     */
    function handleFullscreenChange() {
        isFullscreen = !!(document.fullscreenElement || 
                         document.webkitFullscreenElement || 
                         document.mozFullScreenElement || 
                         document.msFullscreenElement);
        
        if (fullscreenBtn) {
            const fullscreenIcon = fullscreenBtn.querySelector('span:first-child');
            const fullscreenText = fullscreenBtn.querySelector('span:last-child');
            if (fullscreenIcon) {
                fullscreenIcon.textContent = isFullscreen ? '⛶' : '⛶';
            }
            if (fullscreenText) {
                fullscreenText.textContent = isFullscreen ? 'Exit Fullscreen' : 'Fullscreen';
            }
            fullscreenBtn.title = isFullscreen ? 'Exit Fullscreen' : 'Fullscreen';
        }
    }
    
    /**
     * Toggle favorite status
     */
    window.toggleFavorite = function(gameId) {
        const favoriteBtn = document.querySelector('.favorite-btn[data-game-id="' + gameId + '"]');
        if (!favoriteBtn) return;

        const isFavorited = favoriteBtn.classList.contains('favorited');
        const favoriteCountSpan = favoriteBtn.querySelector('.favorite-count');
        const favoriteTextSpan = favoriteBtn.querySelector('.favorite-text');
        let currentCount = parseInt(favoriteCountSpan.textContent) || 0;

        // Toggle visual state
        if (isFavorited) {
            favoriteBtn.classList.remove('favorited');
            favoriteTextSpan.textContent = 'Favorite';
            currentCount = Math.max(0, currentCount - 1);
            showNotification('Removed from favorites', 'info');
        } else {
            favoriteBtn.classList.add('favorited');
            favoriteTextSpan.textContent = 'Favorited';
            currentCount += 1;
            showNotification('Added to favorites!', 'success');
        }

        // Update counter immediately
        favoriteCountSpan.textContent = currentCount;

        // Update localStorage
        const favorites = getFavorites();
        const gameIdStr = gameId.toString();

        if (isFavorited) {
            const index = favorites.indexOf(gameIdStr);
            if (index > -1) favorites.splice(index, 1);
        } else {
            if (!favorites.includes(gameIdStr)) {
                favorites.push(gameIdStr);
            }
        }
        localStorage.setItem('d27_favorites', JSON.stringify(favorites));

        // Send AJAX request to update favorite count
        if (d27_ajax) {
            fetch(d27_ajax.ajax_url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'toggle_game_favorite',
                    game_id: gameId,
                    nonce: d27_ajax.nonce
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data.count !== undefined) {
                    favoriteCountSpan.textContent = data.data.count;
                }
            })
            .catch(error => console.error('Error updating favorite:', error));
        }
    };
    


    /**
     * Toggle like function
     */
    window.toggleLike = function(gameId) {
        const likeBtn = document.querySelector('.like-btn[data-game-id="' + gameId + '"]');
        if (!likeBtn) return;

        const isLiked = likeBtn.classList.contains('liked');
        const likeCountSpan = likeBtn.querySelector('.like-count');
        const likeTextSpan = likeBtn.querySelector('.like-text');
        let currentCount = parseInt(likeCountSpan.textContent) || 0;

        // Toggle visual state
        if (isLiked) {
            likeBtn.classList.remove('liked');
            likeTextSpan.textContent = 'Like';
            currentCount = Math.max(0, currentCount - 1);
            showNotification('Removed like', 'info');
        } else {
            likeBtn.classList.add('liked');
            likeTextSpan.textContent = 'Liked';
            currentCount += 1;
            showNotification('Liked!', 'success');
        }

        // Update counter immediately
        likeCountSpan.textContent = currentCount;

        // Send AJAX request to update like status
        if (d27_ajax) {
            fetch(d27_ajax.ajax_url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'toggle_game_like',
                    game_id: gameId,
                    nonce: d27_ajax.nonce
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data.count !== undefined) {
                    likeCountSpan.textContent = data.data.count;
                }
            })
            .catch(error => console.error('Error updating like:', error));
        }
    };

    /**
     * Share game function
     */
    window.shareGame = function(gameId) {
        const gameTitle = document.querySelector('.game-title h1')?.textContent || 'Awesome Game';
        const gameUrl = window.location.href;

        // Send AJAX request to update share count (backend tracking only)
        if (d27_ajax) {
            fetch(d27_ajax.ajax_url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'increment_game_share',
                    game_id: gameId,
                    nonce: d27_ajax.nonce
                })
            }).catch(error => console.error('Error updating share count:', error));
        }

        if (navigator.share) {
            // Use native sharing if available
            navigator.share({
                title: gameTitle,
                text: `Check out this awesome game: ${gameTitle}`,
                url: gameUrl
            }).catch(err => console.log('Error sharing:', err));
        } else {
            // Fallback to copying URL
            copyToClipboard(gameUrl);
            showNotification('Game URL copied to clipboard!', 'success');
        }
    };
    
    /**
     * Report game function
     */
    window.reportGame = function(gameId) {
        const reason = prompt('Please tell us why you\'re reporting this game:');
        if (reason && reason.trim()) {
            // Send report via AJAX
            sendReport(gameId, reason.trim());
        }
    };/**
 * Increment play count via AJAX
 */
function incrementPlayCount(gameId) {
    if (!d27_ajax) {
        console.error('AJAX configuration missing');
        return;
    }
    
    console.log('Incrementing play count for game ID:', gameId);
    
    fetch(d27_ajax.ajax_url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            action: 'increment_play_count',
            game_id: gameId,
            nonce: d27_ajax.nonce
        })
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update play count display
                const playCountDisplay = document.getElementById('play-count-display');
                if (playCountDisplay) {
                    playCountDisplay.textContent = formatNumber(data.data.count);
                }
            }
        })
        .catch(error => console.error('Error incrementing play count:', error));
    }
    
    /**
     * Send game report
     */
    function sendReport(gameId, reason) {
        if (!d27_ajax) return;
        
        fetch(d27_ajax.ajax_url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'd27_report_game',
                game_id: gameId,
                reason: reason,
                nonce: d27_ajax.nonce
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Thank you for your report. We\'ll review it shortly.', 'success');
            } else {
                showNotification('Error sending report. Please try again.', 'error');
            }
        })
        .catch(error => {
            console.error('Error sending report:', error);
            showNotification('Error sending report. Please try again.', 'error');
        });
    }
    
    /**
     * Get favorites from localStorage
     */
    function getFavorites() {
        try {
            const favorites = localStorage.getItem('d27_favorites');
            return favorites ? JSON.parse(favorites) : [];
        } catch (e) {
            return [];
        }
    }
    
    /**
     * Copy text to clipboard
     */
    function copyToClipboard(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text);
        } else {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
        }
    }
    
    /**
     * Show notification
     */
    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Style the notification
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: type === 'success' ? '#00d4ff' : type === 'error' ? '#ff4757' : '#333',
            color: type === 'success' ? '#000' : '#fff',
            padding: '1rem 1.5rem',
            borderRadius: '8px',
            zIndex: '10000',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease'
        });
        
        // Add to page
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
    
    /**
     * Format number for display
     */
    function formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toLocaleString();
    }
    
    /**
     * UI Helper Functions
     */
    function hidePlayButton() {
        console.log('hidePlayButton called');

        // Try multiple ways to find and hide the play button
        if (playButton) {
            console.log('Hiding play button via stored reference');
            playButton.style.cssText = 'display: none !important; visibility: hidden !important; opacity: 0 !important; pointer-events: none !important;';
            playButton.classList.add('hidden');
        }

        // Also try to find it fresh in case the reference is stale
        const freshPlayButton = document.querySelector('.play-button');
        if (freshPlayButton) {
            console.log('Hiding play button via fresh query');
            freshPlayButton.style.cssText = 'display: none !important; visibility: hidden !important; opacity: 0 !important; pointer-events: none !important;';
            freshPlayButton.classList.add('hidden');
        }

        // Double-check by adding a class as well
        const allPlayButtons = document.querySelectorAll('.play-button');
        allPlayButtons.forEach(btn => {
            console.log('Hiding play button:', btn);
            btn.style.cssText = 'display: none !important; visibility: hidden !important; opacity: 0 !important; pointer-events: none !important;';
            btn.classList.add('hidden');
        });

        // Force remove any conflicting styles
        setTimeout(() => {
            const stillVisibleButtons = document.querySelectorAll('.play-button:not(.hidden)');
            stillVisibleButtons.forEach(btn => {
                console.log('Force hiding still visible button:', btn);
                btn.style.cssText = 'display: none !important; visibility: hidden !important; opacity: 0 !important; pointer-events: none !important;';
                btn.classList.add('hidden');
            });
        }, 100);
    }
    

    
    function showFullscreenButton() {
        if (fullscreenBtn) {
            fullscreenBtn.style.display = 'block';
        }
    }
    
    // Lazy loading for game thumbnails
    function initializeLazyLoading() {
        const images = document.querySelectorAll('img[data-src]');
        
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            images.forEach(img => imageObserver.observe(img));
        } else {
            // Fallback for browsers without IntersectionObserver
            images.forEach(img => {
                img.src = img.dataset.src;
                img.classList.remove('lazy');
            });
        }
    }
    
    // Initialize lazy loading
    initializeLazyLoading();

    // Dropdown menu is handled by menu-handler.js





})();