<?php
/**
 * The template for displaying archive pages
 */

get_header(); ?>

<div class="archive-header">
    <?php if (is_post_type_archive('game')) : ?>
        <h1 class="archive-title">🎮 All Games</h1>
        <p class="archive-description">Discover all our amazing Geometry Dash games!</p>
    <?php elseif (is_tax('game_tag')) : ?>
        <h1 class="archive-title">🏷️ <?php single_term_title(); ?> Games</h1>
        <?php
        $term_description = term_description();
        if ($term_description) :
        ?>
            <p class="archive-description"><?php echo wp_kses_post($term_description); ?></p>
        <?php else : ?>
            <p class="archive-description">Games tagged with "<?php single_term_title(); ?>"</p>
        <?php endif; ?>
    <?php elseif (is_category()) : ?>
        <h1 class="archive-title">📂 <?php single_cat_title(); ?></h1>
        <?php
        $category_description = category_description();
        if ($category_description) :
        ?>
            <p class="archive-description"><?php echo wp_kses_post($category_description); ?></p>
        <?php endif; ?>
    <?php elseif (is_tag()) : ?>
        <h1 class="archive-title">🏷️ <?php single_tag_title(); ?></h1>
        <?php
        $tag_description = tag_description();
        if ($tag_description) :
        ?>
            <p class="archive-description"><?php echo wp_kses_post($tag_description); ?></p>
        <?php endif; ?>
    <?php elseif (is_date()) : ?>
        <h1 class="archive-title">📅 <?php echo get_the_date('F Y'); ?></h1>
        <p class="archive-description">Posts from <?php echo get_the_date('F Y'); ?></p>
    <?php else : ?>
        <h1 class="archive-title"><?php the_archive_title(); ?></h1>
        <?php
        $archive_description = get_the_archive_description();
        if ($archive_description) :
        ?>
            <p class="archive-description"><?php echo wp_kses_post($archive_description); ?></p>
        <?php endif; ?>
    <?php endif; ?>
</div>

<!-- Filter and Sort Options for Games -->
<?php if (is_post_type_archive('game') || is_tax('game_tag')) : ?>
    <div class="games-filter">
        <div class="filter-options">
            <div class="sort-options">
                <label for="game-sort">Sort by:</label>
                <select id="game-sort" onchange="sortGames(this.value)">
                    <option value="date">Latest</option>
                    <option value="popularity">Most Played</option>
                    <option value="rating">Highest Rated</option>
                    <option value="title">A-Z</option>
                </select>
            </div>
            
            <div class="view-options">
                <button class="view-btn active" data-view="grid" onclick="changeView('grid')">⊞ Grid</button>
                <button class="view-btn" data-view="list" onclick="changeView('list')">☰ List</button>
            </div>
        </div>
        
        <!-- Tag Filter -->
        <div class="tag-filter">
            <label>Filter by tags:</label>
            <div class="tag-buttons">
                <button class="tag-filter-btn active" data-tag="all">All</button>
                <?php
                $game_tags = get_terms(array(
                    'taxonomy' => 'game_tag',
                    'hide_empty' => true,
                    'number' => 10,
                ));
                
                if (!empty($game_tags) && !is_wp_error($game_tags)) {
                    foreach ($game_tags as $tag) {
                        $active_class = (is_tax('game_tag', $tag->slug)) ? ' active' : '';
                        echo '<button class="tag-filter-btn' . $active_class . '" data-tag="' . esc_attr($tag->slug) . '">' . esc_html($tag->name) . '</button>';
                    }
                }
                ?>
            </div>
        </div>
    </div>
<?php endif; ?>

<div class="archive-content">
    <?php if (have_posts()) : ?>
        
        <div class="games-grid" id="games-container">
            <?php while (have_posts()) : the_post(); ?>
                
                <?php if (get_post_type() === 'game') : ?>
                    <!-- Game Card -->
                    <article id="post-<?php the_ID(); ?>" <?php post_class('game-card'); ?>>
                        <a href="<?php the_permalink(); ?>">
                            <div class="game-thumbnail">
                                <?php if (has_post_thumbnail()) : ?>
                                    <?php the_post_thumbnail('game-thumbnail'); ?>
                                <?php else : ?>
                                    <div class="no-image">
                                        <span class="game-icon">🎮</span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="game-card-content">
                                <h3><?php the_title(); ?></h3>
                            </div>
                        </a>
                    </article>
                    
                <?php else : ?>
                    <!-- Regular Post Card -->
                    <article id="post-<?php the_ID(); ?>" <?php post_class('post-card'); ?>>
                        <a href="<?php the_permalink(); ?>">
                            <?php if (has_post_thumbnail()) : ?>
                                <div class="post-thumbnail">
                                    <?php the_post_thumbnail('medium'); ?>
                                </div>
                            <?php endif; ?>
                            
                            <div class="post-card-content">
                                <h3><?php the_title(); ?></h3>
                                <div class="post-excerpt">
                                    <?php the_excerpt(); ?>
                                </div>
                                <div class="post-meta">
                                    <span class="post-date"><?php echo get_the_date(); ?></span>
                                    <span class="post-author">by <?php the_author(); ?></span>
                                </div>
                            </div>
                        </a>
                    </article>
                <?php endif; ?>
                
            <?php endwhile; ?>
        </div>
        
        <!-- Pagination -->
        <div class="pagination">
            <?php
            the_posts_pagination(array(
                'mid_size' => 2,
                'prev_text' => __('← Previous', 'd27-gaming'),
                'next_text' => __('Next →', 'd27-gaming'),
            ));
            ?>
        </div>
        
    <?php else : ?>
        
        <div class="no-content">
            <h2><?php _e('Nothing Found', 'd27-gaming'); ?></h2>
            <p><?php _e('It looks like nothing was found at this location. Maybe try a search?', 'd27-gaming'); ?></p>
            <?php get_search_form(); ?>
        </div>
        
    <?php endif; ?>
</div>

<?php get_footer(); ?>

<style>
/* Archive page specific styles */
.archive-header {
    text-align: center;
    padding: 2rem;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
    margin-bottom: 2rem;
}

/* Hide sidebar widgets on game tag archive pages */
body.tax-game_tag .widget-area,
body.tax-game_tag .sidebar,
body.tax-game_tag #secondary {
    display: none !important;
}

.archive-title {
    color: #00d4ff;
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.archive-description {
    color: #ccc;
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

.games-filter {
    background: rgba(0, 0, 0, 0.8);
    padding: 1.5rem;
    border-radius: 10px;
    margin-bottom: 2rem;
}

.filter-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.sort-options select {
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border: 1px solid #00d4ff;
    border-radius: 5px;
}

.view-options {
    display: flex;
    gap: 0.5rem;
}

.view-btn {
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border: 1px solid #00d4ff;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-btn.active,
.view-btn:hover {
    background: #00d4ff;
    color: #000;
}

.tag-filter label {
    color: #00d4ff;
    font-weight: bold;
    display: block;
    margin-bottom: 0.5rem;
}

.tag-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.tag-filter-btn {
    padding: 0.3rem 0.8rem;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border: 1px solid #00d4ff;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.tag-filter-btn.active,
.tag-filter-btn:hover {
    background: #00d4ff;
    color: #000;
}

.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.game-card:hover .game-overlay {
    opacity: 1;
}

.play-icon {
    font-size: 2rem;
    color: #00d4ff;
}

.game-thumbnail {
    position: relative;
    overflow: hidden;
}

.no-image {
    height: 150px;
    background: linear-gradient(45deg, #1a1a2e, #16213e);
    display: flex;
    align-items: center;
    justify-content: center;
}

.game-icon {
    font-size: 3rem;
    opacity: 0.7;
}

/* List view styles */
.games-grid.list-view {
    display: block;
}

.games-grid.list-view .game-card {
    display: flex;
    margin-bottom: 1rem;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
    overflow: hidden;
}

.games-grid.list-view .game-thumbnail {
    width: 150px;
    flex-shrink: 0;
}

.games-grid.list-view .game-card-content {
    flex: 1;
    padding: 1rem;
}

@media (max-width: 768px) {
    .filter-options {
        flex-direction: column;
        align-items: stretch;
    }
    
    .tag-buttons {
        justify-content: center;
    }
    
    .games-grid.list-view .game-card {
        flex-direction: column;
    }
    
    .games-grid.list-view .game-thumbnail {
        width: 100%;
    }
}
</style>

<script>
function sortGames(sortBy) {
    const container = document.getElementById('games-container');
    const games = Array.from(container.children);
    
    games.sort((a, b) => {
        switch(sortBy) {
            case 'popularity':
                return parseInt(b.dataset.plays || 0) - parseInt(a.dataset.plays || 0);
            case 'rating':
                return parseFloat(b.dataset.rating || 0) - parseFloat(a.dataset.rating || 0);
            case 'title':
                return a.querySelector('h3').textContent.localeCompare(b.querySelector('h3').textContent);
            default: // date
                return 0; // Keep original order for date
        }
    });
    
    games.forEach(game => container.appendChild(game));
}

function changeView(view) {
    const container = document.getElementById('games-container');
    const buttons = document.querySelectorAll('.view-btn');
    
    buttons.forEach(btn => btn.classList.remove('active'));
    document.querySelector(`[data-view="${view}"]`).classList.add('active');
    
    if (view === 'list') {
        container.classList.add('list-view');
    } else {
        container.classList.remove('list-view');
    }
}

// Tag filtering
document.querySelectorAll('.tag-filter-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        const tag = this.dataset.tag;
        
        // Update active button
        document.querySelectorAll('.tag-filter-btn').forEach(b => b.classList.remove('active'));
        this.classList.add('active');
        
        // Filter games
        const games = document.querySelectorAll('.game-card');
        games.forEach(game => {
            if (tag === 'all') {
                game.style.display = 'block';
            } else {
                const gameTags = game.querySelectorAll('.tag');
                let hasTag = false;
                gameTags.forEach(gameTag => {
                    if (gameTag.dataset.tag === tag) {
                        hasTag = true;
                    }
                });
                game.style.display = hasTag ? 'block' : 'none';
            }
        });
    });
});
</script>
