/**
 * Emergency Dropdown Fix 
 * This CSS ensures the dropdown is visible no matter what
 */

/* Ensure dropdown toggle is visible when needed */
.dropdown-toggle[id="dropdownToggle"] {
    display: flex;
    position: relative;
    z-index: 999997;
}

/* Critical dropdown content styles */
#dropdownContent {
    position: fixed !important;
    top: 45px !important;
    right: 20px !important;
    width: auto !important;
    max-width: 300px !important;
    min-width: 200px !important;
    
    /* Essential visibility properties */
    z-index: 999999 !important;
    
    /* Appearance */
    background: rgba(0, 0, 0, 0.95) !important;
    border: 2px solid #00d4ff !important;
    border-radius: 8px !important;
    box-shadow: 0 0 30px rgba(0, 212, 255, 0.5) !important;
    
    /* Fix box model */
    padding: 5px 0 !important;
    margin: 0 !important;
    
    /* No transitions to ensure instant display */
    transition: none !important;
    transform: none !important;
    animation: none !important;
}

/* Force visibility when active */
#dropdownContent.show {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    pointer-events: auto !important;
}

/* Style list items */
#dropdownContent li {
    display: block !important;
    width: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
    border-bottom: 1px solid rgba(0, 212, 255, 0.2) !important;
}

#dropdownContent li:last-child {
    border-bottom: none !important;
}

/* Style links inside dropdown */
#dropdownContent a {
    display: block !important;
    width: 100% !important;
    padding: 12px 20px !important;
    color: white !important;
    font-size: 14px !important;
    text-align: left !important;
    text-decoration: none !important;
    background: transparent !important;
    transition: background-color 0.2s !important;
    border-radius: 0 !important;
}

#dropdownContent a:hover {
    background-color: rgba(0, 212, 255, 0.2) !important;
    color: #00d4ff !important;
}

/* Special styling for dropdown toggle button */
#dropdownToggle button {
    background: linear-gradient(45deg, rgba(0, 212, 255, 0.3), rgba(0, 212, 255, 0.5)) !important;
    border: 1px solid rgba(0, 212, 255, 0.7) !important;
    color: white !important;
    padding: 0.4rem 0.8rem !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    font-weight: bold !important;
}

/* Highlight button when dropdown is open */
.dropdown-toggle.active button,
.dropdown-toggle:has(+ .show) button {
    background: linear-gradient(45deg, rgba(0, 212, 255, 0.7), rgba(0, 212, 255, 0.9)) !important;
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.5) !important;
}

/* Mobile optimization */
@media (max-width: 768px) {
    #dropdownContent {
        right: 10px !important;
        max-width: 250px !important;
    }
    
    #dropdownContent a {
        padding: 10px 15px !important;
        font-size: 13px !important;
    }
}

/* Fix potential overflow issues */
html, body {
    overflow-x: hidden !important;
}

/* Add pointer cursor to all clickable elements */
button, a {
    cursor: pointer !important;
}