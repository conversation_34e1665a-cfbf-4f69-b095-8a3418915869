<?php
/**
 * Template for displaying single game posts
 *
 * @package D27_Gaming_Theme
 * @since 1.0.0
 */

get_header();

// Cache game data for performance
$game_id = get_the_ID();
$iframe_link = get_post_meta($game_id, '_iframe_link', true);
$game_instructions = get_post_meta($game_id, '_game_instructions', true);
$game_thumbnail = get_the_post_thumbnail_url($game_id, 'game-thumbnail') ?: get_the_post_thumbnail_url($game_id, 'full');

// Game statistics
$total_votes = (int) get_post_meta($game_id, '_total_votes', true);
$total_score = (int) get_post_meta($game_id, '_total_score', true);
$avg_rating = $total_votes > 0 ? round($total_score / $total_votes, 1) : 0;
$play_count = (int) get_post_meta($game_id, '_play_count', true);

?>

<?php while (have_posts()) : the_post(); ?>

<!-- Main Game Frame -->
<div id="game-box" class="game-frame" itemscope itemtype="https://schema.org/Game">
    <?php if ($iframe_link) : ?>
        <iframe
            class="game-iframe"
            src="<?php echo esc_url($iframe_link); ?>"
            data-game-id="<?php echo esc_attr($game_id); ?>"
            allowfullscreen
            loading="lazy"
            title="<?php echo esc_attr(get_the_title()); ?> - Play Online">
        </iframe>
    <?php endif; ?>

    <?php
    $thumbnail_style = $game_thumbnail ? 'style="--game-thumbnail: url(' . esc_url($game_thumbnail) . ');"' : '';
    $thumbnail_class = $game_thumbnail ? 'has-thumbnail' : '';
    ?>
    <button
        class="play-button <?php echo esc_attr($thumbnail_class); ?>"
        data-game-id="<?php echo esc_attr($game_id); ?>"
        aria-label="Play <?php echo esc_attr(get_the_title()); ?>"
        <?php echo $thumbnail_style; ?>>
        <div class="play-icon">
            <svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                <circle cx="30" cy="30" r="30" fill="rgba(255,255,255,0.9)"/>
                <path d="M23 18L23 42L41 30L23 18Z" fill="#333"/>
            </svg>
        </div>
        <span class="play-text">PLAY GAME</span>
    </button>
</div>

<!-- Game Controls Box -->
<div class="game-controls-box">
    <div class="game-title">
        <h1><?php the_title(); ?></h1>
    </div>

    <div class="game-action-buttons">
        <button class="action-btn fullscreen-btn" onclick="toggleFullscreen()" title="Fullscreen">
            <span>⛶</span>
            <span>Fullscreen</span>
        </button>

        <button class="action-btn share-btn" onclick="shareGame(<?php echo esc_js(get_the_ID()); ?>)" title="Share" data-game-id="<?php echo esc_attr(get_the_ID()); ?>">
            <span>📤</span>
            <span>Share</span>
        </button>
    </div>
</div>

<!-- Two Column Layout: Game Description + Popular Games -->
<div class="content-grid">
    <div class="game-description">
        <h2><?php the_title(); ?></h2>

        <?php
        // Use unified template part for game stats
        get_template_part('template-parts/game/game-stats', null, array('game_id' => get_the_ID()));
        ?>

        <div class="game-content">
            <?php
            // Get the content and apply filters but prevent auto-application of wpautop
            $content = apply_filters('the_content', get_the_content());
            // Remove any empty paragraphs that might be causing spacing issues
            $content = preg_replace('/<p>\s*<\/p>/i', '', $content);
            echo $content;
            ?>
        </div>

        <?php
        $manual_instructions = get_post_meta(get_the_ID(), '_game_instructions', true);
        if ($manual_instructions) :
        ?>
            <div class="game-instructions">
                <h3>How to Play:</h3>
                <p><?php echo esc_html($manual_instructions); ?></p>
            </div>
        <?php endif; ?>

        <!-- Game Tags Section -->
        <div class="game-tags">
            <?php
            $game_tags = get_the_terms(get_the_ID(), 'game_tag');
            if ($game_tags && !is_wp_error($game_tags)) {
                foreach ($game_tags as $tag) {
                    echo '<a href="' . esc_url(d27_get_tag_link($tag)) . '" class="tag">' . esc_html($tag->name) . '</a>';
                }
            }
            ?>
        </div>
    </div>

    <div class="popular-games">
        <h3>🔥 Most Played Games</h3>
        <?php
        $popular_games = d27_get_popular_games(6);
        if ($popular_games->have_posts()) :
            while ($popular_games->have_posts()) : $popular_games->the_post();
                // Skip current game
                if (get_the_ID() === get_queried_object_id()) continue;
        ?>
            <a href="<?php the_permalink(); ?>" class="popular-game-item">
                <?php if (has_post_thumbnail()) : ?>
                    <?php the_post_thumbnail('thumbnail', array('class' => 'popular-game-thumb')); ?>
                <?php else : ?>
                    <div class="popular-game-thumb no-thumb">🎮</div>
                <?php endif; ?>

                <div class="popular-game-info">
                    <h4><?php the_title(); ?></h4>
                    <?php
                    $play_count = get_post_meta(get_the_ID(), '_play_count', true);
                    if ($play_count) :
                    ?>
                        <span class="plays"><?php echo esc_html(number_format($play_count)); ?> plays</span>
                    <?php endif; ?>
                </div>
            </a>
        <?php
            endwhile;
            wp_reset_postdata();
        else :
        ?>
            <p>No games available yet. Check back soon!</p>
        <?php endif; ?>
    </div>
</div>

<!-- Latest Games Grid -->
<div class="latest-games-section">
    <h2>🎮 Latest Games</h2>

    <div class="games-grid">
        <?php
        $current_game_id = get_the_ID();
        $latest_games = d27_get_latest_games(12);
        if ($latest_games->have_posts()) :
            while ($latest_games->have_posts()) : $latest_games->the_post();
                // Skip current game
                if (get_the_ID() === $current_game_id) continue;
        ?>
            <article class="game-card">
                <a href="<?php the_permalink(); ?>">
                    <div class="game-thumbnail">
                        <?php if (has_post_thumbnail()) : ?>
                            <?php the_post_thumbnail('game-thumbnail'); ?>
                        <?php else : ?>
                            <div class="no-image">
                                <span class="game-icon">🎮</span>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="game-card-content">
                        <h3><?php the_title(); ?></h3>
                    </div>
                </a>
            </article>
        <?php
            endwhile;
            wp_reset_postdata();
        endif;
        ?>
    </div>

    <div class="view-all-games">
        <a href="<?php echo esc_url(get_post_type_archive_link('game')); ?>" class="btn btn-primary">
            View All Games →
        </a>
    </div>
</div>

<?php endwhile; ?>

<?php
// Include the shared interactive JavaScript template
get_template_part('template-parts/game/game-interactive.js');

get_footer();
?>