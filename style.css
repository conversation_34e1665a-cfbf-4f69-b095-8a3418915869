/*
Theme Name: D27 Gaming Theme
Description: A modern, lightweight WordPress theme designed specifically for Geometry Dash gaming websites. Features custom game post types, optimized performance, comprehensive SEO, and fully customizable navigation and sidebar through WordPress Customizer.
Author: D27 Gaming
Version: 1.0.0
License: GPL v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Text Domain: d27-gaming
Tags: gaming, games, entertainment, custom-post-types, responsive-design, seo-ready, performance-optimized
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
*/

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    overflow-x: hidden; /* Prevent horizontal scroll on html */
    width: 100%;
    max-width: 100%;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #1a1a2e;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    min-height: 100vh;
    overflow-x: hidden; /* Prevent horizontal scroll on body */
    width: 100%;
    max-width: 100%;
}

/* Header Styles */
.main-header {
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    z-index: 10000;
    height: 60px; /* Fixed height for consistent alignment */
    display: flex;
    flex-direction: row; /* Force horizontal layout */
    align-items: center;
    justify-content: space-between;
    padding: 0 20px; /* Add horizontal padding for content spacing */
    width: 100%;
    max-width: 100%;
    overflow: hidden; /* Prevent overflow issues */
    position: relative; /* Positioning context for children */
}

/* Flexbox distribution for header elements */
.header-left {
    flex: 0 0 auto; /* Don't grow, don't shrink, base size on content */
    margin-right: 20px; /* Consistent spacing from menu - keep this beautiful spacing */
    max-width: 220px; /* Increased logo area width */
    min-width: 180px; /* Ensure minimum width for logo area */
}

.header-center {
    flex: 1 1 auto; /* Grow and shrink to take available space */
    overflow: hidden; /* Prevent menu from breaking layout */
    display: flex;
    justify-content: center;
    min-width: 0; /* CRITICAL: Allows the navigation to shrink below its content's width */
    padding: 0 10px; /* Add some padding for better spacing */
}

.header-right {
    flex: 0 0 auto; /* Don't grow, don't shrink */
    margin-left: 20px; /* Consistent spacing from menu */
    min-width: 150px; /* Ensure search has minimum width */
    display: flex;
    justify-content: flex-end; /* Align to right */
}

/* Remove obsolete absolute positioning rules */
.header-logo-fixed, .header-menu-center, .header-search-fixed { display: none; }

/* LOGO STYLING - Fixed at left corner */
.header-left .home-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: #fff;
    font-weight: bold;
    gap: 0.5rem;
    white-space: nowrap; /* Prevent logo from wrapping */
    width: 100%; /* Take full width of container */
}

.header-left .site-logo {
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    flex-shrink: 0; /* Prevent logo icon from shrinking */
}

.header-left .site-title-nav {
    font-size: 1.2rem;
    color: #00d4ff;
    font-weight: 600;
    white-space: nowrap; /* Prevent text from wrapping */
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 120px; /* Ensure minimum width for site title */
    max-width: 180px; /* Maximum width for site title */
    display: block; /* Ensure it's displayed */
}

/* Navigation */
.nav-menu-dynamic {
    display: flex;
    flex-wrap: nowrap; /* CRITICAL: Prevents menu items from wrapping to a new line */
    align-items: center;
    justify-content: center; /* Center the menu items within the navigation area */
    list-style: none;
    padding: 0;
    margin: 0;
    height: 60px; /* Match header height */
    overflow: hidden; /* Prevent overflow */
    white-space: nowrap; /* Prevent text wrapping */
    width: 100%; /* Take full width of container */
}

.nav-item {
    white-space: nowrap;
    flex: 0 1 auto; /* Don't grow, allow shrinking, size based on content */
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 4px; /* Reduced padding between menu items */
    height: 100%; /* Take full height */
}

.nav-search {
    margin-left: auto;
}

.dropdown-toggle {
    display: none; /* Will be displayed by JS if needed */
    white-space: nowrap;
    flex: 0 0 auto; /* Don't grow or shrink, size based on content */
    position: relative;
    z-index: 10000;
}

/* Ensure dropdown menu is clickable and visible */
.dropdown-toggle button {
    cursor: pointer;
    position: relative;
    z-index: 10001; /* Higher than menu */
}

/* 6-ITEM LIMIT ENFORCEMENT - CSS Fallback - stricter rule */
.nav-menu-dynamic .nav-item:not(.dropdown-toggle):nth-child(n+7) {
    display: none !important; /* Hide items beyond 6-item limit + dropdown */
}

.search-box-nav input {
    padding: 0.4rem 0.8rem;
    border: none;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    width: 100%; /* Make input fill container */
    min-width: 150px;
    max-width: 200px;
    font-size: 0.85rem;
}

.search-box-nav input::placeholder {
    color: #ccc;
}

/* Ensure search form is always visible */
.nav-search .search-form,
.search-container .search-form {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    align-items: center;
    margin: 0;
    padding: 0;
    width: 100%; /* Take full width of container */
}

/* Style the search container in header */
.search-container {
    width: 100%;
    max-width: 200px;
}

.main-navigation {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.main-navigation a {
    color: #fff;
    text-decoration: none;
    padding: 0.3rem 0.5rem; /* Smaller padding for better fit */
    border-radius: 20px;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 0.8rem; /* Slightly smaller font for better fit */
    white-space: nowrap;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    height: 100%;
    width: 100%;
    box-sizing: border-box;
    text-overflow: ellipsis; /* Handle long text gracefully */
    overflow: hidden;
}

.main-navigation a:hover {
    background: #00d4ff;
    color: #000;
}

/* Dropdown Menu Styles */
.dropdown-menu {
}

.dropdown-link {
    position: absolute;
    top: 100%;
    right: 0;
    background: rgba(0, 0, 0, 0.98);
    border: 1px solid rgba(0, 212, 255, 0.5);
    border-radius: 8px;
    min-width: 200px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 99999;
}

.dropdown-link--menu {
    display: flex;
    flex-direction: column;
    gap: 0;
    padding: 0.5rem 0;
}

.dropdown-menu:hover .dropdown-link {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-link li {
    margin: 0;
}

.dropdown-link a {
    display: block;
    padding: 0.75rem 1.5rem;
    color: #fff;
    text-decoration: none;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0, 212, 255, 0.1);
    border-radius: 0;
}

.dropdown-link a:hover {
    background: rgba(0, 212, 255, 0.1);
    color: #00d4ff;
    padding-left: 2rem;
}

.dropdown-link li:last-child a {
    border-bottom: none;
}

/* Search Box */
.search-box {
    position: relative;
}

.search-box input {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    width: 200px;
}

.search-box input::placeholder {
    color: #ccc;
}

/* Main Content */
.main-content {
    max-width: 1200px;
    margin: 1rem auto 2rem auto; /* Reduced top margin from 2rem to 1rem */
    padding: 0 2rem;
    width: 100%;
    box-sizing: border-box;
    overflow: hidden;
    position: relative; /* Add positioning context */
    z-index: 100; /* Lower z-index than dropdown */
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0;
    box-sizing: border-box;
    overflow: hidden;
}

/* Ensure all game content is properly contained */
#game-box,
.game-frame,
.game-controls-box,
.content-grid,
.latest-games-section {
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
    box-sizing: border-box;
}

/* Consistent spacing between sections */
.game-controls-box {
    margin-top: 1.5rem;
    margin-bottom: 2rem;
}

.content-grid {
    margin-bottom: 3rem;
}

/* Layout Utility Classes */
.flex-container {
    display: flex;
    width: 100%;
}

.flex-column {
    flex-direction: column;
}

.flex-row {
    flex-direction: row;
}

.flex-wrap {
    flex-wrap: wrap;
}

.align-center {
    align-items: center;
}

.justify-between {
    justify-content: space-between;
}

.grid-container {
    display: grid;
    width: 100%;
}

.overflow-hidden {
    overflow: hidden;
}

.overflow-auto {
    overflow: auto;
}

/* Game Container & Layout */
.game-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    box-sizing: border-box;
}

/* Game Box */
#game-box,
.game-frame {
    position: relative;
    width: 100%;
    max-width: 1136px;
    margin: 0 auto;
    background: #000;
    border-radius: 10px;
    overflow: hidden;
    aspect-ratio: 16/10;
    box-sizing: border-box;
}
#game-box,
.game-frame {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    width: 100%;
    max-width: 1136.02px;
    height: 711.38px;
    background: #000;
    border-radius: 10px;
    margin: 0 auto;
}

.game-iframe {
    width: 100%;
    height: 100%;
    border: none;
    display: none;
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    border: none;
    border-radius: 15px;
    min-width: 200px;
    min-height: 80px;
    color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    font-family: 'Arial', sans-serif;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}

.play-button:hover {
    transform: translate(-50%, -50%) scale(1.05);
    box-shadow: 0 0 25px rgba(0, 212, 255, 0.6);
    background: linear-gradient(45deg, #0099cc, #00d4ff);
}

.play-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease;
}

.play-button:hover .play-icon {
    transform: scale(1.1);
}

.play-text {
    font-size: 16px;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Allow JavaScript to hide the play button - Enhanced */
.play-button[style*="display: none"],
.play-button.hidden,
.play-button[style*="display:none"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
}

.play-button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 20px;
    transform: translateY(-50%);
    width: 60px;
    height: 60px;
    background-image: var(--game-thumbnail);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 8px;
    opacity: 0.8;
    z-index: 1;
}

.play-button.has-thumbnail::before {
    display: block;
}

.play-button:not(.has-thumbnail)::before {
    display: none;
}

.play-button .play-text {
    font-size: 1.5rem;
    font-weight: bold;
    letter-spacing: 2px;
    text-transform: uppercase;
    position: relative;
    z-index: 2;
    margin-left: 40px;
}

/* Game Controls Box Below Frame */
.game-controls-box {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 15px;
    padding: 1.5rem;
    max-width: 1136.02px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.game-title {
    flex: 1;
    min-width: 200px;
}

.game-title h1 {
    color: #fff;
    margin: 0;
    font-size: 1.8rem;
    font-weight: 600;
    text-align: left;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.game-action-buttons {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    flex-wrap: wrap;
}

.action-btn {
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    color: #fff;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    min-width: 120px;
    text-align: center;
    justify-content: center;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
    background: linear-gradient(45deg, #0099cc, #00d4ff);
}

.action-btn.fullscreen-btn {
    background: linear-gradient(45deg, #ff6b00, #cc5500) !important;
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 999 !important;
}

.action-btn.fullscreen-btn:hover {
    background: linear-gradient(45deg, #cc5500, #ff6b00);
    box-shadow: 0 5px 15px rgba(255, 107, 0, 0.4);
}







/* Game Layout Structure - Using consistent container */

/* Game Content Grid - Responsive Two Column Layout */
.content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
    width: 100%;
}

.game-description {
    background: rgba(0, 0, 0, 0.8);
    padding: 2rem;
    border-radius: 10px;
    color: #fff;
    width: 100%;
    overflow: hidden;
}

.game-description h2 {
    color: #00d4ff;
    margin-bottom: 1rem;
}

.game-stats {
    margin: 1.5rem 0;
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(0, 212, 255, 0.2);
}

/* Game Content Handling */
.game-content {
    margin-top: 2rem;
    overflow-wrap: break-word;
    word-wrap: break-word;
    max-width: 100%;
    overflow: hidden;
}

/* Remove duplicate content */
.game-content > h2:first-of-type,
.game-content > h2:first-of-type + p {
    display: none !important;
}

.game-content > h1[class*="title"],
.game-content > h2[class*="title"] {
    display: none !important;
}

/* Clean up spacing */
.game-content p:empty {
    display: none !important;
}

.game-content > *:first-child {
    margin-top: 0 !important;
}

.game-content > *:last-child {
    margin-bottom: 0 !important;
}

.game-content {
    margin-top: 2rem;
    width: 100%;
    overflow-wrap: break-word;
    word-wrap: break-word;
    max-width: 100%;
    overflow: hidden;
}



.game-tags {
    margin: 1rem 0;
}

.tag {
    display: inline-block;
    background: #00d4ff;
    color: #000;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    margin: 0.2rem;
    text-decoration: none;
}

.popular-games {
    background: rgba(0, 0, 0, 0.8);
    padding: 2rem;
    border-radius: 10px;
    width: 100%;
    box-sizing: border-box;
    overflow: hidden;
    height: fit-content;
}

.popular-games h3 {
    color: #00d4ff;
    margin-bottom: 1rem;
    word-wrap: break-word;
}

.popular-game-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    text-decoration: none;
    color: #fff;
    transition: all 0.3s ease;
    overflow: hidden;
}

.popular-game-item:hover {
    background: rgba(0, 212, 255, 0.2);
}

.popular-game-thumb {
    width: 50px;
    height: 50px;
    border-radius: 5px;
    margin-right: 1rem;
    object-fit: cover;
    flex-shrink: 0;
}

.popular-game-info {
    min-width: 0;
    overflow: hidden;
}

.popular-game-info h4 {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    margin: 0 0 0.3rem 0;
}

/* Latest Games Section */
.latest-games-section {
    margin-top: 4rem;
    margin-bottom: 3rem;
    padding: 2rem 0;
}

.latest-games-section h2 {
    color: #00d4ff;
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2rem;
    padding-bottom: 1rem;
}

.view-all-games {
    text-align: center;
    margin-top: 3rem;
    padding-top: 2rem;
}

.btn {
    display: inline-block;
    padding: 1rem 2rem;
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    color: #000;
    text-decoration: none;
    border-radius: 25px;
    font-weight: bold;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
    color: #000;
}

.btn-primary {
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    color: #000;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0099cc, #00d4ff);
    color: #000;
}

/* Games Grid - Square Cards */
.games-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(178px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
    justify-items: center;
}

.game-card {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
    text-decoration: none;
    color: #fff;
    width: 178px;
    height: 231px;
    display: flex;
    flex-direction: column;
}

.game-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 212, 255, 0.3);
}

.game-card img,
.game-thumbnail img {
    width: 100%;
    height: 178px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.game-card:hover img,
.game-card:hover .game-thumbnail img {
    transform: scale(1.05);
}

.game-thumbnail {
    width: 100%;
    height: 178px;
    overflow: hidden;
    position: relative;
    flex-shrink: 0;
}

.game-card-content {
    padding: 0.75rem 0.5rem;
    height: 53px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    flex-shrink: 0;
}

.game-card h3 {
    color: #fff;
    margin: 0;
    font-size: 0.85rem;
    line-height: 1.2;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
}

/* Hide meta information in square cards */
.game-card .game-meta,
.game-card .game-tags,
.game-card .rating,
.game-card .plays {
    display: none;
}



.comments-section h3 {
    color: #00d4ff;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

/* Comment Form Styling */
.comment-form-container {
    margin-bottom: 2rem;
}

.comment-form {
    background: rgba(255, 255, 255, 0.05);
    padding: 1.5rem;
    border-radius: 10px;
    border: 1px solid rgba(0, 212, 255, 0.2);
}

.comment-form-field {
    margin-bottom: 1rem;
}

.comment-form-field label {
    display: block;
    margin-bottom: 0.5rem;
    color: #00d4ff;
    font-weight: 600;
}

.comment-form-field input,
.comment-form-field textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 5px;
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
    font-family: inherit;
}

.comment-form-field input:focus,
.comment-form-field textarea:focus {
    outline: none;
    border-color: #00d4ff;
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

.submit-btn {
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    color: #fff;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
}

/* Comments List */
.comments-list-container {
    margin-top: 2rem;
}

.comments-list {
    list-style: none;
    padding: 0;
}

.comment {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(0, 212, 255, 0.1);
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.comment-author {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.comment-author-avatar {
    border-radius: 50%;
    border: 2px solid #00d4ff;
}

.comment-author-name {
    font-weight: 600;
    color: #00d4ff;
}

.comment-meta {
    color: #ccc;
    font-size: 0.9rem;
}

.comment-content {
    margin-bottom: 1rem;
    line-height: 1.6;
}

.comment-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.comment-reply-link {
    color: #00d4ff;
    text-decoration: none;
    font-size: 0.9rem;
}

.comment-reply-link:hover {
    color: #fff;
}

/* Footer */
.site-footer {
    background: rgba(0, 0, 0, 0.9);
    color: #fff;
    padding: 2rem 0;
    text-align: center;
    margin-top: 3rem;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.footer-links {
    margin: 1rem 0;
}

.footer-links a {
    color: #00d4ff;
    text-decoration: none;
    margin: 0 1rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .nav-menu-dynamic {
        flex-wrap: nowrap; /* Maintain no-wrap for equal spacing */
        max-width: 700px; /* Slightly smaller max width on medium screens */
    }

    .nav-item {
        max-width: 100px; /* Slightly smaller max width on medium screens */
        font-size: 0.75rem; /* Smaller font on medium screens */
    }

    .main-navigation a {
        font-size: 0.75rem; /* Smaller font on medium screens */
        padding: 0.25rem 0.4rem; /* Smaller padding on medium screens */
    }
}

/* Game Grid Layout */
.content-grid {
    display: grid !important;
    grid-template-columns: 2fr 1fr !important;
    gap: 20px !important;
    margin: 20px auto !important;
    max-width: 1200px !important;
    box-sizing: border-box !important;
}

/* Game Info Section */
.game-info {
    background: rgba(0, 0, 0, 0.8) !important;
    padding: 20px !important;
    border-radius: 10px !important;
    overflow: hidden !important;
}

/* Popular Games Sidebar */
.popular-games {
    background: rgba(0, 0, 0, 0.8) !important;
    padding: 20px !important;
    border-radius: 10px !important;
    height: fit-content !important;
}

/* Latest Games Grid */
.latest-games {
    margin-top: 40px !important;
    text-align: center !important;
}

.games-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)) !important;
    gap: 20px !important;
    margin: 20px 0 !important;
}

@media (max-width: 1024px) {
    /* Tablet adjustments */
    .main-header {
        padding: 0 15px; /* Slightly smaller padding */
    }
    
    .header-left {
        max-width: 200px; /* Maintain logo visibility */
        min-width: 170px; /* Ensure minimum space for logo */
        margin-right: 10px; /* Keep beautiful spacing, just slightly reduced */
    }
    
    .header-right {
        margin-left: 10px; /* Keep beautiful spacing, just slightly reduced */
        min-width: 120px; /* Smaller search area */
    }

    .nav-item {
        padding: 0 3px; /* Smaller padding between items to allow site title visibility */
    }
    
    .search-container {
        max-width: 160px; /* Smaller search width */
    }
    
    /* Adjust menu padding to save horizontal space */
    .main-navigation a {
        padding: 0.2rem 0.4rem; /* Reduced padding */
    }
}

@media (max-width: 768px) {
    /* Mobile adjustments */
    .main-header {
        height: 50px; /* Smaller header height */
        padding: 0 10px; /* Smaller padding */
        flex-wrap: nowrap !important; /* CRITICAL: Prevent wrapping on mobile */
    }
    
    .header-left {
        max-width: 150px; /* Maintain enough width for site title */
        min-width: 130px; /* Minimum width for logo area */
        margin-right: 5px; /* Keep spacing but slightly reduced */
    }
    
    .header-right {
        margin-left: 5px; /* Keep spacing but slightly reduced */
        min-width: 100px; /* Smaller search area */
    }
    
    .header-left .site-logo {
        font-size: 1.2rem; /* Smaller logo */
    }
    
    .header-left .site-title-nav {
        font-size: 0.9rem; /* Smaller site title */
        min-width: 80px; /* Ensure minimum visibility */
    }

    .nav-menu-dynamic {
        height: 50px; /* Match header height */
        padding: 0 2px; /* Minimal padding */
    }

    .nav-item {
        height: 50px; /* Match header height */
        max-width: 70px; /* Smaller max width */
        padding: 0 3px; /* Minimal padding */
        font-size: 0.75rem; /* Smaller font */
    }

    .main-navigation a {
        font-size: 0.7rem; /* Smaller font */
        padding: 0.2rem 0.3rem; /* Smaller padding */
    }

    .dropdown-toggle button {
        font-size: 0.7rem; /* Smaller font */
        padding: 0.3rem 0.5rem; /* Smaller padding */
        max-width: 60px; /* Smaller width */
    }

    .search-container {
        max-width: 120px; /* Smaller search width */
    }
    
    .search-box-nav input,
    .search-field {
        width: 100% !important;
        min-width: 80px;
        max-width: 120px;
        font-size: 0.8rem;
        padding: 0.3rem 0.6rem;
    }
    
    /* Ensure search button is compact */
    .search-submit {
        padding: 0.3rem 0.5rem;
    }
}

    .dropdown-link {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        border: none;
        background: transparent;
        min-width: auto;
    }

    .dropdown-menu:hover .dropdown-link {
        transform: none;
    }

    .dropdown-toggle {
        display: none;
    }
    
    .content-grid {
        grid-template-columns: 1fr;
    }
    
    .games-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
    
    #game-box,
    .game-frame {
        max-width: 100%;
        height: 480px;
        padding: 1rem;
    }

    .game-controls-box {
        max-width: 100%;
        padding: 1rem;
        flex-direction: column;
        text-align: center;
    }

    .game-title h1 {
        font-size: 1.4rem;
        text-align: center;
        margin-bottom: 1rem;
    }

    .game-action-buttons {
        justify-content: center;
    }

    .play-button {
        width: 180px;
        height: 70px;
        font-size: 1.3rem;
    }

    .play-button::before {
        width: 50px;
        height: 50px;
        left: 15px;
    }

    .play-button .play-text {
        margin-left: 30px;
    }
    
    /* Responsive adjustments for all game pages */
    .latest-games-section {
        margin-top: 2rem;
    }
    
    /* Ensure iframe/game content fits mobile screens */
    #game-box,
    .game-frame {
        padding: 1rem;
        height: auto;
        aspect-ratio: 16/9;
        min-height: 200px;
    }
    
    /* Make two column layout stack on mobile */
    .content-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

/* Very small screens */
@media (max-width: 480px) {
    .main-header {
        padding: 0 8px; /* Minimal padding */
    }
    
    .header-left {
        max-width: 100px; /* Minimal logo area */
        margin-right: 3px;
    }
    
    .header-right {
        margin-left: 3px;
        min-width: 80px;
    }
    
    /* Compact logo */
    .header-left .home-link {
        gap: 0.2rem;
    }
    
    .header-left .site-logo {
        font-size: 1rem;
    }
    
    .header-left .site-title-nav {
        font-size: 0.8rem;
        max-width: 70px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    /* More compact search */
    .search-container {
        max-width: 80px;
    }
    
    .search-field {
        padding: 0.3rem 1.8rem 0.3rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .search-submit {
        padding: 0.2rem 0.4rem;
        right: 2px;
    }
    
    .game-controls-box {
        padding: 0.75rem;
    }
    
    .game-action-buttons {
        width: 100%;
    }
    
    .game-description {
        padding: 1rem;
    }
    
    .game-title h1 {
        font-size: 1.2rem;
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.mb-2 {
    margin-bottom: 2rem;
}

/* Games Page Styles */
.games-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 40px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    color: white;
}

.games-header .page-title {
    font-size: 2.5rem;
    margin-bottom: 20px;
    font-weight: 700;
}

.games-filter-nav {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.filter-link {
    padding: 10px 20px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.filter-link:hover,
.filter-link.active {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.no-games-found {
    text-align: center;
    padding: 60px 20px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 15px;
    margin: 40px 0;
    color: white;
}

.no-games-found h2 {
    color: #00d4ff;
    margin-bottom: 15px;
}

.no-games-found p {
    color: #ccc;
    font-size: 16px;
}

.pagination-wrapper {
    margin-top: 40px;
    text-align: center;
}

.pagination-wrapper .page-numbers {
    display: inline-flex;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 10px;
}

.pagination-wrapper .page-numbers li {
    margin: 0;
}

.pagination-wrapper .page-numbers a,
.pagination-wrapper .page-numbers span {
    display: block;
    padding: 10px 15px;
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 212, 255, 0.3);
}

.pagination-wrapper .page-numbers a:hover,
.pagination-wrapper .page-numbers .current {
    background: #00d4ff;
    color: #000;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
}

.main-navigation ul {
    overflow: visible;
}

.dropdown-toggle button {
    background: rgba(0, 212, 255, 0.3);
    color: white;
    border: 1px solid rgba(0, 212, 255, 0.6);
    cursor: pointer;
    padding: 0.4rem 0.8rem; /* Smaller padding to fit better */
    font-size: 0.85rem; /* Slightly smaller font */
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.3rem; /* Smaller gap */
    white-space: nowrap;
    width: 100%; /* Fill the dropdown container */
    max-width: 100px; /* Prevent it from getting too wide */
    box-sizing: border-box;
    min-width: 80px;
    justify-content: center;
}

.dropdown-toggle button:hover {
    background: rgba(0, 212, 255, 0.5);
    border-color: #00d4ff;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
}

.dropdown-toggle button:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 212, 255, 0.2);
}

.dropdown-content {
    position: absolute;
    top: 100%;
    right: 0;
    background: rgba(17, 17, 17, 0.98);
    border: 1px solid rgba(0, 212, 255, 0.5);
    display: none;
    flex-direction: column;
    min-width: 200px;
    z-index: 99999;
    border-radius: 10px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    padding: 0.5rem 0;
    margin-top: 5px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.dropdown-content.show {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
}

.dropdown-content > * {
    display: block !important;
    margin: 0;
    padding: 0;
}

.dropdown-content a {
    display: block !important;
    padding: 12px 16px !important;
    color: #fff !important;
    text-decoration: none !important;
    border-radius: 0 !important;
    transition: all 0.3s ease !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

.dropdown-content a:hover {
    background: rgba(0, 212, 255, 0.2) !important;
    color: #00d4ff !important;
}

.dropdown-content .nav-item {
    flex-grow: 0;
    flex-shrink: 0;
    flex-basis: auto;
    padding: 0;
    text-align: left;
    justify-content: flex-start;
    border-bottom: 1px solid #333;
}

.dropdown-content .nav-item:last-child {
    border-bottom: none;
}

.dropdown-content .nav-item a {
    padding: 12px 16px;
    display: block;
    width: 100%;
    text-align: left;
    border-radius: 0;
}

.dropdown-content .nav-item a:hover {
    background: #00d4ff;
    color: #000;
}

/* Responsive adjustments for games page */
@media (max-width: 768px) {
    .games-header .page-title {
        font-size: 2rem;
    }

    .games-filter-nav {
        gap: 10px;
    }

    .filter-link {
        padding: 8px 16px;
        font-size: 14px;
    }
}

/* Force full-width header on all pages - highest specificity */
body .site-header,
body.home .site-header,
body.archive .site-header,
body.search .site-header,
body.single .site-header,
body.page .site-header,
body.tax-game_tag .site-header,
body.post-type-archive-game .site-header,
body.error404 .site-header {
    width: 100% !important; /* Full width without scrollbar - prevents horizontal scroll */
    max-width: 100% !important; /* Ensure no overflow - prevents horizontal scroll */
    margin: 0 !important;
    box-sizing: border-box !important;
}

body .header-container,
body.home .header-container,
body.archive .header-container,
body.search .header-container,
body.single .header-container,
body.page .header-container,
body.tax-game_tag .header-container,
body.post-type-archive-game .header-container,
body.error404 .header-container {
    width: 100% !important; /* Full width without scrollbar - prevents horizontal scroll */
    max-width: 100% !important; /* Ensure no overflow - prevents horizontal scroll */
    margin: 0 !important;
    display: flex !important;
    box-sizing: border-box !important;
}

/* Interactive Star Rating Styles - Updated to match user's approach */
.rating-star {
    cursor: pointer;
    font-size: 20px;
    color: gold;
    margin: 0 2px;
    transition: transform 0.2s;
}

.rating-star.hover {
    transform: scale(1.2);
}

.rating-star.filled {
    color: orange;
}

.rating-result {
    margin-left: 10px;
    font-weight: bold;
    color: #00bfff;
}

/* Ensure proper spacing in game stats */
.game-stats .rating {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.game-stats .play-count {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.play-count strong {
    color: #00d4ff;
}

#play-count-display {
    color: #fff;
    font-weight: bold;
}