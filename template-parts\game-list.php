<?php
/**
 * Template part for displaying games in list format
 */

$game_query = isset($args['query']) ? $args['query'] : null;
$columns = isset($args['columns']) ? $args['columns'] : 4;
$show_meta = isset($args['show_meta']) ? $args['show_meta'] : true;
$show_excerpt = isset($args['show_excerpt']) ? $args['show_excerpt'] : false;

if (!$game_query) {
    $game_query = d27_get_latest_games(12);
}
?>

<div class="games-list-container">
    <?php if ($game_query->have_posts()) : ?>
        
        <div class="games-grid" style="grid-template-columns: repeat(auto-fit, minmax(<?php echo esc_attr(200 / $columns * 100); ?>px, 1fr));">
            <?php while ($game_query->have_posts()) : $game_query->the_post(); ?>
                
                <article class="game-list-item" data-game-id="<?php echo esc_attr(get_the_ID()); ?>">
                    <a href="<?php the_permalink(); ?>" class="game-link">
                        
                        <!-- Game Thumbnail -->
                        <div class="game-thumbnail-container">
                            <?php if (has_post_thumbnail()) : ?>
                                <?php the_post_thumbnail('game-thumbnail', array('class' => 'game-thumbnail')); ?>
                            <?php else : ?>
                                <div class="game-thumbnail no-image">
                                    <span class="game-icon">🎮</span>
                                    <span class="game-placeholder-text"><?php echo esc_html(substr(get_the_title(), 0, 1)); ?></span>
                                </div>
                            <?php endif; ?>
                            
                            <!-- Hover Overlay -->
                            <div class="game-overlay">
                                <div class="overlay-content">
                                    <span class="play-icon">▶️</span>
                                    <span class="play-text">PLAY NOW</span>
                                </div>
                            </div>
                            
                            <!-- Quick Stats Badge -->
                            <?php
                            $play_count = get_post_meta(get_the_ID(), '_play_count', true);
                            $rating = get_post_meta(get_the_ID(), '_rating', true);
                            ?>
                            
                            <?php if ($play_count && $play_count > 100) : ?>
                                <div class="popularity-badge">
                                    <?php if ($play_count > 10000) : ?>
                                        <span class="badge hot">🔥 HOT</span>
                                    <?php elseif ($play_count > 1000) : ?>
                                        <span class="badge popular">⭐ POPULAR</span>
                                    <?php else : ?>
                                        <span class="badge trending">📈 TRENDING</span>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Game Info - Square Card Design -->
                        <div class="game-card-content">
                            <h3 class="game-title"><?php the_title(); ?></h3>
                        </div>
                    </a>
                    
                    <!-- Quick Action Buttons -->
                    <div class="quick-actions">
                        <button class="quick-action-btn favorite-btn" onclick="toggleFavorite(<?php echo esc_js(get_the_ID()); ?>); event.preventDefault();" title="Add to Favorites">
                            <span class="heart-icon">🤍</span>
                        </button>
                        
                        <button class="quick-action-btn share-btn" onclick="shareGame(<?php echo esc_js(get_the_ID()); ?>); event.preventDefault();" title="Share Game">
                            <span class="share-icon">📤</span>
                        </button>
                    </div>
                </article>
                
            <?php endwhile; ?>
        </div>
        
        <?php wp_reset_postdata(); ?>
        
    <?php else : ?>
        
        <div class="no-games-found">
            <div class="no-games-icon">🎮</div>
            <h3>No Games Found</h3>
            <p>Sorry, no games match your criteria. Try adjusting your filters or check back later for new games!</p>
        </div>
        
    <?php endif; ?>
</div>

<style>
/* Game List Styles */
.games-list-container {
    width: 100%;
}

.games-grid {
    display: grid;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.game-list-item {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid transparent;
}

.game-list-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 212, 255, 0.3);
    border-color: #00d4ff;
}

.game-link {
    display: block;
    text-decoration: none;
    color: inherit;
}

.game-thumbnail-container {
    position: relative;
    overflow: hidden;
}

.game-thumbnail {
    width: 100%;
    height: 180px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.game-thumbnail.no-image {
    height: 180px;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.game-icon {
    font-size: 3rem;
    opacity: 0.7;
    margin-bottom: 0.5rem;
}

.game-placeholder-text {
    font-size: 2rem;
    font-weight: bold;
    color: #00d4ff;
    opacity: 0.8;
}

.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.game-list-item:hover .game-overlay {
    opacity: 1;
}

.game-list-item:hover .game-thumbnail {
    transform: scale(1.05);
}

.overlay-content {
    text-align: center;
    color: #fff;
}

.overlay-content .play-icon {
    font-size: 3rem;
    display: block;
    margin-bottom: 0.5rem;
    color: #00d4ff;
}

.overlay-content .play-text {
    font-weight: bold;
    font-size: 1rem;
    color: #00d4ff;
}

.popularity-badge {
    position: absolute;
    top: 10px;
    right: 10px;
}

.badge {
    padding: 0.3rem 0.6rem;
    border-radius: 15px;
    font-size: 0.7rem;
    font-weight: bold;
    text-transform: uppercase;
}

.badge.hot {
    background: linear-gradient(45deg, #ff4757, #ff3742);
    color: #fff;
}

.badge.popular {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #000;
}

.badge.trending {
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    color: #fff;
}

.game-info {
    padding: 1.2rem;
}

.game-title {
    color: #fff;
    font-size: 1.1rem;
    margin: 0 0 0.8rem 0;
    line-height: 1.3;
    font-weight: 600;
}

.game-excerpt {
    color: #ccc;
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 0.8rem;
}

.game-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.8rem;
    font-size: 0.9rem;
}

.rating-display {
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.stars {
    color: #ffd700;
    font-size: 0.8rem;
}

.rating-number {
    color: #00d4ff;
    font-weight: bold;
}

.play-count-display {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    color: #ccc;
}

.play-count-display .play-icon {
    font-size: 0.8rem;
}

.game-tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.4rem;
    margin-bottom: 0.5rem;
}

.game-tag {
    background: rgba(0, 212, 255, 0.2);
    color: #00d4ff;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.more-tags {
    background: rgba(255, 255, 255, 0.1);
    color: #ccc;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.75rem;
}

.quick-actions {
    position: absolute;
    top: 10px;
    left: 10px;
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.game-list-item:hover .quick-actions {
    opacity: 1;
}

.quick-action-btn {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid #00d4ff;
    color: #00d4ff;
    padding: 0.4rem;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quick-action-btn:hover {
    background: #00d4ff;
    color: #000;
}

.favorite-btn.active {
    background: #ff4757;
    border-color: #ff4757;
    color: #fff;
}

.no-games-found {
    text-align: center;
    padding: 4rem 2rem;
    color: #ccc;
}

.no-games-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.no-games-found h3 {
    color: #00d4ff;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .games-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }
    
    .game-thumbnail,
    .game-thumbnail.no-image {
        height: 140px;
    }
    
    .game-info {
        padding: 1rem;
    }
    
    .game-title {
        font-size: 1rem;
    }
    
    .game-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .games-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    }
    
    .game-thumbnail,
    .game-thumbnail.no-image {
        height: 120px;
    }
    
    .game-icon {
        font-size: 2rem;
    }
    
    .overlay-content .play-icon {
        font-size: 2rem;
    }
}
</style>

<?php
// Helper function to format numbers
if (!function_exists('d27_format_number')) {
    function d27_format_number($number) {
        if ($number >= 1000000) {
            return round($number / 1000000, 1) . 'M';
        } elseif ($number >= 1000) {
            return round($number / 1000, 1) . 'K';
        }
        return number_format($number);
    }
}
?>
