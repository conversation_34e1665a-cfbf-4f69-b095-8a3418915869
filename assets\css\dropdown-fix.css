/**
 * Dropdown Fix - Ensures the dropdown menu works correctly
 */

/* Base styling for dropdown toggle */
.dropdown-toggle {
    position: relative !important;
    z-index: 10000 !important;
    display: flex !important;
}

/* Style for dropdown toggle button */
.dropdown-toggle button {
    background: rgba(0, 212, 255, 0.3) !important;
    color: white !important;
    border: 1px solid rgba(0, 212, 255, 0.6) !important;
    cursor: pointer !important;
    padding: 0.4rem 0.8rem !important;
    font-size: 0.85rem !important;
    font-weight: 600 !important;
    border-radius: 8px !important;
    white-space: nowrap !important;
    z-index: 10001 !important;
}

.dropdown-toggle button:hover {
    background: rgba(0, 212, 255, 0.5) !important;
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.3) !important;
}

/* Critical styling for dropdown content */
.dropdown-content {
    position: absolute !important;
    top: 45px !important; /* Position below the button */
    right: 0 !important;
    background: rgba(0, 0, 0, 0.95) !important;
    border: 2px solid rgba(0, 212, 255, 0.5) !important;
    min-width: 200px !important;
    z-index: 999999 !important; /* Extra high z-index */
    border-radius: 8px !important;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.7) !important;
    padding: 5px 0 !important;
    margin-top: 5px !important;
    
    /* Hidden by default */
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    
    /* Ensure it's above everything */
    transform: none !important;
}

/* Show dropdown when active */
.dropdown-content.show {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Style dropdown items */
.dropdown-content li {
    display: block !important;
    padding: 0 !important;
    margin: 0 !important;
    width: 100% !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.dropdown-content li:last-child {
    border-bottom: none !important;
}

.dropdown-content a {
    display: block !important;
    padding: 10px 15px !important;
    color: white !important;
    text-decoration: none !important;
    font-size: 0.9rem !important;
    transition: all 0.2s ease !important;
}

.dropdown-content a:hover {
    background: rgba(0, 212, 255, 0.2) !important;
    color: #00d4ff !important;
    padding-left: 20px !important;
}

/* When dropdown is shown, highlight the toggle button */
.dropdown-toggle.active button {
    background: rgba(0, 212, 255, 0.5) !important;
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.3) !important;
}