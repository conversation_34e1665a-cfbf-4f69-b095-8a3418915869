/**
 * Responsive Dropdown
 * 
 * A high-performance, responsive dropdown solution with:
 * - Instant response to clicks
 * - No lag or delay
 * - Direct DOM manipulation
 * - Simple, clean event handling
 */

(function() {
    'use strict';

    // Initialize immediately
    document.addEventListener('DOMContentLoaded', init);
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        init();
    }

    let dropdownActive = false;
    let dropdownButton = null;
    let dropdownContainer = null;
    let touchDevice = false;

    function init() {
        // Detect touch devices for better interaction
        touchDevice = ('ontouchstart' in window) || 
                      (navigator.maxTouchPoints > 0) || 
                      (navigator.msMaxTouchPoints > 0);
                      
        console.log('Initializing responsive dropdown - Touch device:', touchDevice);

        // First, remove any duplicate dropdown toggles
        removeDuplicateToggles();

        // Create dropdown elements
        createResponsiveDropdown();

        // Find the More button
        setupDropdownButton();
    }
    
    function removeDuplicateToggles() {
        // Get all dropdown toggles
        const toggles = document.querySelectorAll('#dropdownToggle, .dropdown-toggle');
        
        if (toggles.length > 1) {
            console.log('Found', toggles.length, 'dropdown toggles, removing duplicates');
            
            // Keep only the first one
            for (let i = 1; i < toggles.length; i++) {
                if (toggles[i] && toggles[i].parentNode) {
                    console.log('Removing duplicate dropdown toggle:', toggles[i]);
                    toggles[i].parentNode.removeChild(toggles[i]);
                }
            }
        }
    }

    function createResponsiveDropdown() {
        // Create a clean, standalone dropdown container
        dropdownContainer = document.createElement('div');
        dropdownContainer.id = 'responsive-dropdown';
        
        // Apply high-performance styles
        Object.assign(dropdownContainer.style, {
            position: 'fixed',
            display: 'none',
            top: '0px',
            right: '0px',
            width: '220px',
            backgroundColor: 'rgba(0, 0, 0, 0.95)',
            border: '2px solid #00d4ff',
            borderRadius: '8px',
            boxShadow: '0 0 20px rgba(0, 212, 255, 0.7)',
            zIndex: '9999999',
            padding: '5px',
            transition: 'opacity 0.15s ease-out',
            opacity: '0',
            transform: 'translateY(0)',
            maxHeight: '80vh',
            overflowY: 'auto'
        });
        
        // Add clean list container
        const listContainer = document.createElement('ul');
        listContainer.id = 'responsive-dropdown-items';
        Object.assign(listContainer.style, {
            listStyle: 'none',
            margin: '0',
            padding: '0'
        });
        
        dropdownContainer.appendChild(listContainer);
        document.body.appendChild(dropdownContainer);
        
        // Populate initially
        refreshDropdownContent();
    }

    function setupDropdownButton() {
        // Find dropdown toggle in multiple ways to ensure we get it
        dropdownButton = document.querySelector('#dropdownToggle button, .dropdown-toggle button');
        
        if (!dropdownButton) {
            console.warn('Dropdown button not found, creating one');
            
            const toggle = document.querySelector('#dropdownToggle, .dropdown-toggle');
            if (toggle) {
                dropdownButton = document.createElement('button');
                dropdownButton.textContent = '⋯ More';
                
                // Add clean, direct styling
                Object.assign(dropdownButton.style, {
                    backgroundColor: 'rgba(0, 212, 255, 0.5)',
                    color: 'white',
                    border: '1px solid rgba(0, 212, 255, 0.7)',
                    padding: '5px 10px',
                    borderRadius: '5px',
                    cursor: 'pointer',
                    fontWeight: 'bold'
                });
                
                toggle.appendChild(dropdownButton);
            } else {
                console.error('Dropdown toggle element not found');
                return;
            }
        }
        
        // Remove any existing handlers
        dropdownButton.removeAttribute('onclick');
        
        // Use cleaner event listeners
        if (touchDevice) {
            dropdownButton.addEventListener('touchstart', toggleDropdown, {passive: false});
        } else {
            dropdownButton.addEventListener('click', toggleDropdown);
        }
        
        console.log('Dropdown button set up:', dropdownButton);
    }

    function toggleDropdown(e) {
        // Prevent any default behaviors
        if (e) {
            e.preventDefault();
            e.stopPropagation();
        }
        
        // Instant visual feedback (button)
        if (dropdownButton) {
            dropdownButton.style.backgroundColor = dropdownActive ? 
                'rgba(0, 212, 255, 0.5)' : 'rgba(0, 212, 255, 0.8)';
            dropdownButton.style.boxShadow = dropdownActive ? 
                'none' : '0 0 10px rgba(0, 212, 255, 0.5)';
        }
        
        // Toggle dropdown state
        if (dropdownActive) {
            hideDropdown();
        } else {
            showDropdown();
        }
        
        return false;
    }

    function showDropdown() {
        if (!dropdownContainer || !dropdownButton) return;
        
        // Position dropdown correctly
        positionDropdown();
        
        // Make visible immediately
        dropdownContainer.style.display = 'block';
        
        // Add animation
        setTimeout(() => {
            dropdownContainer.style.opacity = '1';
        }, 10);
        
        // Set active state
        dropdownActive = true;
        
        // Handle clicks outside
        setTimeout(() => {
            if (touchDevice) {
                document.addEventListener('touchstart', handleOutsideTouch);
            } else {
                document.addEventListener('click', handleOutsideClick);
            }
        }, 10);
        
        console.log('Dropdown shown');
    }

    function hideDropdown() {
        if (!dropdownContainer) return;
        
        // Fade out
        dropdownContainer.style.opacity = '0';
        
        // Hide after transition
        setTimeout(() => {
            dropdownContainer.style.display = 'none';
        }, 150);
        
        // Set inactive state
        dropdownActive = false;
        
        // Remove outside click handler
        if (touchDevice) {
            document.removeEventListener('touchstart', handleOutsideTouch);
        } else {
            document.removeEventListener('click', handleOutsideClick);
        }
        
        console.log('Dropdown hidden');
    }

    function handleOutsideClick(e) {
        if (dropdownContainer && dropdownButton && 
            !dropdownContainer.contains(e.target) && 
            !dropdownButton.contains(e.target)) {
            hideDropdown();
        }
    }
    
    function handleOutsideTouch(e) {
        if (dropdownContainer && dropdownButton && 
            !dropdownContainer.contains(e.target) && 
            !dropdownButton.contains(e.target)) {
            hideDropdown();
        }
    }

    function positionDropdown() {
        if (!dropdownContainer || !dropdownButton) return;
        
        // Get button position
        const rect = dropdownButton.getBoundingClientRect();
        
        // Position dropdown
        dropdownContainer.style.top = (rect.bottom + 5) + 'px';
        
        // Align to right edge of button
        const rightEdge = window.innerWidth - rect.right;
        dropdownContainer.style.right = Math.max(rightEdge, 20) + 'px';
        
        console.log('Dropdown positioned at:', { 
            top: dropdownContainer.style.top,
            right: dropdownContainer.style.right
        });
    }

    function refreshDropdownContent() {
        if (!dropdownContainer) return;
        
        const listContainer = document.getElementById('responsive-dropdown-items');
        if (!listContainer) return;
        
        // Clear list
        listContainer.innerHTML = '';
        
        // Get menu items beyond the 6-item limit
        const menu = document.querySelector('.nav-menu-dynamic') || document.getElementById('primary-menu');
        if (!menu) return;
        
        const items = Array.from(menu.querySelectorAll('li:not(#dropdownToggle):not(.dropdown-toggle)'));
        const MAX_VISIBLE = 6;
        
        // Show dropdown toggle if needed
        const toggle = document.getElementById('dropdownToggle') || document.querySelector('.dropdown-toggle');
        if (toggle) {
            toggle.style.display = items.length > MAX_VISIBLE ? 'flex' : 'none';
        }
        
        // Add overflow items to dropdown
        items.forEach((item, index) => {
            if (index >= MAX_VISIBLE) {
                const link = item.querySelector('a');
                if (link) {
                    // Hide original item
                    item.style.display = 'none';
                    
                    // Create dropdown item
                    const listItem = document.createElement('li');
                    Object.assign(listItem.style, {
                        margin: '0',
                        padding: '0',
                        borderBottom: '1px solid rgba(0, 212, 255, 0.15)'
                    });
                    
                    // Create link
                    const newLink = document.createElement('a');
                    newLink.href = link.href;
                    newLink.textContent = link.textContent;
                    Object.assign(newLink.style, {
                        display: 'block',
                        padding: '10px 15px',
                        color: 'white',
                        textDecoration: 'none',
                        fontSize: '14px',
                        fontFamily: 'Arial, sans-serif',
                        transition: 'all 0.1s ease'
                    });
                    
                    // High-performance hover effect
                    newLink.onmouseenter = function() {
                        this.style.backgroundColor = 'rgba(0, 212, 255, 0.2)';
                        this.style.color = '#00d4ff';
                        this.style.paddingLeft = '20px';
                    };
                    newLink.onmouseleave = function() {
                        this.style.backgroundColor = 'transparent';
                        this.style.color = 'white';
                        this.style.paddingLeft = '15px';
                    };
                    
                    // Add to list
                    listItem.appendChild(newLink);
                    listContainer.appendChild(listItem);
                }
            } else {
                // Show item in main menu
                item.style.display = 'flex';
            }
        });
        
        console.log('Dropdown content refreshed');
    }

    // Add global access
    window.toggleResponsiveDropdown = toggleDropdown;
    window.refreshDropdownItems = refreshDropdownContent;
})();