/**
 * D27 Gaming Theme - Consolidated Styles
 * Optimized and consolidated CSS for better performance
 * Combines: header-fix.css, dropdown-fix.css, responsive-dropdown.css, logo-visibility-fix.css
 */

/* ===== HEADER LAYOUT FIXES ===== */
/* Force single row layout */
.main-header {
    flex-wrap: nowrap !important;
    height: 60px !important;
    min-height: 60px !important;
    max-height: 60px !important;
    overflow: visible !important;
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    box-sizing: border-box !important;
}

/* Ensure header items stay in a row */
.header-left, 
.header-center,
.header-right {
    flex-shrink: 0;
}

/* Logo area - optimized width */
.header-left {
    width: auto !important;
    flex: 0 0 auto !important;
    min-width: 200px !important;
}

/* Center navigation area */
.header-center {
    flex: 0 1 auto !important; 
    max-width: calc(100% - 350px) !important;
    overflow: hidden !important;
}

/* Search area */
.header-right {
    width: auto !important;
    flex: 0 0 auto !important;
}

/* ===== LOGO VISIBILITY FIXES ===== */
.header-left .home-link {
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    color: white !important;
    text-decoration: none !important;
    z-index: 1000 !important;
    position: relative !important;
}

.header-left .site-logo {
    font-size: 1.5rem !important;
    line-height: 1 !important;
    display: inline-block !important;
    vertical-align: middle !important;
}

.header-left .site-title-nav {
    font-size: 1rem !important;
    font-weight: 600 !important;
    color: white !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 150px !important;
}

/* ===== NAVIGATION MENU LIMITS ===== */
/* Strictly enforce 6-item limit */
.nav-menu-dynamic > li:nth-child(n+7):not(.dropdown-toggle) {
    display: none !important;
}

/* Always show dropdown when there are more than 6 items */
.nav-menu-dynamic:has(> li:nth-child(7)) .dropdown-toggle,
.nav-menu-dynamic:has(> li:nth-child(7)) ~ .dropdown-toggle {
    display: flex !important;
}

/* ===== DROPDOWN STYLES ===== */
/* Base dropdown toggle styling */
.dropdown-toggle {
    position: relative !important;
    z-index: 10000 !important;
    display: flex !important;
    flex-grow: 0 !important;
    flex-shrink: 0 !important;
    flex-basis: auto !important;
}

/* Dropdown toggle button */
.dropdown-toggle button,
#dropdownToggle button {
    background: linear-gradient(45deg, rgba(0, 212, 255, 0.3), rgba(0, 212, 255, 0.5)) !important;
    color: white !important;
    border: 1px solid rgba(0, 212, 255, 0.6) !important;
    cursor: pointer !important;
    padding: 0.4rem 0.8rem !important;
    font-size: 0.85rem !important;
    font-weight: 600 !important;
    border-radius: 8px !important;
    white-space: nowrap !important;
    z-index: 10001 !important;
    outline: none !important;
    
    /* Performance optimizations */
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
    will-change: transform, background-color;
    transition: background-color 0.1s ease, transform 0.1s ease, box-shadow 0.1s ease;
}

.dropdown-toggle button:hover,
#dropdownToggle button:hover {
    background-color: rgba(0, 212, 255, 0.7) !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 212, 255, 0.4) !important;
}

.dropdown-toggle button:active,
#dropdownToggle button:active {
    transform: translateY(1px);
    box-shadow: 0 1px 3px rgba(0, 212, 255, 0.3) !important;
}

/* ===== DROPDOWN CONTENT ===== */
.dropdown-content,
#dropdownContent,
#inlineDropdown,
#responsive-dropdown {
    position: absolute !important;
    top: 45px !important;
    right: 0 !important;
    background: rgba(0, 0, 0, 0.95) !important;
    border: 2px solid rgba(0, 212, 255, 0.5) !important;
    min-width: 200px !important;
    max-width: 300px !important;
    z-index: 999999 !important;
    border-radius: 8px !important;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.7), 0 0 20px rgba(0, 212, 255, 0.5) !important;
    padding: 5px 0 !important;
    margin-top: 5px !important;
    
    /* Hidden by default */
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    
    /* Performance optimizations */
    transform: translateZ(0);
    backface-visibility: hidden;
    will-change: opacity, transform;
    filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.5));
    
    /* Scrolling for long lists */
    max-height: 80vh !important;
    overflow-y: auto !important;
}

/* Show dropdown when active */
.dropdown-content.show,
#dropdownContent.show,
#inlineDropdown.force-show,
#responsive-dropdown.show {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* ===== DROPDOWN ITEMS ===== */
.dropdown-content li,
#dropdownContent li,
#inlineDropdown li,
#responsive-dropdown li {
    display: block !important;
    padding: 0 !important;
    margin: 0 !important;
    width: 100% !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    list-style: none !important;
}

.dropdown-content li:last-child,
#dropdownContent li:last-child,
#inlineDropdown li:last-child,
#responsive-dropdown li:last-child {
    border-bottom: none !important;
}

.dropdown-content a,
#dropdownContent a,
#inlineDropdown a,
#responsive-dropdown a {
    display: block !important;
    padding: 10px 15px !important;
    color: white !important;
    text-decoration: none !important;
    font-size: 0.9rem !important;
    line-height: 1.4 !important;
    font-family: Arial, sans-serif !important;
    box-sizing: border-box !important;
    width: 100% !important;
    text-align: left !important;
    background-color: transparent !important;
    border: none !important;
    
    /* Performance optimizations */
    will-change: background-color, padding-left;
    transition: background-color 0.1s ease, color 0.1s ease, padding-left 0.1s ease;
    cursor: pointer;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.dropdown-content a:hover,
#dropdownContent a:hover,
#inlineDropdown a:hover,
#responsive-dropdown a:hover {
    background-color: rgba(0, 212, 255, 0.2) !important;
    color: #00d4ff !important;
    padding-left: 20px !important;
}

/* ===== RESPONSIVE OPTIMIZATIONS ===== */
/* Touch device optimizations */
@media (pointer: coarse) {
    .dropdown-toggle button,
    #dropdownToggle button {
        min-height: 34px;
        padding: 6px 12px !important;
    }
    
    .dropdown-content a,
    #dropdownContent a,
    #inlineDropdown a,
    #responsive-dropdown a {
        min-height: 44px;
        line-height: 24px;
        padding-top: 10px !important;
        padding-bottom: 10px !important;
    }
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .header-left {
        min-width: 150px !important;
    }
    
    .header-center {
        max-width: calc(100% - 250px) !important;
    }
    
    .dropdown-content,
    #dropdownContent,
    #inlineDropdown,
    #responsive-dropdown {
        right: 10px !important;
        min-width: 180px !important;
    }
}

@media (max-width: 480px) {
    .header-left {
        min-width: 100px !important;
    }
    
    .header-left .site-title-nav {
        max-width: 70px !important;
        font-size: 0.8rem !important;
    }
    
    .dropdown-content,
    #dropdownContent,
    #inlineDropdown,
    #responsive-dropdown {
        right: 5px !important;
        min-width: 160px !important;
        max-width: calc(100vw - 20px) !important;
    }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
/* Focus styles */
.dropdown-toggle button:focus,
#dropdownToggle button:focus {
    outline: 2px solid #00d4ff !important;
    outline-offset: 2px !important;
}

.dropdown-content a:focus,
#dropdownContent a:focus,
#inlineDropdown a:focus,
#responsive-dropdown a:focus {
    outline: 2px solid #00d4ff !important;
    outline-offset: -2px !important;
    background-color: rgba(0, 212, 255, 0.3) !important;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .dropdown-toggle button,
    #dropdownToggle button,
    .dropdown-content a,
    #dropdownContent a,
    #inlineDropdown a,
    #responsive-dropdown a {
        transition: none !important;
        animation: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .dropdown-toggle button,
    #dropdownToggle button {
        border: 2px solid #00d4ff !important;
    }
    
    .dropdown-content,
    #dropdownContent,
    #inlineDropdown,
    #responsive-dropdown {
        border: 3px solid #00d4ff !important;
    }
}
