/**
 * Game Layout Fixes
 * Prevents content jumping and fixes layout issues
 */

/* Force proper box sizing everywhere */
*, *::before, *::after {
    box-sizing: border-box !important;
}

/* Body and html fixes */
html, body {
    overflow-x: hidden;
    max-width: 100vw;
    margin: 0;
    padding: 0;
}

/* Game frame container */
#game-box {
    position: relative !important;
    width: 100% !important;
    max-width: 1136px !important;
    margin: 0 auto !important;
    background: #000 !important;
    border-radius: 10px !important;
    overflow: hidden !important;
    aspect-ratio: 16/10;
    min-height: 400px;
}

/* Game iframe */
.game-iframe {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    border: none !important;
    display: none; /* Initially hidden */
}

/* Play button with placeholder dimensions */
.play-button {
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    min-width: 200px !important;
    min-height: 80px !important;
    background: linear-gradient(45deg, #00d4ff, #0099cc) !important;
    border: none !important;
    border-radius: 15px !important;
    color: #fff !important;
    cursor: pointer !important;
    z-index: 10 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Allow JavaScript to hide the play button - Higher specificity */
.play-button[style*="display: none"],
.play-button.hidden,
.play-button[style*="display:none"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
}

/* Game controls container */
.game-controls-box {
    width: 100% !important;
    max-width: 1136px !important;
    margin: 1.5rem auto !important;
    padding: 1.5rem !important;
    background: rgba(0, 0, 0, 0.8) !important;
    border-radius: 10px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    min-height: 70px !important;
}

/* Content grid layout */
.content-grid {
    display: grid !important;
    grid-template-columns: 2fr 1fr !important;
    gap: 2rem !important;
    width: 100% !important;
    max-width: 1200px !important;
    margin: 0 auto 2rem auto !important;
    padding: 0 1rem !important;
}

/* Game description container */
.game-description {
    background: rgba(0, 0, 0, 0.8) !important;
    padding: 2rem !important;
    border-radius: 10px !important;
    overflow: hidden !important;
    word-wrap: break-word !important;
    min-height: 200px !important;
}

/* Game content overflow handling */
.game-content {
    width: 100% !important;
    max-width: 100% !important;
    overflow: hidden !important;
    word-wrap: break-word !important;
}

.game-content img,
.game-content iframe,
.game-content video {
    max-width: 100% !important;
    height: auto !important;
}

/* Popular games sidebar */
.popular-games {
    background: rgba(0, 0, 0, 0.8) !important;
    padding: 2rem !important;
    border-radius: 10px !important;
    height: fit-content !important;
    position: sticky !important;
    top: 20px !important;
}

/* Latest games section */
.latest-games-section {
    width: 100% !important;
    max-width: 1200px !important;
    margin: 3rem auto !important;
    padding: 0 1rem !important;
}

/* Responsive fixes */
@media (max-width: 768px) {
    .content-grid {
        grid-template-columns: 1fr !important;
    }

    #game-box {
        aspect-ratio: 16/9;
        min-height: 300px;
    }

    .game-controls-box {
        flex-direction: column !important;
        text-align: center !important;
    }

    .game-action-buttons {
        width: 100% !important;
        justify-content: center !important;
    }

    .game-description,
    .popular-games {
        padding: 1.5rem !important;
    }
}

@media (max-width: 480px) {
    #game-box {
        min-height: 200px;
    }

    .game-description,
    .popular-games {
        padding: 1rem !important;
    }

    .content-grid {
        gap: 1rem !important;
        padding: 0 0.5rem !important;
    }

    .game-controls-box {
        padding: 1rem !important;
        margin: 1rem auto !important;
    }
}

/* Hide all duplicate content */
.game-content > h1,
.game-content > h2:first-of-type,
.game-content > h2:first-of-type + p {
    display: none !important;
}

/* Hide repeated game descriptions */
.game-content > p:first-of-type:has(+ h2),
.game-content > h2:has(+ p:last-of-type) {
    display: none !important;
}

/* Ensure proper spacing */
.game-content > *:first-child {
    margin-top: 0 !important;
}

.game-content > *:last-child {
    margin-bottom: 0 !important;
}