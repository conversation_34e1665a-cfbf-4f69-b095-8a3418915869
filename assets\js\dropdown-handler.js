/**
 * Dedicated Dropdown Handler
 * This script ensures the dropdown toggle functionality works correctly
 */

(function() {
    'use strict';
    
    // Initialize when the DOM is loaded
    document.addEventListener('DOMContentLoaded', initializeDropdown);
    window.addEventListener('load', initializeDropdown);
    
    // In case the page is already loaded
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        initializeDropdown();
    }
    
    function initializeDropdown() {
        console.log('Initializing dropdown functionality');
        
        // Find dropdown elements
        const dropdownToggle = document.getElementById('dropdownToggle');
        const dropdownButton = dropdownToggle ? dropdownToggle.querySelector('button') : null;
        const dropdownContent = document.getElementById('dropdownContent');
        
        if (!dropdownToggle || !dropdownButton || !dropdownContent) {
            console.warn('Dropdown elements not found, will try again later');
            setTimeout(initializeDropdown, 500);
            return;
        }
        
        console.log('Dropdown elements found, setting up event handlers');
        
        // Remove any existing handlers to avoid duplicates
        dropdownButton.removeAttribute('onclick');
        
        // Add click handler to button
        dropdownButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            toggleDropdown();
            return false;
        });
        
        // Initialize dropdown state
        dropdownContent.style.display = 'none';
        dropdownContent.style.visibility = 'hidden';
        dropdownContent.style.opacity = '0';
        
        console.log('Dropdown initialization complete');
    }
    
    // Toggle dropdown visibility
    function toggleDropdown() {
        console.log('Toggle dropdown called');
        
        const dropdownToggle = document.getElementById('dropdownToggle');
        const dropdownContent = document.getElementById('dropdownContent');
        
        if (!dropdownContent) {
            console.error('Dropdown content not found');
            return;
        }
        
        const isVisible = dropdownContent.classList.contains('show');
        
        // Toggle visibility
        if (isVisible) {
            // Hide
            dropdownContent.classList.remove('show');
            dropdownContent.style.display = 'none';
            dropdownContent.style.visibility = 'hidden';
            dropdownContent.style.opacity = '0';
            dropdownToggle.classList.remove('active');
            console.log('Dropdown hidden');
        } else {
            // Show
            dropdownContent.classList.add('show');
            dropdownContent.style.display = 'block';
            dropdownContent.style.visibility = 'visible';
            dropdownContent.style.opacity = '1';
            dropdownToggle.classList.add('active');
            console.log('Dropdown shown');
        }
    }
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        const dropdownToggle = document.getElementById('dropdownToggle');
        const dropdownContent = document.getElementById('dropdownContent');
        
        if (dropdownContent && dropdownToggle && 
            !dropdownToggle.contains(event.target) && 
            dropdownContent.classList.contains('show')) {
            
            dropdownContent.classList.remove('show');
            dropdownContent.style.display = 'none';
            dropdownContent.style.visibility = 'hidden';
            dropdownContent.style.opacity = '0';
            dropdownToggle.classList.remove('active');
        }
    });
    
    // Make function available globally
    window.toggleDropdown = toggleDropdown;
})();