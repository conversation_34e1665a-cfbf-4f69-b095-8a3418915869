<?php
/**
 * Automatic Sitemap Generator for Theme 27
 * Generates comprehensive XML sitemaps for SEO optimization
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize sitemap functionality
 */
function d27_init_sitemap() {
    // Add rewrite rules for sitemap URLs
    add_action('init', 'd27_add_sitemap_rewrite_rules');
    
    // Handle sitemap requests
    add_action('template_redirect', 'd27_handle_sitemap_request');
    
    // Auto-generate sitemap on post save/update
    add_action('save_post', 'd27_regenerate_sitemap_on_save');
    add_action('delete_post', 'd27_regenerate_sitemap_on_save');
    
    // Auto-generate sitemap on theme activation
    add_action('after_switch_theme', 'd27_generate_initial_sitemap');
    
    // Add sitemap to robots.txt
    add_filter('robots_txt', 'd27_add_sitemap_to_robots', 10, 2);
}
add_action('init', 'd27_init_sitemap');

/**
 * Add rewrite rules for sitemap URLs
 */
function d27_add_sitemap_rewrite_rules() {
    // Main sitemap index
    add_rewrite_rule('^sitemap\.xml$', 'index.php?d27_sitemap=index', 'top');
    
    // Individual sitemaps
    add_rewrite_rule('^sitemap-([^/]+)\.xml$', 'index.php?d27_sitemap=$matches[1]', 'top');
    
    // Add query vars
    add_filter('query_vars', function($vars) {
        $vars[] = 'd27_sitemap';
        return $vars;
    });
}

/**
 * Handle sitemap requests
 */
function d27_handle_sitemap_request() {
    $sitemap_type = get_query_var('d27_sitemap');
    
    if (!$sitemap_type) {
        return;
    }
    
    // Set XML headers
    header('Content-Type: application/xml; charset=UTF-8');
    header('X-Robots-Tag: noindex, follow');
    
    switch ($sitemap_type) {
        case 'index':
            d27_generate_sitemap_index();
            break;
        case 'posts':
            d27_generate_posts_sitemap();
            break;
        case 'games':
            d27_generate_games_sitemap();
            break;
        case 'pages':
            d27_generate_pages_sitemap();
            break;
        case 'categories':
            d27_generate_categories_sitemap();
            break;
        case 'tags':
            d27_generate_tags_sitemap();
            break;
        case 'images':
            d27_generate_images_sitemap();
            break;
        default:
            status_header(404);
            exit;
    }
    
    exit;
}

/**
 * Generate main sitemap index
 */
function d27_generate_sitemap_index() {
    $sitemaps = array();
    
    // Add individual sitemaps with last modified dates
    $sitemaps[] = array(
        'loc' => home_url('/sitemap-posts.xml'),
        'lastmod' => d27_get_latest_post_date('post')
    );
    
    $sitemaps[] = array(
        'loc' => home_url('/sitemap-games.xml'),
        'lastmod' => d27_get_latest_post_date('game')
    );
    
    $sitemaps[] = array(
        'loc' => home_url('/sitemap-pages.xml'),
        'lastmod' => d27_get_latest_post_date('page')
    );
    
    $sitemaps[] = array(
        'loc' => home_url('/sitemap-categories.xml'),
        'lastmod' => d27_get_latest_taxonomy_date('category')
    );
    
    $sitemaps[] = array(
        'loc' => home_url('/sitemap-tags.xml'),
        'lastmod' => d27_get_latest_taxonomy_date('game_tag')
    );
    
    $sitemaps[] = array(
        'loc' => home_url('/sitemap-images.xml'),
        'lastmod' => d27_get_latest_image_date()
    );
    
    // Generate XML
    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
    
    foreach ($sitemaps as $sitemap) {
        echo "\t<sitemap>\n";
        echo "\t\t<loc>" . esc_url($sitemap['loc']) . "</loc>\n";
        if ($sitemap['lastmod']) {
            echo "\t\t<lastmod>" . esc_html($sitemap['lastmod']) . "</lastmod>\n";
        }
        echo "\t</sitemap>\n";
    }
    
    echo '</sitemapindex>';
}

/**
 * Generate posts sitemap
 */
function d27_generate_posts_sitemap() {
    $posts = get_posts(array(
        'post_type' => 'post',
        'post_status' => 'publish',
        'numberposts' => -1,
        'orderby' => 'modified',
        'order' => 'DESC'
    ));

    // Filter out any malicious posts
    $posts = d27_filter_malicious_content($posts);

    d27_output_urlset_sitemap($posts, 'post');
}

/**
 * Generate games sitemap
 */
function d27_generate_games_sitemap() {
    $games = get_posts(array(
        'post_type' => 'game',
        'post_status' => 'publish',
        'numberposts' => -1,
        'orderby' => 'modified',
        'order' => 'DESC'
    ));

    // Filter out any malicious games
    $games = d27_filter_malicious_content($games);

    d27_output_urlset_sitemap($games, 'game');
}

/**
 * Generate pages sitemap
 */
function d27_generate_pages_sitemap() {
    $pages = get_posts(array(
        'post_type' => 'page',
        'post_status' => 'publish',
        'numberposts' => -1,
        'orderby' => 'modified',
        'order' => 'DESC'
    ));

    // Filter out any malicious pages
    $pages = d27_filter_malicious_content($pages);

    // Add homepage
    $homepage_url = array(
        'loc' => home_url('/'),
        'lastmod' => d27_get_latest_post_date('game'), // Use latest game as homepage changes
        'changefreq' => 'daily',
        'priority' => '1.0'
    );

    d27_output_urlset_sitemap($pages, 'page', array($homepage_url));
}

/**
 * Generate categories sitemap
 */
function d27_generate_categories_sitemap() {
    $categories = get_terms(array(
        'taxonomy' => 'category',
        'hide_empty' => true
    ));
    
    d27_output_taxonomy_sitemap($categories);
}

/**
 * Generate tags sitemap
 */
function d27_generate_tags_sitemap() {
    $tags = get_terms(array(
        'taxonomy' => 'game_tag',
        'hide_empty' => true
    ));
    
    d27_output_taxonomy_sitemap($tags);
}

/**
 * Generate images sitemap
 */
function d27_generate_images_sitemap() {
    // Get all published posts with featured images
    $posts_with_images = get_posts(array(
        'post_type' => array('post', 'game', 'page'),
        'post_status' => 'publish',
        'numberposts' => -1,
        'meta_query' => array(
            array(
                'key' => '_thumbnail_id',
                'compare' => 'EXISTS'
            )
        )
    ));
    
    // Get all attachment images
    $images = get_posts(array(
        'post_type' => 'attachment',
        'post_mime_type' => 'image',
        'post_status' => 'inherit',
        'numberposts' => -1
    ));
    
    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">' . "\n";
    
    // Add images from posts
    foreach ($posts_with_images as $post) {
        $thumbnail_id = get_post_thumbnail_id($post->ID);
        if ($thumbnail_id) {
            $image_url = wp_get_attachment_image_url($thumbnail_id, 'full');
            $image_title = get_the_title($post->ID);
            $image_caption = wp_get_attachment_caption($thumbnail_id);
            
            echo "\t<url>\n";
            echo "\t\t<loc>" . esc_url(get_permalink($post->ID)) . "</loc>\n";
            echo "\t\t<image:image>\n";
            echo "\t\t\t<image:loc>" . esc_url($image_url) . "</image:loc>\n";
            echo "\t\t\t<image:title>" . esc_html($image_title) . "</image:title>\n";
            if ($image_caption) {
                echo "\t\t\t<image:caption>" . esc_html($image_caption) . "</image:caption>\n";
            }
            echo "\t\t</image:image>\n";
            echo "\t</url>\n";
        }
    }
    
    echo '</urlset>';
}

/**
 * Output standard urlset sitemap
 */
function d27_output_urlset_sitemap($posts, $post_type, $additional_urls = array()) {
    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
    
    // Add additional URLs first (like homepage)
    foreach ($additional_urls as $url) {
        echo "\t<url>\n";
        echo "\t\t<loc>" . esc_url($url['loc']) . "</loc>\n";
        echo "\t\t<lastmod>" . esc_html($url['lastmod']) . "</lastmod>\n";
        echo "\t\t<changefreq>" . esc_html($url['changefreq']) . "</changefreq>\n";
        echo "\t\t<priority>" . esc_html($url['priority']) . "</priority>\n";
        echo "\t</url>\n";
    }
    
    // Add posts
    foreach ($posts as $post) {
        $priority = d27_get_post_priority($post, $post_type);
        $changefreq = d27_get_post_changefreq($post, $post_type);
        
        echo "\t<url>\n";
        echo "\t\t<loc>" . esc_url(get_permalink($post->ID)) . "</loc>\n";
        echo "\t\t<lastmod>" . esc_html(get_the_modified_date('c', $post->ID)) . "</lastmod>\n";
        echo "\t\t<changefreq>" . esc_html($changefreq) . "</changefreq>\n";
        echo "\t\t<priority>" . esc_html($priority) . "</priority>\n";
        echo "\t</url>\n";
    }
    
    echo '</urlset>';
}

/**
 * Output taxonomy sitemap
 */
function d27_output_taxonomy_sitemap($terms) {
    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

    foreach ($terms as $term) {
        echo "\t<url>\n";
        echo "\t\t<loc>" . esc_url(get_term_link($term)) . "</loc>\n";
        echo "\t\t<lastmod>" . esc_html(d27_get_term_last_modified($term)) . "</lastmod>\n";
        echo "\t\t<changefreq>weekly</changefreq>\n";
        echo "\t\t<priority>0.6</priority>\n";
        echo "\t</url>\n";
    }

    echo '</urlset>';
}

/**
 * Get post priority based on type and recency
 */
function d27_get_post_priority($post, $post_type) {
    $days_old = (time() - strtotime($post->post_date)) / (60 * 60 * 24);

    switch ($post_type) {
        case 'game':
            if ($days_old < 7) return '0.9';
            if ($days_old < 30) return '0.8';
            return '0.7';
        case 'post':
            if ($days_old < 7) return '0.8';
            if ($days_old < 30) return '0.7';
            return '0.6';
        case 'page':
            return '0.8';
        default:
            return '0.5';
    }
}

/**
 * Get post change frequency
 */
function d27_get_post_changefreq($post, $post_type) {
    $days_since_modified = (time() - strtotime($post->post_modified)) / (60 * 60 * 24);

    switch ($post_type) {
        case 'game':
            if ($days_since_modified < 7) return 'daily';
            if ($days_since_modified < 30) return 'weekly';
            return 'monthly';
        case 'post':
            if ($days_since_modified < 7) return 'weekly';
            return 'monthly';
        case 'page':
            return 'monthly';
        default:
            return 'yearly';
    }
}

/**
 * Get latest post date for a post type
 */
function d27_get_latest_post_date($post_type) {
    $latest_post = get_posts(array(
        'post_type' => $post_type,
        'post_status' => 'publish',
        'numberposts' => 1,
        'orderby' => 'modified',
        'order' => 'DESC'
    ));

    if ($latest_post) {
        return get_the_modified_date('c', $latest_post[0]->ID);
    }

    return date('c');
}

/**
 * Get latest taxonomy date
 */
function d27_get_latest_taxonomy_date($taxonomy) {
    $terms = get_terms(array(
        'taxonomy' => $taxonomy,
        'hide_empty' => true,
        'number' => 1,
        'orderby' => 'count',
        'order' => 'DESC'
    ));

    if ($terms && !is_wp_error($terms)) {
        return d27_get_term_last_modified($terms[0]);
    }

    return date('c');
}

/**
 * Get latest image date
 */
function d27_get_latest_image_date() {
    $latest_image = get_posts(array(
        'post_type' => 'attachment',
        'post_mime_type' => 'image',
        'post_status' => 'inherit',
        'numberposts' => 1,
        'orderby' => 'modified',
        'order' => 'DESC'
    ));

    if ($latest_image) {
        return get_the_modified_date('c', $latest_image[0]->ID);
    }

    return date('c');
}

/**
 * Get term last modified date
 */
function d27_get_term_last_modified($term) {
    // Get latest post in this term
    $latest_post = get_posts(array(
        'post_type' => array('post', 'game'),
        'post_status' => 'publish',
        'numberposts' => 1,
        'orderby' => 'modified',
        'order' => 'DESC',
        'tax_query' => array(
            array(
                'taxonomy' => $term->taxonomy,
                'field' => 'term_id',
                'terms' => $term->term_id
            )
        )
    ));

    if ($latest_post) {
        return get_the_modified_date('c', $latest_post[0]->ID);
    }

    return date('c');
}

/**
 * Regenerate sitemap when posts are saved/deleted
 */
function d27_regenerate_sitemap_on_save($post_id) {
    // Only regenerate for public post types
    $post_type = get_post_type($post_id);
    if (!in_array($post_type, array('post', 'game', 'page'))) {
        return;
    }

    // Clear any sitemap cache if you implement caching
    delete_transient('d27_sitemap_cache');

    // Flush rewrite rules to ensure sitemap URLs work
    flush_rewrite_rules(false);
}

/**
 * Generate initial sitemap on theme activation
 */
function d27_generate_initial_sitemap() {
    // Flush rewrite rules to add sitemap URLs
    flush_rewrite_rules(false);

    // Clear any existing cache
    delete_transient('d27_sitemap_cache');
}

/**
 * Add sitemap to robots.txt
 */
function d27_add_sitemap_to_robots($output, $public) {
    if ($public) {
        $output .= "\n# Sitemap\n";
        $output .= "Sitemap: " . home_url('/sitemap.xml') . "\n";
    }

    return $output;
}

/**
 * Add sitemap links to admin bar
 */
function d27_add_sitemap_admin_bar($wp_admin_bar) {
    if (!current_user_can('manage_options')) {
        return;
    }

    $wp_admin_bar->add_node(array(
        'id' => 'd27-sitemap',
        'title' => 'View Sitemap',
        'href' => home_url('/sitemap.xml'),
        'meta' => array(
            'target' => '_blank'
        )
    ));
}
add_action('admin_bar_menu', 'd27_add_sitemap_admin_bar', 100);

/**
 * Add sitemap settings to customizer
 */
function d27_add_sitemap_customizer($wp_customize) {
    // Add sitemap section
    $wp_customize->add_section('d27_sitemap_settings', array(
        'title' => 'Sitemap Settings',
        'description' => 'Configure automatic sitemap generation',
        'panel' => 'd27_seo_panel'
    ));

    // Enable/disable sitemap
    $wp_customize->add_setting('d27_sitemap_enabled', array(
        'default' => true,
        'sanitize_callback' => 'wp_validate_boolean'
    ));

    $wp_customize->add_control('d27_sitemap_enabled', array(
        'label' => 'Enable Automatic Sitemap',
        'description' => 'Generate XML sitemaps automatically',
        'section' => 'd27_sitemap_settings',
        'type' => 'checkbox'
    ));

    // Sitemap info
    $wp_customize->add_setting('d27_sitemap_info', array(
        'sanitize_callback' => 'sanitize_text_field'
    ));

    $wp_customize->add_control(new WP_Customize_Control($wp_customize, 'd27_sitemap_info', array(
        'label' => 'Sitemap URL',
        'description' => 'Your sitemap is available at: ' . home_url('/sitemap.xml'),
        'section' => 'd27_sitemap_settings',
        'type' => 'hidden'
    )));
}
add_action('customize_register', 'd27_add_sitemap_customizer');

/**
 * Filter malicious content from sitemap
 */
function d27_filter_malicious_content($posts) {
    if (empty($posts)) {
        return $posts;
    }

    $malicious_patterns = array(
        'geometrydashfullversion.org',
        'shopdetail',
        'malware',
        'virus',
        'trojan',
        'phishing',
        'casino',
        'pharmacy',
        'viagra',
        'cialis',
        'porn',
        'xxx',
        'adult',
        'escort',
        'dating',
        'loan',
        'mortgage',
        'insurance',
        'replica',
        'fake',
        'weight.*loss',
        'diet.*pill',
        'make.*money',
        'work.*home',
        'earn.*money',
        'get.*rich',
        'free.*money',
        'lottery',
        'winner',
        'prize',
        'click.*here',
        'buy.*now',
        'limited.*time',
        'act.*now',
        'urgent',
        'guaranteed',
        'risk.*free',
        'call.*now',
        'order.*now',
        'download.*now',
        'install.*now',
        'update.*now',
        'verify.*account',
        'confirm.*identity',
        'suspended.*account',
        'security.*alert',
        'unusual.*activity'
    );

    $suspicious_domains = array(
        'geometrydashfullversion.org',
        'bit.ly',
        'tinyurl.com',
        'goo.gl',
        't.co',
        'ow.ly',
        'short.link',
        'tiny.cc',
        'is.gd',
        'buff.ly',
        'adf.ly',
        'linkbucks.com',
        'shorte.st',
        'ouo.io',
        'bc.vc',
        'sh.st',
        'link.tl',
        'clk.sh',
        'exe.io',
        'sub2unlock.com',
        'adfly.com',
        'linkvertise.com'
    );

    $clean_posts = array();

    foreach ($posts as $post) {
        $is_malicious = false;
        $content_lower = strtolower($post->post_content);
        $title_lower = strtolower($post->post_title);
        $url_lower = strtolower(get_permalink($post->ID));

        // Check for malicious patterns
        foreach ($malicious_patterns as $pattern) {
            if (preg_match('/' . $pattern . '/i', $content_lower) ||
                preg_match('/' . $pattern . '/i', $title_lower) ||
                preg_match('/' . $pattern . '/i', $url_lower)) {
                $is_malicious = true;

                // Log the malicious content for admin review
                error_log("D27 Sitemap: Blocked malicious content from sitemap - Post ID: {$post->ID}, Title: {$post->post_title}, Pattern: {$pattern}");
                break;
            }
        }

        // Check for suspicious domains
        if (!$is_malicious) {
            foreach ($suspicious_domains as $domain) {
                if (strpos($content_lower, $domain) !== false ||
                    strpos($title_lower, $domain) !== false ||
                    strpos($url_lower, $domain) !== false) {
                    $is_malicious = true;

                    // Log the malicious content
                    error_log("D27 Sitemap: Blocked suspicious domain from sitemap - Post ID: {$post->ID}, Title: {$post->post_title}, Domain: {$domain}");
                    break;
                }
            }
        }

        // Check for suspicious URL patterns
        if (!$is_malicious && preg_match('/shopdetail\/\d+/i', $url_lower)) {
            $is_malicious = true;
            error_log("D27 Sitemap: Blocked suspicious URL pattern from sitemap - Post ID: {$post->ID}, URL: {$url_lower}");
        }

        // Check for external redirects in content
        if (!$is_malicious && preg_match('/http[s]?:\/\/(?!.*' . preg_quote($_SERVER['HTTP_HOST'], '/') . ')/i', $post->post_content)) {
            $is_malicious = true;
            error_log("D27 Sitemap: Blocked external redirect from sitemap - Post ID: {$post->ID}, Title: {$post->post_title}");
        }

        // Only include clean content in sitemap
        if (!$is_malicious) {
            $clean_posts[] = $post;
        }
    }

    return $clean_posts;
}

/**
 * Ping search engines when sitemap updates
 */
function d27_ping_search_engines() {
    if (!get_theme_mod('d27_sitemap_enabled', true)) {
        return;
    }

    $sitemap_url = home_url('/sitemap.xml');

    // Ping Google
    $google_ping = 'https://www.google.com/ping?sitemap=' . urlencode($sitemap_url);
    wp_remote_get($google_ping, array('timeout' => 5));

    // Ping Bing
    $bing_ping = 'https://www.bing.com/ping?sitemap=' . urlencode($sitemap_url);
    wp_remote_get($bing_ping, array('timeout' => 5));
}

// Ping search engines when posts are published
add_action('transition_post_status', function($new_status, $old_status, $post) {
    if ($new_status === 'publish' && $old_status !== 'publish') {
        wp_schedule_single_event(time() + 60, 'd27_ping_search_engines');
    }
}, 10, 3);

add_action('d27_ping_search_engines', 'd27_ping_search_engines');
