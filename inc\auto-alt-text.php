<?php
/**
 * D27 Gaming Theme Automatic Alt Text Generator
 * Automatically generates SEO-optimized alt text for images
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class D27_Auto_Alt_Text {
    
    public function __construct() {
        // Hook into image upload process
        add_action('add_attachment', array($this, 'generate_alt_text_on_upload'));
        
        // Hook into image display to ensure alt text is present
        add_filter('wp_get_attachment_image_attributes', array($this, 'ensure_alt_text'), 10, 3);
        add_filter('get_image_tag', array($this, 'add_alt_to_image_tag'), 10, 6);
        
        // Hook into post thumbnail to add alt text
        add_filter('post_thumbnail_html', array($this, 'add_alt_to_thumbnail'), 10, 5);
        
        // Add admin interface
        add_action('admin_menu', array($this, 'add_admin_menu'));
    }
    
    /**
     * Generate alt text when image is uploaded
     */
    public function generate_alt_text_on_upload($attachment_id) {
        $attachment = get_post($attachment_id);
        
        if (!$attachment || $attachment->post_type !== 'attachment') {
            return;
        }
        
        // Skip if alt text already exists
        $existing_alt = get_post_meta($attachment_id, '_wp_attachment_image_alt', true);
        if (!empty($existing_alt)) {
            return;
        }
        
        $alt_text = $this->generate_alt_text($attachment_id);
        
        if ($alt_text) {
            update_post_meta($attachment_id, '_wp_attachment_image_alt', $alt_text);
        }
    }
    
    /**
     * Ensure alt text is present when image is displayed
     */
    public function ensure_alt_text($attr, $attachment, $size) {
        if (empty($attr['alt'])) {
            $alt_text = get_post_meta($attachment->ID, '_wp_attachment_image_alt', true);
            
            if (empty($alt_text)) {
                $alt_text = $this->generate_alt_text($attachment->ID);
                if ($alt_text) {
                    update_post_meta($attachment->ID, '_wp_attachment_image_alt', $alt_text);
                }
            }
            
            $attr['alt'] = $alt_text ?: $this->get_fallback_alt_text($attachment->ID);
        }
        
        return $attr;
    }
    
    /**
     * Add alt text to image tags
     */
    public function add_alt_to_image_tag($html, $id, $alt, $title, $align, $size) {
        if (empty($alt)) {
            $alt_text = get_post_meta($id, '_wp_attachment_image_alt', true);
            
            if (empty($alt_text)) {
                $alt_text = $this->generate_alt_text($id);
                if ($alt_text) {
                    update_post_meta($id, '_wp_attachment_image_alt', $alt_text);
                }
            }
            
            $alt = $alt_text ?: $this->get_fallback_alt_text($id);
            $html = str_replace('<img', '<img alt="' . esc_attr($alt) . '"', $html);
        }
        
        return $html;
    }
    
    /**
     * Add alt text to post thumbnails
     */
    public function add_alt_to_thumbnail($html, $post_id, $post_thumbnail_id, $size, $attr) {
        if (strpos($html, 'alt=') === false || strpos($html, 'alt=""') !== false) {
            $alt_text = get_post_meta($post_thumbnail_id, '_wp_attachment_image_alt', true);
            
            if (empty($alt_text)) {
                $alt_text = $this->generate_alt_text($post_thumbnail_id, $post_id);
                if ($alt_text) {
                    update_post_meta($post_thumbnail_id, '_wp_attachment_image_alt', $alt_text);
                }
            }
            
            $alt = $alt_text ?: $this->get_fallback_alt_text($post_thumbnail_id, $post_id);
            
            // Replace empty alt or add alt if missing
            if (strpos($html, 'alt=""') !== false) {
                $html = str_replace('alt=""', 'alt="' . esc_attr($alt) . '"', $html);
            } elseif (strpos($html, 'alt=') === false) {
                $html = str_replace('<img', '<img alt="' . esc_attr($alt) . '"', $html);
            }
        }
        
        return $html;
    }
    
    /**
     * Generate intelligent alt text based on context
     */
    public function generate_alt_text($attachment_id, $post_id = null) {
        $attachment = get_post($attachment_id);
        if (!$attachment) {
            return '';
        }
        
        // Get image filename and clean it
        $filename = pathinfo($attachment->post_title, PATHINFO_FILENAME);
        $clean_filename = $this->clean_filename($filename);
        
        // Get parent post context if available
        $parent_post = null;
        if ($post_id) {
            $parent_post = get_post($post_id);
        } elseif ($attachment->post_parent) {
            $parent_post = get_post($attachment->post_parent);
        }
        
        // Generate context-aware alt text
        $alt_text = '';
        
        if ($parent_post && $parent_post->post_type === 'game') {
            // Game-related image
            $game_title = $parent_post->post_title;
            
            // Check if it's a thumbnail/featured image
            if (get_post_thumbnail_id($parent_post->ID) == $attachment_id) {
                $alt_text = $game_title . ' - Play Free Online Game';
            } else {
                $alt_text = $game_title . ' Game Screenshot';
            }
            
            // Add game tags for context
            $game_tags = get_the_terms($parent_post->ID, 'game_tag');
            if ($game_tags && !is_wp_error($game_tags)) {
                $tag_names = wp_list_pluck($game_tags, 'name');
                $primary_tag = $tag_names[0];
                $alt_text .= ' - ' . $primary_tag . ' Game';
            }
            
        } elseif ($parent_post) {
            // Other post types
            $alt_text = $parent_post->post_title;
            
            if ($clean_filename && $clean_filename !== $parent_post->post_title) {
                $alt_text .= ' - ' . $clean_filename;
            }
            
        } else {
            // Standalone image
            if ($clean_filename) {
                $alt_text = $clean_filename;
                
                // Add gaming context if filename suggests it's game-related
                if (preg_match('/\b(game|play|dash|geometry|level|arcade|puzzle|action|adventure|platform)\b/i', $clean_filename)) {
                    $alt_text .= ' - Free Online Game';
                }
            }
        }
        
        // Add site branding for SEO
        if ($alt_text) {
            $site_name = get_bloginfo('name');
            if ($site_name && strpos($alt_text, $site_name) === false) {
                $alt_text .= ' | ' . $site_name;
            }
        }
        
        // Clean and limit length
        $alt_text = $this->clean_alt_text($alt_text);
        
        return $alt_text;
    }
    
    /**
     * Get fallback alt text when generation fails
     */
    public function get_fallback_alt_text($attachment_id, $post_id = null) {
        $attachment = get_post($attachment_id);
        if (!$attachment) {
            return 'Image';
        }
        
        // Try to use attachment title
        if ($attachment->post_title) {
            $title = $this->clean_filename($attachment->post_title);
            if ($title) {
                return $title . ' | ' . get_bloginfo('name');
            }
        }
        
        // Use parent post title if available
        if ($post_id) {
            $parent_post = get_post($post_id);
            if ($parent_post) {
                return $parent_post->post_title . ' Image | ' . get_bloginfo('name');
            }
        }
        
        // Generic fallback
        return 'Free Online Game | ' . get_bloginfo('name');
    }
    
    /**
     * Clean filename for use in alt text
     */
    private function clean_filename($filename) {
        // Remove file extensions
        $filename = preg_replace('/\.(jpe?g|png|gif|webp|svg)$/i', '', $filename);
        
        // Replace common separators with spaces
        $filename = preg_replace('/[-_\.]/', ' ', $filename);
        
        // Remove numbers that look like dimensions or IDs
        $filename = preg_replace('/\b\d{2,4}x\d{2,4}\b/', '', $filename);
        $filename = preg_replace('/\b\d{4,}\b/', '', $filename);
        
        // Clean up multiple spaces
        $filename = preg_replace('/\s+/', ' ', $filename);
        
        // Capitalize words
        $filename = ucwords(trim($filename));
        
        return $filename;
    }
    
    /**
     * Clean and optimize alt text
     */
    private function clean_alt_text($alt_text) {
        // Remove extra spaces
        $alt_text = preg_replace('/\s+/', ' ', $alt_text);
        
        // Trim
        $alt_text = trim($alt_text);
        
        // Limit length (recommended max 125 characters for SEO)
        if (strlen($alt_text) > 125) {
            $alt_text = substr($alt_text, 0, 122) . '...';
        }
        
        return $alt_text;
    }
    
    /**
     * Bulk generate alt text for existing images
     */
    public function bulk_generate_alt_text() {
        $args = array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'post_status' => 'inherit',
            'posts_per_page' => -1,
            'meta_query' => array(
                'relation' => 'OR',
                array(
                    'key' => '_wp_attachment_image_alt',
                    'compare' => 'NOT EXISTS'
                ),
                array(
                    'key' => '_wp_attachment_image_alt',
                    'value' => '',
                    'compare' => '='
                )
            )
        );
        
        $attachments = get_posts($args);
        $updated = 0;
        
        foreach ($attachments as $attachment) {
            $alt_text = $this->generate_alt_text($attachment->ID);
            
            if ($alt_text) {
                update_post_meta($attachment->ID, '_wp_attachment_image_alt', $alt_text);
                $updated++;
            }
        }
        
        return $updated;
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_management_page(
            'Auto Alt Text',
            'Auto Alt Text',
            'manage_options',
            'd27-auto-alt-text',
            array($this, 'admin_page')
        );
    }
    
    /**
     * Admin page
     */
    public function admin_page() {
        if (isset($_POST['bulk_generate']) && wp_verify_nonce($_POST['alt_nonce'], 'd27_bulk_alt')) {
            $updated = $this->bulk_generate_alt_text();
            echo '<div class="notice notice-success"><p>Updated alt text for ' . $updated . ' images.</p></div>';
        }
        
        // Get statistics
        $total_images = wp_count_posts('attachment');
        $images_with_alt = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'post_status' => 'inherit',
            'posts_per_page' => -1,
            'fields' => 'ids',
            'meta_query' => array(
                array(
                    'key' => '_wp_attachment_image_alt',
                    'value' => '',
                    'compare' => '!='
                )
            )
        ));
        
        $images_without_alt = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'post_status' => 'inherit',
            'posts_per_page' => -1,
            'fields' => 'ids',
            'meta_query' => array(
                'relation' => 'OR',
                array(
                    'key' => '_wp_attachment_image_alt',
                    'compare' => 'NOT EXISTS'
                ),
                array(
                    'key' => '_wp_attachment_image_alt',
                    'value' => '',
                    'compare' => '='
                )
            )
        ));
        
        ?>
        <div class="wrap">
            <h1>🖼️ Automatic Alt Text Generator</h1>
            
            <div class="card">
                <h2>Image Statistics</h2>
                <table class="form-table">
                    <tr>
                        <th>Total Images</th>
                        <td><?php echo number_format(count($images_with_alt) + count($images_without_alt)); ?></td>
                    </tr>
                    <tr>
                        <th>Images with Alt Text</th>
                        <td style="color: green;"><?php echo number_format(count($images_with_alt)); ?></td>
                    </tr>
                    <tr>
                        <th>Images without Alt Text</th>
                        <td style="color: red;"><?php echo number_format(count($images_without_alt)); ?></td>
                    </tr>
                    <tr>
                        <th>Coverage</th>
                        <td>
                            <?php 
                            $total = count($images_with_alt) + count($images_without_alt);
                            $coverage = $total > 0 ? round((count($images_with_alt) / $total) * 100, 1) : 0;
                            echo $coverage . '%';
                            ?>
                        </td>
                    </tr>
                </table>
            </div>
            
            <div class="card">
                <h2>Bulk Generate Alt Text</h2>
                <p>Generate SEO-optimized alt text for all images that don't have alt text.</p>
                
                <form method="post">
                    <?php wp_nonce_field('d27_bulk_alt', 'alt_nonce'); ?>
                    <p>
                        <input type="submit" name="bulk_generate" class="button button-primary" 
                               value="Generate Alt Text for <?php echo count($images_without_alt); ?> Images"
                               onclick="return confirm('Generate alt text for all images without alt text?');">
                    </p>
                </form>
            </div>
            
            <div class="card">
                <h2>How It Works</h2>
                <ul>
                    <li><strong>Game Images:</strong> "Game Title - Play Free Online Game | Site Name"</li>
                    <li><strong>Game Screenshots:</strong> "Game Title Game Screenshot - Category | Site Name"</li>
                    <li><strong>Context-Aware:</strong> Uses post title, game tags, and filename</li>
                    <li><strong>SEO Optimized:</strong> Includes relevant keywords and site branding</li>
                    <li><strong>Length Optimized:</strong> Keeps alt text under 125 characters</li>
                    <li><strong>Automatic:</strong> New uploads get alt text automatically</li>
                </ul>
            </div>
        </div>
        
        <style>
        .card {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            margin: 20px 0;
            padding: 20px;
            box-shadow: 0 1px 1px rgba(0,0,0,.04);
        }
        .card h2 {
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        </style>
        <?php
    }
}

// Initialize the auto alt text system
new D27_Auto_Alt_Text();
