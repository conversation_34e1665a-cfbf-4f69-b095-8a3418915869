<?php
/**
 * D27 Gaming Theme WebP Admin Interface
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add WebP converter admin menu
 */
function d27_webp_admin_menu() {
    add_management_page(
        'WebP Converter',
        'WebP Converter',
        'manage_options',
        'd27-webp-converter',
        'd27_webp_admin_page'
    );
}
add_action('admin_menu', 'd27_webp_admin_menu');

/**
 * WebP converter admin page
 */
function d27_webp_admin_page() {
    $converter = new D27_WebP_Converter();
    $webp_supported = $converter->is_webp_supported();
    
    // Get statistics
    $total_images = wp_count_posts('attachment');
    $jpeg_png_count = 0;
    $webp_count = 0;
    
    $args = array(
        'post_type' => 'attachment',
        'post_mime_type' => array('image/jpeg', 'image/png'),
        'post_status' => 'inherit',
        'posts_per_page' => -1,
        'fields' => 'ids'
    );
    
    $image_ids = get_posts($args);
    $jpeg_png_count = count($image_ids);
    
    // Count existing WebP files by checking each JPEG/PNG for corresponding WebP
    $webp_count = 0;
    foreach ($image_ids as $image_id) {
        $file_path = get_attached_file($image_id);
        if ($file_path) {
            $webp_path = preg_replace('/\.(jpe?g|png)$/i', '.webp', $file_path);
            if (file_exists($webp_path)) {
                $webp_count++;
            }
        }
    }
    
    ?>
    <div class="wrap">
        <h1>🖼️ WebP Image Converter</h1>
        
        <?php if (isset($_GET['converted'])): ?>
        <div class="notice notice-success is-dismissible">
            <p><strong>Success!</strong> Converted <?php echo intval($_GET['converted']); ?> images to WebP format.</p>
            <p><em>Note: Page statistics will update after refresh. <a href="<?php echo admin_url('tools.php?page=d27-webp-converter'); ?>">Refresh page</a> to see updated counts.</em></p>
        </div>
        <?php endif; ?>
        
        <div class="card">
            <h2>Server Support</h2>
            <table class="form-table">
                <tr>
                    <th scope="row">WebP Support</th>
                    <td>
                        <?php if ($webp_supported): ?>
                            <span style="color: green;">✅ Supported</span>
                        <?php else: ?>
                            <span style="color: red;">❌ Not Supported</span>
                            <p class="description">Your server doesn't support WebP conversion. Contact your hosting provider.</p>
                        <?php endif; ?>
                    </td>
                </tr>
                <tr>
                    <th scope="row">Required Functions</th>
                    <td>
                        <ul>
                            <li>imagewebp(): <?php echo function_exists('imagewebp') ? '✅' : '❌'; ?></li>
                            <li>imagecreatefromjpeg(): <?php echo function_exists('imagecreatefromjpeg') ? '✅' : '❌'; ?></li>
                            <li>imagecreatefrompng(): <?php echo function_exists('imagecreatefrompng') ? '✅' : '❌'; ?></li>
                        </ul>
                    </td>
                </tr>
            </table>
        </div>
        
        <div class="card">
            <h2>Image Statistics</h2>
            <table class="form-table">
                <tr>
                    <th scope="row">Total Attachments</th>
                    <td><?php echo number_format($total_images->inherit); ?></td>
                </tr>
                <tr>
                    <th scope="row">JPEG/PNG Images</th>
                    <td><?php echo number_format($jpeg_png_count); ?></td>
                </tr>
                <tr>
                    <th scope="row">WebP Files</th>
                    <td><?php echo number_format($webp_count); ?></td>
                </tr>
                <tr>
                    <th scope="row">Conversion Rate</th>
                    <td>
                        <?php
                        $conversion_rate = $jpeg_png_count > 0 ? round(($webp_count / $jpeg_png_count) * 100, 1) : 0;
                        echo $conversion_rate . '%';
                        ?>
                    </td>
                </tr>
            </table>

            <?php if (isset($_GET['debug']) && $_GET['debug'] === '1'): ?>
            <h3>Debug Information</h3>
            <div style="background: #f9f9f9; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px;">
                <?php
                echo "<strong>Checking WebP files:</strong><br>";
                $debug_count = 0;
                foreach (array_slice($image_ids, 0, 5) as $image_id) { // Check first 5 images
                    $file_path = get_attached_file($image_id);
                    $webp_path = preg_replace('/\.(jpe?g|png)$/i', '.webp', $file_path);
                    $webp_exists = file_exists($webp_path);
                    $webp_size = $webp_exists ? filesize($webp_path) : 0;

                    echo "Image ID: {$image_id}<br>";
                    echo "Original: " . basename($file_path) . "<br>";
                    echo "WebP: " . basename($webp_path) . " - " . ($webp_exists ? "EXISTS ({$webp_size} bytes)" : "NOT FOUND") . "<br><br>";

                    if ($webp_exists) $debug_count++;
                }
                echo "<strong>WebP files found in sample: {$debug_count}/5</strong><br>";
                ?>
            </div>
            <p><a href="<?php echo admin_url('tools.php?page=d27-webp-converter'); ?>">Hide Debug Info</a></p>
            <?php else: ?>
            <p><a href="<?php echo admin_url('tools.php?page=d27-webp-converter&debug=1'); ?>">Show Debug Info</a></p>
            <?php endif; ?>
        </div>
        
        <?php if ($webp_supported): ?>
        <div class="card">
            <h2>Bulk Conversion</h2>
            <p>Convert all existing JPEG and PNG images to WebP format. This process may take some time depending on the number of images.</p>
            
            <form method="post" action="<?php echo admin_url('admin-post.php'); ?>">
                <?php wp_nonce_field('d27_bulk_webp_convert', 'd27_webp_nonce'); ?>
                <input type="hidden" name="action" value="d27_bulk_webp_convert">
                
                <p>
                    <input type="submit" class="button button-primary" value="Convert All Images to WebP" 
                           onclick="return confirm('This will convert all JPEG and PNG images to WebP. Continue?');">
                </p>
                
                <p class="description">
                    <strong>Note:</strong> Original images will be kept. WebP versions will be served automatically to supported browsers.
                </p>
            </form>
        </div>
        
        <div class="card">
            <h2>How It Works</h2>
            <ul>
                <li><strong>Automatic Conversion:</strong> New uploads are automatically converted to WebP</li>
                <li><strong>Browser Detection:</strong> WebP images are served only to browsers that support them</li>
                <li><strong>Fallback Support:</strong> Original images remain as fallback for unsupported browsers</li>
                <li><strong>Quality:</strong> WebP images are created with 85% quality for optimal size/quality balance</li>
                <li><strong>Transparency:</strong> PNG transparency is preserved in WebP conversion</li>
            </ul>
        </div>
        
        <div class="card">
            <h2>Performance Benefits</h2>
            <ul>
                <li><strong>File Size:</strong> WebP images are typically 25-35% smaller than JPEG</li>
                <li><strong>Loading Speed:</strong> Faster page load times due to smaller file sizes</li>
                <li><strong>SEO Benefits:</strong> Google favors faster-loading websites</li>
                <li><strong>User Experience:</strong> Improved performance on mobile devices</li>
            </ul>
        </div>
        <?php else: ?>
        <div class="card">
            <h2>Enable WebP Support</h2>
            <p>To enable WebP conversion, your server needs to have the GD extension with WebP support compiled. Contact your hosting provider to enable this feature.</p>
            
            <h3>Common Solutions:</h3>
            <ul>
                <li><strong>Shared Hosting:</strong> Contact support to enable GD with WebP</li>
                <li><strong>VPS/Dedicated:</strong> Install php-gd with WebP support</li>
                <li><strong>WordPress.com:</strong> WebP is automatically handled</li>
            </ul>
        </div>
        <?php endif; ?>
    </div>
    
    <style>
    .card {
        background: #fff;
        border: 1px solid #ccd0d4;
        border-radius: 4px;
        margin: 20px 0;
        padding: 20px;
        box-shadow: 0 1px 1px rgba(0,0,0,.04);
    }
    
    .card h2 {
        margin-top: 0;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
    }
    
    .card ul {
        margin: 10px 0;
        padding-left: 20px;
    }
    
    .card li {
        margin: 5px 0;
    }
    </style>
    <?php
}

/**
 * Handle bulk conversion
 */
function d27_handle_bulk_webp_convert() {
    if (!current_user_can('manage_options')) {
        wp_die('Unauthorized');
    }
    
    if (!wp_verify_nonce($_POST['d27_webp_nonce'], 'd27_bulk_webp_convert')) {
        wp_die('Security check failed');
    }
    
    // Redirect to the conversion function
    wp_redirect(admin_url('admin.php?page=d27-webp-converter&action=bulk_convert'));
    exit;
}
add_action('admin_post_d27_bulk_webp_convert', 'd27_handle_bulk_webp_convert');

/**
 * Handle the actual bulk conversion
 */
function d27_process_bulk_conversion() {
    if (!current_user_can('manage_options')) {
        return;
    }
    
    if (isset($_GET['action']) && $_GET['action'] === 'bulk_convert') {
        d27_bulk_convert_to_webp();
    }
}
add_action('admin_init', 'd27_process_bulk_conversion');

/**
 * Add admin notice for WebP conversion
 */
function d27_webp_admin_notice() {
    $screen = get_current_screen();
    
    // Only show on media library and upload pages
    if (!in_array($screen->id, array('upload', 'media'))) {
        return;
    }
    
    $converter = new D27_WebP_Converter();
    if (!$converter->is_webp_supported()) {
        ?>
        <div class="notice notice-warning">
            <p><strong>WebP Conversion:</strong> Your server doesn't support WebP. <a href="<?php echo admin_url('tools.php?page=d27-webp-converter'); ?>">Learn more</a></p>
        </div>
        <?php
    }
}
add_action('admin_notices', 'd27_webp_admin_notice');
