<?php
/**
 * D27 Gaming Theme SEO Meta Tags
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add SEO meta tags to head
 */
function d27_add_seo_meta_tags() {
    global $post;
    
    // Get customizer SEO settings
    $seo_title = get_theme_mod('d27_seo_title', '');
    $seo_description = get_theme_mod('d27_seo_description', '');
    $seo_keywords = get_theme_mod('d27_seo_keywords', 'online games USA, free browser games, web games no download, HTML5 games, American online gaming, US free games, instant play games, geometry dash online');
    
    // Determine page-specific meta data
    $meta_title = '';
    $meta_description = '';
    $meta_keywords = $seo_keywords;
    $meta_image = '';
    $canonical_url = '';
    
    if (is_front_page()) {
        // Homepage - US Market Optimized
        if ($seo_title) {
            $meta_title = $seo_title;
        } else {
            $meta_title = get_bloginfo('name') . ' - Free Online Games USA | Play Browser Games Instantly';
        }

        if ($seo_description) {
            $meta_description = $seo_description;
        } else {
            $meta_description = 'Play thousands of free online games in the USA! No downloads, no registration required. Instant browser games for desktop and mobile. Start playing now!';
        }

        $canonical_url = home_url('/');

        // Get featured game image
        $featured_game_id = get_theme_mod('d27_featured_game', '');
        if ($featured_game_id && has_post_thumbnail($featured_game_id)) {
            $meta_image = get_the_post_thumbnail_url($featured_game_id, 'large');
        }

        // Add US-specific homepage keywords
        $homepage_us_keywords = array(
            'free online games USA',
            'browser games no download',
            'instant play games',
            'web games United States',
            'HTML5 games online',
            'American online gaming',
            'free games for Americans',
            'US browser games',
            'online games no registration',
            'play games instantly',
            'mobile browser games',
            'desktop web games',
            'family friendly games USA',
            'safe online games'
        );
        $meta_keywords = implode(', ', $homepage_us_keywords) . ', ' . $meta_keywords;

    } elseif (is_single() && get_post_type() === 'game') {
        // Single game page
        $meta_title = get_the_title() . ' - Play Free Online Games';
        $meta_description = get_the_excerpt() ?: 'Play ' . get_the_title() . ' online for free. No downloads required!';
        $canonical_url = get_permalink();
        
        if (has_post_thumbnail()) {
            $meta_image = get_the_post_thumbnail_url($post->ID, 'large');
        }
        
        // Add US-optimized game-specific keywords
        $game_tags = get_the_terms($post->ID, 'game_tag');
        $us_gaming_keywords = array(
            'online games USA',
            'free browser games',
            'web games no download',
            'HTML5 games online',
            'instant play games',
            'American online gaming',
            'US free games',
            'browser games United States'
        );

        if ($game_tags && !is_wp_error($game_tags)) {
            $tag_names = wp_list_pluck($game_tags, 'name');
            foreach ($tag_names as $tag) {
                $us_gaming_keywords[] = $tag . ' games online';
                $us_gaming_keywords[] = 'play ' . $tag . ' free';
                $us_gaming_keywords[] = $tag . ' browser game';
                $us_gaming_keywords[] = $tag . ' web game';
            }
            $meta_keywords = implode(', ', array_merge($tag_names, $us_gaming_keywords)) . ', ' . $meta_keywords;
        } else {
            $meta_keywords = implode(', ', $us_gaming_keywords) . ', ' . $meta_keywords;
        }
        
    } elseif (is_post_type_archive('game')) {
        // Games archive
        $meta_title = 'All Games - ' . get_bloginfo('name');
        $meta_description = 'Browse all our amazing Geometry Dash games. Play for free online!';
        $canonical_url = get_post_type_archive_link('game');
        
    } elseif (is_tax('game_tag')) {
        // Game tag archive
        $term = get_queried_object();
        $meta_title = $term->name . ' Games - ' . get_bloginfo('name');
        $meta_description = 'Play ' . $term->name . ' games online for free. ' . ($term->description ?: 'Enjoy amazing gameplay!');
        $canonical_url = get_term_link($term);
        
    } elseif (is_single()) {
        // Regular post
        $meta_title = get_the_title() . ' - ' . get_bloginfo('name');
        $meta_description = get_the_excerpt() ?: wp_trim_words(get_the_content(), 25, '...');
        $canonical_url = get_permalink();
        
        if (has_post_thumbnail()) {
            $meta_image = get_the_post_thumbnail_url($post->ID, 'large');
        }
        
    } elseif (is_page()) {
        // Page
        $meta_title = get_the_title() . ' - ' . get_bloginfo('name');
        $meta_description = get_the_excerpt() ?: wp_trim_words(get_the_content(), 25, '...');
        $canonical_url = get_permalink();
        
    } elseif (is_category()) {
        // Category archive
        $category = get_queried_object();
        $meta_title = $category->name . ' - ' . get_bloginfo('name');
        $meta_description = $category->description ?: 'Browse posts in ' . $category->name;
        $canonical_url = get_category_link($category->term_id);
        
    } elseif (is_tag()) {
        // Tag archive
        $tag = get_queried_object();
        $meta_title = $tag->name . ' - ' . get_bloginfo('name');
        $meta_description = $tag->description ?: 'Posts tagged with ' . $tag->name;
        $canonical_url = get_tag_link($tag->term_id);
        
    } elseif (is_search()) {
        // Search results
        $search_query = get_search_query();
        $meta_title = 'Search Results for "' . $search_query . '" - ' . get_bloginfo('name');
        $meta_description = 'Search results for "' . $search_query . '" on ' . get_bloginfo('name');
        $canonical_url = get_search_link($search_query);
        
    } else {
        // Default fallback
        $meta_title = get_bloginfo('name') . ' - ' . get_bloginfo('description');
        $meta_description = get_bloginfo('description');
        $canonical_url = home_url('/');
    }
    
    // Fallback image
    if (!$meta_image) {
        $meta_image = get_template_directory_uri() . '/assets/images/default-og-image.jpg';
        // You can add a default OG image to your theme
    }
    
    // Clean up meta data
    $meta_title = wp_strip_all_tags($meta_title);
    $meta_description = wp_strip_all_tags($meta_description);
    $meta_keywords = wp_strip_all_tags($meta_keywords);
    
    // Limit lengths
    $meta_title = mb_substr($meta_title, 0, 60);
    $meta_description = mb_substr($meta_description, 0, 160);
    
    ?>
    <title><?php echo esc_html($meta_title); ?></title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
    <meta name="external" content="true">
    <meta name="distribution" content="Global">
    <meta http-equiv="audience" content="General">
    <meta name="title" content="<?php echo esc_attr($meta_title); ?>">
    <meta name="description" content="<?php echo esc_attr($meta_description); ?>">
    <meta name="keywords" content="<?php echo esc_attr($meta_keywords); ?>">
    <meta name="news_keywords" content="<?php echo esc_attr($meta_keywords); ?>">
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <meta name="googlebot" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1">
    <meta name="google" content="notranslate">
    <link rel="canonical" href="<?php echo esc_url($canonical_url); ?>">

    <!-- US Market Optimization -->
    <meta name="geo.region" content="US">
    <meta name="geo.placename" content="United States">
    <meta name="language" content="en-US">
    <meta name="content-language" content="en-US">
    <link rel="alternate" hreflang="en-us" href="<?php echo esc_url($canonical_url); ?>">
    <link rel="alternate" hreflang="en" href="<?php echo esc_url($canonical_url); ?>">
    <link rel="alternate" hreflang="x-default" href="<?php echo esc_url($canonical_url); ?>">

    <!-- Favicon and Apple Touch Icons -->
    <link rel="icon" href="<?php echo esc_url(get_template_directory_uri() . '/assets/images/favicon.png'); ?>">
    <link rel="apple-touch-icon" href="<?php echo esc_url(get_template_directory_uri() . '/assets/images/favicon.png'); ?>">
    <link rel="apple-touch-icon" sizes="57x57" href="<?php echo esc_url(get_template_directory_uri() . '/assets/images/favicon-57x57.png'); ?>">
    <link rel="apple-touch-icon" sizes="72x72" href="<?php echo esc_url(get_template_directory_uri() . '/assets/images/favicon-72x72.png'); ?>">
    <link rel="apple-touch-icon" sizes="114x114" href="<?php echo esc_url(get_template_directory_uri() . '/assets/images/favicon-114x114.png'); ?>">
    <link rel="apple-touch-icon" sizes="144x144" href="<?php echo esc_url(get_template_directory_uri() . '/assets/images/favicon-144x144.png'); ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo esc_attr($meta_title); ?>" itemprop="headline">
    <meta property="og:type" content="<?php echo is_single() ? 'article' : 'website'; ?>">
    <meta property="og:url" itemprop="url" content="<?php echo esc_url($canonical_url); ?>">
    <?php if ($meta_image) : ?>
    <meta property="og:image" itemprop="thumbnailUrl" content="<?php echo esc_url($meta_image); ?>">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <?php endif; ?>
    <meta property="og:description" content="<?php echo esc_attr($meta_description); ?>" itemprop="description">
    <meta property="og:site_name" content="<?php echo esc_attr(get_bloginfo('name')); ?>">
    <meta property="og:locale" content="en_US">
    <meta property="og:locale:alternate" content="en_GB">
    <?php if (is_single() && get_post_type() === 'game') : ?>
    <meta property="og:type" content="game">
    <meta property="game:platform" content="Web Browser">
    <meta property="game:rating" content="Everyone">
    <meta property="game:price" content="Free">
    <meta property="game:availability" content="Available">
    <?php endif; ?>
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:title" content="<?php echo esc_attr($meta_title); ?>">
    <meta name="twitter:url" content="<?php echo esc_url($canonical_url); ?>">
    <?php if ($meta_image) : ?>
    <meta name="twitter:image" content="<?php echo esc_url($meta_image); ?>">
    <?php endif; ?>
    <meta name="twitter:description" content="<?php echo esc_attr($meta_description); ?>">
    <meta name="twitter:card" content="<?php echo (is_single() && get_post_type() === 'game') ? 'summary_large_image' : 'summary'; ?>">
    <meta name="twitter:site" content="@<?php echo esc_attr(get_bloginfo('name')); ?>">
    <meta name="twitter:creator" content="@<?php echo esc_attr(get_bloginfo('name')); ?>">
    <?php if (is_single() && get_post_type() === 'game') : ?>
    <meta name="twitter:label1" content="Platform">
    <meta name="twitter:data1" content="Web Browser">
    <meta name="twitter:label2" content="Price">
    <meta name="twitter:data2" content="Free">
    <?php endif; ?>

    <!-- Homepage-specific US Market Meta Tags -->
    <?php if (is_front_page()) : ?>
        <meta property="website:category" content="Online Gaming Platform">
        <meta property="website:region" content="United States">
        <meta property="website:language" content="English">
        <meta property="website:audience" content="US Gamers">
        <meta property="website:type" content="Free Gaming Website">
        <meta property="website:platform" content="Web Browser, Desktop, Mobile">
        <meta property="website:availability" content="24/7 Available in US">
        <meta property="website:price" content="Free">
        <meta property="website:registration" content="Not Required">
        <meta property="website:download" content="Not Required">
        <meta property="website:compatibility" content="Chrome, Firefox, Safari, Edge">
        <meta property="website:games_count" content="<?php echo wp_count_posts('game')->publish; ?>+">
        <meta property="website:content_rating" content="Family Friendly">
        <meta property="website:safety" content="Safe for All Ages">

        <!-- Enhanced Twitter Cards for Homepage -->
        <meta name="twitter:label1" content="Games Available">
        <meta name="twitter:data1" content="<?php echo wp_count_posts('game')->publish; ?>+ Free Games">
        <meta name="twitter:label2" content="Platform">
        <meta name="twitter:data2" content="Browser & Mobile">

        <!-- Homepage Gaming Keywords -->
        <meta name="gaming-keywords" content="free online games USA, browser games no download, instant play games, web games United States, HTML5 games online, American online gaming">
        <meta name="target-audience" content="US gamers, American players, free game enthusiasts, browser game players, mobile gamers">
        <meta name="game-categories" content="action games, puzzle games, adventure games, sports games, arcade games, strategy games">
    <?php endif; ?>

    <!-- Additional Game-specific Meta Tags -->
    <?php if (is_single() && get_post_type() === 'game') : ?>
        <?php
        $total_votes = (int) get_post_meta($post->ID, '_total_votes', true);
        $total_score = (int) get_post_meta($post->ID, '_total_score', true);
        $avg_rating = $total_votes > 0 ? round($total_score / $total_votes, 1) : 0;
        $play_count = get_post_meta($post->ID, '_play_count', true);
        $iframe_link = get_post_meta($post->ID, '_iframe_link', true);
        ?>
        
        <!-- US Gaming Market Optimization -->
        <meta property="game:rating" content="Everyone">
        <meta property="game:play_count" content="<?php echo esc_attr($play_count ?: '0'); ?>">
        <meta property="game:platform" content="Web Browser, Desktop, Mobile, Tablet">
        <meta property="game:category" content="Online Browser Game">
        <meta property="game:price" content="Free">
        <meta property="game:region" content="United States">
        <meta property="game:language" content="English">
        <meta property="game:audience" content="US Gamers">
        <meta property="game:availability" content="24/7 Available in US">
        <meta property="game:compatibility" content="Chrome, Firefox, Safari, Edge">
        <?php if ($iframe_link) : ?>
        <meta property="game:url" content="<?php echo esc_url($iframe_link); ?>">
        <?php endif; ?>
        
        <!-- Article specific tags -->
        <meta property="article:published_time" content="<?php echo esc_attr(get_the_date('c')); ?>">
        <meta property="article:modified_time" content="<?php echo esc_attr(get_the_modified_date('c')); ?>">
        <meta property="article:author" content="<?php echo esc_attr(get_the_author()); ?>">
        
        <?php if ($game_tags && !is_wp_error($game_tags)) : ?>
            <?php foreach ($game_tags as $tag) : ?>
        <meta property="article:tag" content="<?php echo esc_attr($tag->name); ?>">
            <?php endforeach; ?>
        <?php endif; ?>
    <?php endif; ?>
    
    <?php if (is_single() && get_post_type() === 'game') : ?>
        <!-- VideoGame Schema.org JSON-LD for Game Pages -->
        <?php d27_add_videogame_schema(); ?>
    <?php elseif (is_front_page()) : ?>
        <!-- US-Optimized Homepage Schema -->
        <?php d27_add_homepage_schema(); ?>
        <!-- Homepage FAQ Schema for Featured Snippets -->
        <?php d27_add_homepage_faq_schema(); ?>
    <?php else : ?>
        <!-- Website Schema.org JSON-LD -->
        <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "WebSite",
            "name": "<?php echo esc_js($meta_title); ?>",
            "description": "<?php echo esc_js($meta_description); ?>",
            "url": "<?php echo esc_url($canonical_url); ?>",
            <?php if ($meta_image) : ?>
            "image": "<?php echo esc_url($meta_image); ?>",
            <?php endif; ?>
            "publisher": {
                "@type": "Organization",
                "name": "<?php echo esc_js(get_bloginfo('name')); ?>",
                "url": "<?php echo esc_url(home_url('/')); ?>"
            }
        }
        </script>
    <?php endif; ?>
    
    <!-- Additional Performance and SEO Meta Tags -->
    <meta name="theme-color" content="<?php echo esc_attr(get_theme_mod('d27_primary_color', '#00d4ff')); ?>">
    <meta name="msapplication-TileColor" content="<?php echo esc_attr(get_theme_mod('d27_primary_color', '#00d4ff')); ?>">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    
    <!-- Preconnect to external domains for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <?php
}

/**
 * Add US-optimized homepage schema
 */
function d27_add_homepage_schema() {
    if (!is_front_page()) {
        return;
    }

    // Get homepage data
    $site_name = get_bloginfo('name');
    $site_description = get_bloginfo('description');
    $site_url = home_url('/');

    // Get customizer SEO data
    $seo_title = get_theme_mod('d27_seo_title', '');
    $seo_description = get_theme_mod('d27_seo_description', '');

    $meta_title = $seo_title ?: $site_name . ' - Free Online Games USA | Play Browser Games Instantly';
    $meta_description = $seo_description ?: 'Play thousands of free online games in the USA! No downloads, no registration required. Instant browser games for desktop and mobile.';

    // Get featured game image or default
    $featured_game_id = get_theme_mod('d27_featured_game', '');
    $site_image = '';
    if ($featured_game_id && has_post_thumbnail($featured_game_id)) {
        $site_image = get_the_post_thumbnail_url($featured_game_id, 'large');
    } else {
        $site_image = get_template_directory_uri() . '/assets/images/default-og-image.jpg';
    }

    // Build comprehensive homepage schema
    $homepage_schema = array(
        '@context' => 'https://schema.org',
        '@type' => array('WebSite', 'EntertainmentBusiness'),
        'name' => $meta_title,
        'alternateName' => $site_name,
        'description' => $meta_description,
        'url' => $site_url,
        'image' => array(
            '@type' => 'ImageObject',
            'url' => $site_image,
            'width' => '1200',
            'height' => '630'
        ),
        'inLanguage' => 'en-US',
        'audience' => array(
            '@type' => 'Audience',
            'audienceType' => 'Gamers',
            'geographicArea' => array(
                '@type' => 'Country',
                'name' => 'United States'
            )
        ),
        'publisher' => array(
            '@type' => 'Organization',
            'name' => $site_name,
            'url' => $site_url,
            'logo' => array(
                '@type' => 'ImageObject',
                'url' => get_template_directory_uri() . '/assets/images/logo.png'
            ),
            'address' => array(
                '@type' => 'PostalAddress',
                'addressCountry' => 'US'
            )
        ),
        'potentialAction' => array(
            '@type' => 'SearchAction',
            'target' => array(
                '@type' => 'EntryPoint',
                'urlTemplate' => $site_url . '?s={search_term_string}'
            ),
            'query-input' => 'required name=search_term_string'
        ),
        'mainEntity' => array(
            '@type' => 'ItemList',
            'name' => 'Free Online Games',
            'description' => 'Collection of free browser games available in the United States',
            'numberOfItems' => wp_count_posts('game')->publish
        ),
        'offers' => array(
            '@type' => 'Offer',
            'price' => '0.00',
            'priceCurrency' => 'USD',
            'availability' => 'https://schema.org/InStock',
            'eligibleRegion' => array(
                '@type' => 'Country',
                'name' => 'United States'
            )
        ),
        'serviceType' => 'Online Gaming Platform',
        'areaServed' => array(
            '@type' => 'Country',
            'name' => 'United States'
        ),
        'knowsAbout' => array(
            'Online Games',
            'Browser Games',
            'HTML5 Games',
            'Free Games',
            'Web Games',
            'Mobile Games'
        )
    );

    // Output homepage schema
    ?>
    <script type="application/ld+json">
    <?php echo json_encode($homepage_schema, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES); ?>
    </script>
    <?php
}

/**
 * Add homepage FAQ schema for featured snippets
 */
function d27_add_homepage_faq_schema() {
    if (!is_front_page()) {
        return;
    }

    $site_name = get_bloginfo('name');

    // Build homepage FAQ items
    $faq_items = array();

    // Question 1: What is this website
    $faq_items[] = array(
        '@type' => 'Question',
        'name' => 'What is ' . $site_name . '?',
        'acceptedAnswer' => array(
            '@type' => 'Answer',
            'text' => $site_name . ' is a free online gaming platform where you can play thousands of browser games instantly. No downloads or registration required. All games are free to play in the United States.'
        )
    );

    // Question 2: Are games free
    $faq_items[] = array(
        '@type' => 'Question',
        'name' => 'Are all games on ' . $site_name . ' free?',
        'acceptedAnswer' => array(
            '@type' => 'Answer',
            'text' => 'Yes, all games on ' . $site_name . ' are completely free to play. No hidden costs, no subscriptions, no in-app purchases. Just click and play instantly in your browser.'
        )
    );

    // Question 3: Do I need to download
    $faq_items[] = array(
        '@type' => 'Question',
        'name' => 'Do I need to download anything to play games?',
        'acceptedAnswer' => array(
            '@type' => 'Answer',
            'text' => 'No downloads required! All games on ' . $site_name . ' are browser-based HTML5 games that run directly in your web browser. Simply click play and start gaming immediately.'
        )
    );

    // Question 4: Mobile compatibility
    $faq_items[] = array(
        '@type' => 'Question',
        'name' => 'Can I play games on mobile devices?',
        'acceptedAnswer' => array(
            '@type' => 'Answer',
            'text' => 'Yes, ' . $site_name . ' games are fully compatible with mobile devices. Play on smartphones and tablets using iOS (iPhone/iPad) or Android devices through your mobile browser.'
        )
    );

    // Question 5: Registration required
    $faq_items[] = array(
        '@type' => 'Question',
        'name' => 'Do I need to register or create an account?',
        'acceptedAnswer' => array(
            '@type' => 'Answer',
            'text' => 'No registration required! You can start playing games on ' . $site_name . ' immediately without creating an account, providing personal information, or signing up for anything.'
        )
    );

    // Question 6: Safe for kids
    $faq_items[] = array(
        '@type' => 'Question',
        'name' => 'Are the games safe for children?',
        'acceptedAnswer' => array(
            '@type' => 'Answer',
            'text' => 'Yes, ' . $site_name . ' features family-friendly games that are safe for children. All games are browser-based with no downloads, making them secure and appropriate for all ages.'
        )
    );

    // Question 7: How many games
    $game_count = wp_count_posts('game')->publish;
    $faq_items[] = array(
        '@type' => 'Question',
        'name' => 'How many games are available on ' . $site_name . '?',
        'acceptedAnswer' => array(
            '@type' => 'Answer',
            'text' => $site_name . ' offers ' . $game_count . '+ free online games across various genres including action, puzzle, adventure, sports, and more. New games are added regularly.'
        )
    );

    // Question 8: Browser compatibility
    $faq_items[] = array(
        '@type' => 'Question',
        'name' => 'Which browsers support ' . $site_name . ' games?',
        'acceptedAnswer' => array(
            '@type' => 'Answer',
            'text' => $site_name . ' games work on all modern browsers including Chrome, Firefox, Safari, and Edge. The games are HTML5-based and compatible with Windows, Mac, Linux, iOS, and Android.'
        )
    );

    // Build FAQ schema
    $faq_schema = array(
        '@context' => 'https://schema.org',
        '@type' => 'FAQPage',
        'mainEntity' => $faq_items
    );

    // Output FAQ schema
    ?>
    <script type="application/ld+json">
    <?php echo json_encode($faq_schema, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES); ?>
    </script>
    <?php
}
add_action('wp_head', 'd27_add_seo_meta_tags', 1);

/**
 * Add structured data for game listings
 */
function d27_add_game_listing_schema() {
    if (is_post_type_archive('game') || is_tax('game_tag')) {
        global $wp_query;
        
        $games = array();
        if ($wp_query->have_posts()) {
            while ($wp_query->have_posts()) {
                $wp_query->the_post();
                
                $total_votes = (int) get_post_meta(get_the_ID(), '_total_votes', true);
                $total_score = (int) get_post_meta(get_the_ID(), '_total_score', true);
                $avg_rating = $total_votes > 0 ? round($total_score / $total_votes, 1) : 0;
                $play_count = get_post_meta(get_the_ID(), '_play_count', true);
                
                $game_data = array(
                    '@type' => 'VideoGame',
                    'name' => get_the_title(),
                    'description' => wp_trim_words(get_the_excerpt() ?: get_the_content(), 25, '...'),
                    'url' => get_permalink(),
                    'datePublished' => get_the_date('c'),
                    'gamePlatform' => 'Web Browser',
                    'applicationCategory' => 'Game',
                );
                
                if (has_post_thumbnail()) {
                    $game_data['image'] = get_the_post_thumbnail_url(get_the_ID(), 'large');
                }
                
                // Ratings removed per user request
                
                if ($play_count) {
                    $game_data['interactionStatistic'] = array(
                        '@type' => 'InteractionCounter',
                        'interactionType' => 'https://schema.org/PlayAction',
                        'userInteractionCount' => $play_count,
                    );
                }
                
                $games[] = $game_data;
            }
            wp_reset_postdata();
        }
        
        if (!empty($games)) {
            ?>
            <script type="application/ld+json">
            {
                "@context": "https://schema.org",
                "@type": "ItemList",
                "name": "<?php echo is_tax() ? single_term_title('', false) . ' Games' : 'All Games'; ?>",
                "description": "<?php echo is_tax() ? term_description() : 'Browse all our amazing games'; ?>",
                "url": "<?php echo esc_url(get_pagenum_link()); ?>",
                "numberOfItems": <?php echo count($games); ?>,
                "itemListElement": [
                    <?php
                    foreach ($games as $index => $game) {
                        echo json_encode(array(
                            '@type' => 'ListItem',
                            'position' => $index + 1,
                            'item' => $game
                        ));
                        if ($index < count($games) - 1) echo ',';
                    }
                    ?>
                ]
            }
            </script>
            <?php
        }
    }
}
add_action('wp_head', 'd27_add_game_listing_schema', 2);

/**
 * Add breadcrumb schema
 */
function d27_add_breadcrumb_schema() {
    if (is_front_page()) return;
    
    $breadcrumbs = array();
    
    // Home
    $breadcrumbs[] = array(
        '@type' => 'ListItem',
        'position' => 1,
        'name' => 'Home',
        'item' => home_url('/')
    );
    
    $position = 2;
    
    if (is_post_type_archive('game')) {
        $breadcrumbs[] = array(
            '@type' => 'ListItem',
            'position' => $position,
            'name' => 'Games',
            'item' => get_post_type_archive_link('game')
        );
    } elseif (is_tax('game_tag')) {
        $breadcrumbs[] = array(
            '@type' => 'ListItem',
            'position' => $position++,
            'name' => 'Games',
            'item' => get_post_type_archive_link('game')
        );
        
        $term = get_queried_object();
        $breadcrumbs[] = array(
            '@type' => 'ListItem',
            'position' => $position,
            'name' => $term->name,
            'item' => get_term_link($term)
        );
    } elseif (is_single() && get_post_type() === 'game') {
        $breadcrumbs[] = array(
            '@type' => 'ListItem',
            'position' => $position++,
            'name' => 'Games',
            'item' => get_post_type_archive_link('game')
        );
        
        $breadcrumbs[] = array(
            '@type' => 'ListItem',
            'position' => $position,
            'name' => get_the_title(),
            'item' => get_permalink()
        );
    }
    
    if (count($breadcrumbs) > 1) {
        ?>
        <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": <?php echo json_encode($breadcrumbs); ?>
        }
        </script>
        <?php
    }
}
add_action('wp_head', 'd27_add_breadcrumb_schema', 3);

/**
 * Add VideoGame schema for individual game pages
 */
function d27_add_videogame_schema() {
    if (!is_single() || get_post_type() !== 'game') {
        return;
    }

    global $post;

    // Get game data
    $game_title = get_the_title();
    $game_description = get_the_excerpt() ?: wp_trim_words(get_the_content(), 25, '...');
    $game_url = get_permalink();
    $game_image = has_post_thumbnail() ? get_the_post_thumbnail_url($post->ID, 'large') : '';

    // Get custom fields
    $play_count = get_post_meta($post->ID, '_play_count', true);
    $iframe_link = get_post_meta($post->ID, '_iframe_link', true);
    $instructions = get_post_meta($post->ID, '_game_instructions', true);

    // Get game tags for genre
    $game_tags = get_the_terms($post->ID, 'game_tag');
    $genres = array();
    if ($game_tags && !is_wp_error($game_tags)) {
        $genres = wp_list_pluck($game_tags, 'name');
    }

    // Fallback image
    if (!$game_image) {
        $game_image = get_template_directory_uri() . '/assets/images/default-og-image.jpg';
    }

    // Build US-optimized VideoGame schema
    $schema = array(
        '@context' => 'https://schema.org',
        '@type' => 'VideoGame',
        'name' => $game_title,
        'description' => $game_description,
        'url' => $game_url,
        'image' => array(
            '@type' => 'ImageObject',
            'url' => $game_image,
            'width' => '1200',
            'height' => '630'
        ),
        'datePublished' => get_the_date('c'),
        'dateModified' => get_the_modified_date('c'),
        'inLanguage' => 'en-US',
        'contentRating' => 'Everyone',
        'publisher' => array(
            '@type' => 'Organization',
            'name' => get_bloginfo('name'),
            'url' => home_url('/'),
            'logo' => array(
                '@type' => 'ImageObject',
                'url' => get_template_directory_uri() . '/assets/images/logo.png'
            ),
            'address' => array(
                '@type' => 'PostalAddress',
                'addressCountry' => 'US'
            )
        ),
        'author' => array(
            '@type' => 'Person',
            'name' => get_the_author()
        ),
        'gamePlatform' => array('Web Browser', 'Desktop', 'Mobile', 'Tablet'),
        'applicationCategory' => 'Game',
        'operatingSystem' => array('Windows', 'macOS', 'Linux', 'iOS', 'Android'),
        'browserRequirements' => 'HTML5 compatible browser',
        'memoryRequirements' => '512MB RAM',
        'storageRequirements' => '0MB (Browser-based)',
        'offers' => array(
            '@type' => 'Offer',
            'price' => '0.00',
            'priceCurrency' => 'USD',
            'availability' => 'https://schema.org/InStock',
            'validFrom' => get_the_date('c'),
            'priceValidUntil' => date('Y-m-d', strtotime('+1 year')),
            'eligibleRegion' => array(
                '@type' => 'Country',
                'name' => 'United States'
            ),
            'seller' => array(
                '@type' => 'Organization',
                'name' => get_bloginfo('name')
            )
        ),
        'audience' => array(
            '@type' => 'Audience',
            'audienceType' => 'Gamers',
            'geographicArea' => array(
                '@type' => 'Country',
                'name' => 'United States'
            )
        ),
        'isAccessibleForFree' => true,
        'isFamilyFriendly' => true
    );

    // Add genres if available
    if (!empty($genres)) {
        $schema['genre'] = $genres;
    }

    // Add game URL if available
    if ($iframe_link) {
        $schema['gameLocation'] = $iframe_link;
    }

    // Add instructions if available
    if ($instructions) {
        $schema['gameHelp'] = $instructions;
    }

    // Add play count as interaction statistic
    if ($play_count) {
        $schema['interactionStatistic'] = array(
            '@type' => 'InteractionCounter',
            'interactionType' => 'https://schema.org/PlayAction',
            'userInteractionCount' => $play_count
        );
    }

    // Output the VideoGame schema
    ?>
    <script type="application/ld+json">
    <?php echo json_encode($schema, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES); ?>
    </script>

    <!-- FAQ Schema for Featured Snippets -->
    <?php d27_add_game_faq_schema(); ?>
    <?php
}

/**
 * Add FAQ schema for game pages to increase featured snippet chances
 */
function d27_add_game_faq_schema() {
    if (!is_single() || get_post_type() !== 'game') {
        return;
    }

    global $post;

    // Get game data
    $game_title = get_the_title();
    $instructions = get_post_meta($post->ID, '_game_instructions', true);
    $iframe_link = get_post_meta($post->ID, '_iframe_link', true);
    $game_tags = get_the_terms($post->ID, 'game_tag');

    // Build FAQ data
    $faq_items = array();

    // Question 1: How to play
    $how_to_play_answer = $instructions ?: "Use your keyboard and mouse to control the game. Follow the on-screen instructions to learn the specific controls for " . $game_title . ".";
    $faq_items[] = array(
        '@type' => 'Question',
        'name' => 'How to play ' . $game_title . '?',
        'acceptedAnswer' => array(
            '@type' => 'Answer',
            'text' => $how_to_play_answer
        )
    );

    // Question 2: Is it free
    $faq_items[] = array(
        '@type' => 'Question',
        'name' => 'Is ' . $game_title . ' free to play?',
        'acceptedAnswer' => array(
            '@type' => 'Answer',
            'text' => 'Yes, ' . $game_title . ' is completely free to play online. No download or registration required. You can start playing instantly in your web browser.'
        )
    );

    // Question 3: System requirements
    $faq_items[] = array(
        '@type' => 'Question',
        'name' => 'What are the system requirements for ' . $game_title . '?',
        'acceptedAnswer' => array(
            '@type' => 'Answer',
            'text' => $game_title . ' runs in any modern web browser including Chrome, Firefox, Safari, and Edge. You need an internet connection and a device with at least 512MB RAM. Works on Windows, Mac, Linux, iOS, and Android devices.'
        )
    );

    // Question 4: No download needed
    $faq_items[] = array(
        '@type' => 'Question',
        'name' => 'Do I need to download ' . $game_title . '?',
        'acceptedAnswer' => array(
            '@type' => 'Answer',
            'text' => 'No download required! ' . $game_title . ' is a browser-based HTML5 game that you can play instantly online. Simply click play and start gaming immediately.'
        )
    );

    // Question 5: Mobile compatibility
    $faq_items[] = array(
        '@type' => 'Question',
        'name' => 'Can I play ' . $game_title . ' on mobile?',
        'acceptedAnswer' => array(
            '@type' => 'Answer',
            'text' => 'Yes, ' . $game_title . ' is fully compatible with mobile devices. You can play on smartphones and tablets using iOS (iPhone/iPad) or Android devices through your mobile browser.'
        )
    );

    // Question 6: Game genre (if tags available)
    if ($game_tags && !is_wp_error($game_tags)) {
        $genres = wp_list_pluck($game_tags, 'name');
        $genre_text = implode(', ', $genres);
        $faq_items[] = array(
            '@type' => 'Question',
            'name' => 'What type of game is ' . $game_title . '?',
            'acceptedAnswer' => array(
                '@type' => 'Answer',
                'text' => $game_title . ' is a ' . $genre_text . ' game that you can play for free in your browser. It features engaging gameplay and is suitable for all ages.'
            )
        );
    }

    // Question 7: Safe to play
    $faq_items[] = array(
        '@type' => 'Question',
        'name' => 'Is ' . $game_title . ' safe to play?',
        'acceptedAnswer' => array(
            '@type' => 'Answer',
            'text' => 'Yes, ' . $game_title . ' is completely safe to play. It\'s a browser-based game with no downloads required, making it secure for all users. The game is family-friendly and contains no harmful content.'
        )
    );

    // Question 8: Offline play
    $faq_items[] = array(
        '@type' => 'Question',
        'name' => 'Can I play ' . $game_title . ' offline?',
        'acceptedAnswer' => array(
            '@type' => 'Answer',
            'text' => $game_title . ' requires an internet connection to play as it\'s a web-based game. However, once loaded, it may work briefly offline depending on your browser cache.'
        )
    );

    // Build FAQ schema
    $faq_schema = array(
        '@context' => 'https://schema.org',
        '@type' => 'FAQPage',
        'mainEntity' => $faq_items
    );

    // Output FAQ schema
    ?>
    <script type="application/ld+json">
    <?php echo json_encode($faq_schema, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES); ?>
    </script>
    <?php
}

/**
 * Remove default WordPress SEO meta tags that might conflict
 */
remove_action('wp_head', 'rel_canonical');
remove_action('wp_head', 'wp_shortlink_wp_head');
remove_action('wp_head', 'wp_oembed_add_discovery_links');
remove_action('wp_head', 'wp_oembed_add_host_js');
remove_action('wp_head', 'wp_generator');
remove_action('wp_head', 'wlwmanifest_link');
remove_action('wp_head', 'rsd_link');
remove_action('wp_head', 'wp_resource_hints', 2);

/**
 * Remove WordPress default title tag since we provide our own
 */
remove_theme_support('title-tag');
?>
