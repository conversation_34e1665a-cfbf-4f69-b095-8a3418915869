<?php
/**
 * The sidebar containing the main widget area
 */

if (!is_active_sidebar('sidebar-1')) {
    return;
}
?>

<aside id="secondary" class="widget-area sidebar">
    <?php dynamic_sidebar('sidebar-1'); ?>
</aside>

<style>
/* Sidebar Styles */
.sidebar {
    background: rgba(0, 0, 0, 0.8);
    padding: 2rem;
    border-radius: 10px;
    margin-top: 2rem;
}

.sidebar .widget {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba(0, 212, 255, 0.3);
}

.sidebar .widget:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.sidebar .widget-title {
    color: #00d4ff;
    font-size: 1.3rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #00d4ff;
}

.sidebar .widget ul {
    list-style: none;
    padding: 0;
}

.sidebar .widget li {
    margin-bottom: 0.5rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar .widget li:last-child {
    border-bottom: none;
}

.sidebar .widget a {
    color: #fff;
    text-decoration: none;
    transition: color 0.3s ease;
}

.sidebar .widget a:hover {
    color: #00d4ff;
}

/* Search Widget */
.sidebar .widget_search .search-form {
    position: relative;
}

.sidebar .widget_search input[type="search"] {
    width: 100%;
    padding: 0.8rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid #00d4ff;
    border-radius: 25px;
    color: #fff;
    font-size: 1rem;
}

.sidebar .widget_search input[type="search"]::placeholder {
    color: #ccc;
}

.sidebar .widget_search input[type="submit"] {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: #00d4ff;
    color: #000;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    font-weight: bold;
}

/* Categories Widget */
.sidebar .widget_categories ul li {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar .widget_categories .post-count {
    background: #00d4ff;
    color: #000;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    font-size: 0.8rem;
    font-weight: bold;
}

/* Recent Posts Widget */
.sidebar .widget_recent_entries ul li {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
}

.sidebar .widget_recent_entries .post-date {
    color: #888;
    font-size: 0.8rem;
}

/* Tag Cloud Widget */
.sidebar .widget_tag_cloud .tagcloud {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.sidebar .widget_tag_cloud .tagcloud a {
    background: rgba(0, 212, 255, 0.2);
    color: #00d4ff;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.9rem !important;
    text-decoration: none;
    transition: all 0.3s ease;
}

.sidebar .widget_tag_cloud .tagcloud a:hover {
    background: #00d4ff;
    color: #000;
}

/* Calendar Widget */
.sidebar .widget_calendar table {
    width: 100%;
    border-collapse: collapse;
}

.sidebar .widget_calendar th,
.sidebar .widget_calendar td {
    padding: 0.5rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar .widget_calendar th {
    background: rgba(0, 212, 255, 0.2);
    color: #00d4ff;
    font-weight: bold;
}

.sidebar .widget_calendar td a {
    color: #00d4ff;
    font-weight: bold;
}

/* Archives Widget */
.sidebar .widget_archive select {
    width: 100%;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border: 1px solid #00d4ff;
    border-radius: 5px;
}

/* Meta Widget */
.sidebar .widget_meta ul li a {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.sidebar .widget_meta ul li a:before {
    content: "🔗";
    font-size: 0.9rem;
}

/* RSS Widget */
.sidebar .widget_rss ul li {
    margin-bottom: 1rem;
}

.sidebar .widget_rss .rss-date {
    color: #888;
    font-size: 0.8rem;
    display: block;
    margin-top: 0.3rem;
}

.sidebar .widget_rss cite {
    color: #00d4ff;
    font-style: normal;
    font-size: 0.9rem;
}

/* Text Widget */
.sidebar .widget_text .textwidget {
    color: #ccc;
    line-height: 1.6;
}

.sidebar .widget_text .textwidget p {
    margin-bottom: 1rem;
}

.sidebar .widget_text .textwidget a {
    color: #00d4ff;
}

/* Custom Widgets */
.sidebar .widget_custom_html .textwidget {
    color: #ccc;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        margin-top: 1rem;
        padding: 1.5rem;
    }
    
    .sidebar .widget {
        margin-bottom: 1.5rem;
    }
    
    .sidebar .widget-title {
        font-size: 1.2rem;
    }
}

/* Widget Area when no widgets */
.sidebar:empty:before {
    content: "No widgets added to sidebar yet. Add some widgets from the WordPress admin.";
    color: #888;
    font-style: italic;
    display: block;
    text-align: center;
    padding: 2rem;
}
</style>
