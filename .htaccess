# D27 Gaming Theme - Security & Anti-Spam Rules
# Block spam bots and malicious requests

# Security Headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set X-Robots-Tag "index, follow"
    Header unset X-Powered-By
    Header unset Server
</IfModule>

# Block spam user agents
<IfModule mod_rewrite.c>
RewriteEngine On
RewriteCond %{HTTP_USER_AGENT} (AhrefsBot|MJ12bot|DotBot|SemrushBot|MegaIndex|BLEXBot|PetalBot|YandexBot|BaiduSpider|SogouSpider|360Spider|Mail\.RU_Bot|CCBot|ChatGPT-User|GPTBot|Google-Extended|FacebookBot|facebookexternalhit|Twitterbot|LinkedInBot|WhatsApp|TelegramBot|SkypeUriPreview|Slackbot|Discordbot|Applebot|ia_archiver|Wayback|archive\.org_bot|SiteAuditBot|ScreamingFrogSEOSpider|MajesticSEO|spbot|TurnitinBot|CopyscapeBot|SiteExplorer|LinkpadBot|BacklinkCrawler|SEOkicks|Lipperhey|Wotbox|SearchmetricsBot|LinkdexBot|Ezooms|SEOstats|OpenHoseBot|proximic|changedetection|Blekkobot|BUbiNG|Kraken|coccoc|IntegromeDB|EmailCollector|EmailSiphon|WebBandit|EmailWolf|ExtractorPro|CopyRightCheck|Crescent|SiteSnagger|ProWebWalker|CheeseBot|LNSpiderguy|Teleport|TeleportPro|MIIxpc|Telesoft|WebZip|WebStripper|WebSauger|WebCopier|NetAnts|WebAuto|TheNomad|WWW-Collector-E|RMA|libWeb|asterias|httplib|turingos|spanner|InfoNaviRobot|Harvest|Bullseye|CherryPickerSE|CherryPickerElite|NICErsPRO|DittoSpyder|Foobot|WebmasterWorldForumBot|SpankBot|BotALot|lwp-trivial|BunnySlippers|URLy Warning|Wget|LinkWalker|cosmos|moget|hloader|humanlinks|LinkextractorPro|Offline Explorer|Mata Hari|LexiBot|Web Image Collector|The Intraformant|True_Robot|BlowFish|JennyBot|BuiltBotTough|ProPowerBot|BackDoorBot|toCrawl|WebEnhancer|suzuran|VCI|Szukacz|QueryN Metasearch|Openfind|Xenu|Zeus|RepoMonkey|Microsoft URL Control|Openbot|URL Control|Webster Pro|EroCrawler|LinkScan|Keyword Density|Kenjin Spider|Iron33|Bookmark search tool|GetRight|FairAd Client|Gaisbot|Aqua_Products|Radiation Retriever|Flaming AttackBot|Oracle Ultra Search|MSIECrawler|PerMan|searchpreview) [NC]
RewriteRule .* - [F,L]

# Block empty user agents
RewriteCond %{HTTP_USER_AGENT} ^$ [OR]
RewriteCond %{HTTP_USER_AGENT} ^-$ [OR]
RewriteCond %{HTTP_USER_AGENT} ^\s*$
RewriteRule .* - [F,L]

# Block suspicious requests
RewriteCond %{QUERY_STRING} \.\.\/ [NC,OR]
RewriteCond %{QUERY_STRING} \<script [NC,OR]
RewriteCond %{QUERY_STRING} javascript: [NC,OR]
RewriteCond %{QUERY_STRING} eval\( [NC,OR]
RewriteCond %{QUERY_STRING} base64_decode [NC,OR]
RewriteCond %{QUERY_STRING} union.*select [NC,OR]
RewriteCond %{QUERY_STRING} select.*from [NC,OR]
RewriteCond %{QUERY_STRING} insert.*into [NC,OR]
RewriteCond %{QUERY_STRING} update.*set [NC,OR]
RewriteCond %{QUERY_STRING} delete.*from [NC,OR]
RewriteCond %{QUERY_STRING} drop.*table [NC,OR]
RewriteCond %{QUERY_STRING} create.*table [NC,OR]
RewriteCond %{QUERY_STRING} alter.*table [NC,OR]
RewriteCond %{QUERY_STRING} exec.*\( [NC,OR]
RewriteCond %{QUERY_STRING} system\( [NC,OR]
RewriteCond %{QUERY_STRING} shell_exec [NC,OR]
RewriteCond %{QUERY_STRING} passthru [NC,OR]
RewriteCond %{QUERY_STRING} file_get_contents [NC,OR]
RewriteCond %{QUERY_STRING} curl_exec [NC,OR]
RewriteCond %{QUERY_STRING} wget [NC,OR]
RewriteCond %{QUERY_STRING} lynx [NC]
RewriteRule .* - [F,L]

# Block directory traversal attempts
RewriteCond %{THE_REQUEST} \s/+\.\./+ [NC,OR]
RewriteCond %{THE_REQUEST} \s.*\.\./ [NC,OR]
RewriteCond %{THE_REQUEST} \s.*%2e%2e%2f [NC,OR]
RewriteCond %{THE_REQUEST} \s.*%2e%2e%5c [NC]
RewriteRule .* - [F,L]

# Block null byte attacks
RewriteCond %{QUERY_STRING} \x00 [NC,OR]
RewriteCond %{QUERY_STRING} %00 [NC]
RewriteRule .* - [F,L]

# Block WordPress vulnerabilities - Keep admin-ajax.php accessible
RewriteRule ^wp-config\.php$ - [F,L]
RewriteRule ^wp-config-sample\.php$ - [F,L]
RewriteRule ^readme\.html$ - [F,L]
RewriteRule ^readme\.txt$ - [F,L]
RewriteRule ^license\.txt$ - [F,L]
RewriteRule ^xmlrpc\.php$ - [F,L]
RewriteRule ^wp-trackback\.php$ - [F,L]
RewriteRule ^install\.php$ - [F,L]
RewriteRule ^upgrade\.php$ - [F,L]

# Allow AJAX requests
RewriteCond %{REQUEST_URI} ^/wp-admin/admin-ajax\.php$
RewriteRule .* - [L]

# Block access to sensitive files
RewriteRule ^\.htaccess$ - [F,L]
RewriteRule ^\.htpasswd$ - [F,L]
RewriteRule ^\.env$ - [F,L]
RewriteRule ^error_log$ - [F,L]
RewriteRule ^error\.log$ - [F,L]
RewriteRule ^access\.log$ - [F,L]
RewriteRule ^access_log$ - [F,L]

# Block backup and temporary files
RewriteRule \.(bak|backup|old|orig|tmp|temp|swp|swo)$ - [F,L]
RewriteRule ~$ - [F,L]
RewriteRule #$ - [F,L]

# Block version control directories
RewriteRule ^\.git/ - [F,L]
RewriteRule ^\.svn/ - [F,L]
RewriteRule ^\.hg/ - [F,L]
RewriteRule ^\.bzr/ - [F,L]
RewriteRule ^CVS/ - [F,L]

# Block common attack vectors
RewriteRule ^(phpmyadmin|phpMyAdmin|adminer|mysql|database|db|admin|administrator|manager|control|panel|dashboard|console|terminal|shell|cmd|command|cgi-bin|scripts|bin|sbin|usr|var|tmp|temp|cache|backup|backups|logs|log|test|testing|dev|development|staging|beta|alpha|demo|sample|example|examples|old|new|ftp|sftp|ssh)/ - [F,L]

# Block spam file extensions
RewriteRule \.(php|php3|php4|php5|php7|php8|phtml|phps|asp|aspx|jsp|jspx|cgi|pl|py|rb|sh|bat|cmd|exe|dll|so|dylib|com|scr|pif|sql|db|sqlite|mdb|accdb|dbf|inc|class|lib|dat|data)$ - [F,L]

# Block spam query parameters - Temporarily disabled for AJAX functionality
# RewriteCond %{QUERY_STRING} (utm_source|utm_medium|utm_campaign|utm_content|utm_term|fbclid|gclid|msclkid|yclid|ref|source|campaign|medium|content|term|affiliate|partner|promo|coupon|discount|offer|deal|sale|buy|shop|store|product|item|cart|checkout|payment|billing|invoice|order|purchase|transaction|debug|test|admin|login|password|user|username|email|mail|contact|phone|address|zip|postal|country|state|city|location|ip|host|domain|url|link|redirect|goto|forward|proxy|mirror) [NC]
# RewriteRule .* - [F,L]

# Block spam content patterns
RewriteCond %{REQUEST_URI} (pharmacy|viagra|cialis|casino|poker|loan|mortgage|insurance|dating|escort|porn|sex|adult|xxx|replica|fake|cheap|discount|sale|buy.*online|weight.*loss|diet.*pill|make.*money|work.*home|earn.*money|get.*rich|free.*money|lottery|winner|prize|congratulations) [NC]
RewriteRule .* - [F,L]

# Rate limiting (if mod_evasive is available)
<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        10
    DOSSiteCount        50
    DOSPageInterval     1
    DOSSiteInterval     1
    DOSBlockingPeriod   600
</IfModule>

# BEGIN WordPress
<IfModule mod_rewrite.c>
RewriteEngine On
RewriteBase /

# D27 Gaming Theme - Clean URL Rewrite Rules
# These rules internally load content from /games/ but keep the clean URL displayed

# Game sorting URLs
RewriteRule ^popularity/?$ /games/?orderby=popularity [L,QSA]
RewriteRule ^newest/?$ /games/?orderby=date [L,QSA]
RewriteRule ^trending/?$ /games/?orderby=trending [L,QSA]
RewriteRule ^most-played/?$ /games/?orderby=most_played [L,QSA]
RewriteRule ^random/?$ /games/?orderby=rand [L,QSA]

# Category URLs
RewriteRule ^category/([^/]+)/?$ /games/?game_category=$1 [L,QSA]

# Tag URLs - Removed to use WordPress native taxonomy archives
# RewriteRule ^tag/([^/]+)/?$ /games/?game_tag=$1 [L,QSA]

# Search URLs
RewriteRule ^search/([^/]+)/?$ /games/?search=$1 [L,QSA]

# WordPress default rules
RewriteRule ^index\.php$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.php [L]
</IfModule>
# END WordPress