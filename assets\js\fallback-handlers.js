/**
 * D27 Gaming Theme - Fallback Handlers
 * Alternative implementation of game interaction features in case the primary ones fail
 */

(function() {
    'use strict';
    
    // Global variables
    let ratingSubmitted = false;
    let playTriggered = false;
    
    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Fallback handlers initialized');
        initializeFallbackHandlers();
    });
    
    /**
     * Initialize fallback handlers for all interactive elements
     */
    function initializeFallbackHandlers() {
        // Initialize rating stars with direct implementation
        initializeFallbackRating();
        
        // Initialize play button with direct implementation
        initializeFallbackPlay();
    }
    
    /**
     * Initialize fallback rating functionality
     */
    function initializeFallbackRating() {
        const ratingContainers = document.querySelectorAll('.star-rating');
        
        ratingContainers.forEach(container => {
            const gameId = container.getAttribute('data-game-id');
            const stars = container.querySelectorAll('.rating-star');
            const resultDisplay = container.querySelector('.rating-result');
            
            if (!gameId || !stars.length) return;
            
            console.log('Setting up fallback rating for game ID:', gameId);
            
            // Apply hover effects
            stars.forEach((star, index) => {
                // Mouse enter effect
                star.addEventListener('mouseenter', function() {
                    // Fill in stars up to current hover
                    for (let i = 0; i < stars.length; i++) {
                        if (i <= index) {
                            stars[i].textContent = '★';
                            stars[i].classList.add('hover');
                        } else {
                            stars[i].textContent = '☆';
                            stars[i].classList.remove('hover');
                        }
                    }
                });
                
                // Click handler
                star.addEventListener('click', function() {
                    if (ratingSubmitted) {
                        showNotification('You have already submitted a rating', 'info');
                        return;
                    }
                    
                    const rating = index + 1;
                    submitFallbackRating(gameId, rating, container);
                });
            });
            
            // Mouse leave handler
            container.addEventListener('mouseleave', function() {
                resetStarDisplay(stars, resultDisplay);
            });
        });
    }
    
    /**
     * Submit rating via direct AJAX
     */
    function submitFallbackRating(gameId, rating, container) {
        console.log('Submitting fallback rating:', gameId, rating);
        
        // Get necessary data
        const ajaxUrl = window.location.origin + '/wp-admin/admin-ajax.php';
        
        // Create form data
        const formData = new FormData();
        formData.append('action', 'submit_game_rating');
        formData.append('game_id', gameId);
        formData.append('rating', rating);
        formData.append('nonce', typeof d27_ajax !== 'undefined' ? d27_ajax.nonce : '');
        
        // Submit via fetch API
        fetch(ajaxUrl, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            console.log('Rating response:', data);
            
            if (data.success) {
                const stars = container.querySelectorAll('.rating-star');
                const resultDisplay = container.querySelector('.rating-result');
                
                // Update UI
                if (resultDisplay) {
                    resultDisplay.textContent = data.data.new_average + '/5 (' + data.data.total_votes + ' votes)';
                }
                
                // Mark stars as filled
                stars.forEach((star, i) => {
                    if (i < rating) {
                        star.classList.add('filled');
                        star.textContent = '★';
                    } else {
                        star.classList.remove('filled');
                        star.textContent = '☆';
                    }
                });
                
                // Mark as submitted
                ratingSubmitted = true;
                
                // Show success message
                showNotification('Rating submitted successfully!', 'success');
            } else {
                showNotification('Error submitting rating', 'error');
            }
        })
        .catch(error => {
            console.error('Error submitting rating:', error);
            showNotification('Error submitting rating', 'error');
        });
    }
    
    /**
     * Reset star display based on current rating
     */
    function resetStarDisplay(stars, resultDisplay) {
        if (!resultDisplay) return;
        
        // Extract current rating from display text
        const ratingText = resultDisplay.textContent;
        const match = ratingText.match(/([0-9.]+)\/5/);
        
        if (match) {
            const currentRating = parseFloat(match[1]);
            const roundedRating = Math.round(currentRating);
            
            // Reset stars based on current rating
            stars.forEach((star, i) => {
                star.classList.remove('hover');
                
                if (i < roundedRating) {
                    star.textContent = '★';
                } else {
                    star.textContent = '☆';
                }
            });
        } else {
            // No rating yet, clear all stars
            stars.forEach(star => {
                star.classList.remove('hover');
                star.textContent = '☆';
            });
        }
    }
    
    /**
     * Initialize fallback play button functionality
     */
    function initializeFallbackPlay() {
        const playButton = document.querySelector('.play-button');
        if (!playButton) return;
        
        // Get game ID from onclick attribute
        let gameId = null;
        const onclickAttr = playButton.getAttribute('onclick');
        
        if (onclickAttr) {
            const match = onclickAttr.match(/playGame\((\d+)\)/);
            if (match) {
                gameId = parseInt(match[1]);
            }
        }
        
        if (!gameId) return;
        
        console.log('Setting up fallback play for game ID:', gameId);
        
        // Add direct click handler
        playButton.addEventListener('click', function(event) {
            // Stop event propagation to prevent double handling
            event.stopPropagation();
            
            // If already handled by main script, don't do anything
            if (playTriggered) return;
            
            console.log('Fallback play handler triggered for game ID:', gameId);
            
            // Get iframe
            const iframe = document.querySelector('.game-iframe');
            if (!iframe) return;
            
            // Show iframe, hide play button
            iframe.style.display = 'block';
            playButton.style.display = 'none';
            
            // Increment play count
            incrementFallbackPlayCount(gameId);
            
            // Show fullscreen button
            const fullscreenBtn = document.querySelector('.fullscreen-btn');
            if (fullscreenBtn) {
                fullscreenBtn.style.display = 'block';
            }
            
            // Mark as triggered
            playTriggered = true;
            
            // Prevent default click behavior
            return false;
        });
    }
    
    /**
     * Increment play count via direct AJAX
     */
    function incrementFallbackPlayCount(gameId) {
        console.log('Incrementing fallback play count for game ID:', gameId);
        
        // Get necessary data
        const ajaxUrl = window.location.origin + '/wp-admin/admin-ajax.php';
        
        // Create form data
        const formData = new FormData();
        formData.append('action', 'increment_play_count');
        formData.append('game_id', gameId);
        formData.append('nonce', typeof d27_ajax !== 'undefined' ? d27_ajax.nonce : '');
        
        // Submit via fetch API
        fetch(ajaxUrl, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            console.log('Play count response:', data);
            
            if (data.success) {
                // Update UI
                const playCountDisplay = document.getElementById('play-count-display');
                if (playCountDisplay) {
                    playCountDisplay.textContent = formatNumber(data.data.count);
                }
                
                // Show success message
                showNotification('Game loaded successfully!', 'success');
            }
        })
        .catch(error => {
            console.error('Error incrementing play count:', error);
            // Don't show error to user - game should still play
        });
    }
    
    /**
     * Show notification
     */
    function showNotification(message, type = 'info') {
        // Create notification
        const notification = document.createElement('div');
        notification.className = 'game-notification ' + type;
        notification.textContent = message;
        
        // Add to page
        document.body.appendChild(notification);
        
        // Trigger animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // Remove after delay
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
    
    /**
     * Format number for display
     */
    function formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
})();