// Enhanced Menu Handler - Responsive Header Layout System
function initializeMenuHandler() {
    console.log('🚀 Menu handler initializing - responsive header layout system');

    const menu = document.querySelector('.nav-menu-dynamic') || document.getElementById('primary-menu');
    if (!menu) {
        console.error('❌ Menu not found - checked both .nav-menu-dynamic and #primary-menu');
        console.log('Available menus:', document.querySelectorAll('ul'));
        return;
    }

    console.log('✅ Menu found:', menu.id || menu.className, menu);

    // Try multiple selectors to find menu items (logo and search are now separate)
    let items = menu.querySelectorAll('.nav-item:not(.dropdown-toggle)');

    // Fallback: if no items found, try broader selector
    if (items.length === 0) {
        items = menu.querySelectorAll('li:not(.dropdown-toggle)');
        console.log('🔄 Using fallback selector for menu items');
    }

    const dropdownToggle = menu.querySelector('.dropdown-toggle') || document.getElementById('dropdownToggle');
    const dropdownContent = document.getElementById('dropdownContent');
    
    // Get header dimensions for responsive calculations
    const header = document.querySelector('.main-header');
    const headerLeft = document.querySelector('.header-left');
    const headerRight = document.querySelector('.header-right');

    console.log('Header elements found:', {
        menu: !!menu,
        items: items.length,
        dropdownToggle: !!dropdownToggle,
        dropdownContent: !!dropdownContent,
        header: !!header,
        headerLeft: !!headerLeft,
        headerRight: !!headerRight
    });

    // If dropdown elements don't exist, create them
    if (!dropdownToggle || !dropdownContent) {
        console.log('⚠️ Dropdown elements missing, creating them...');

        // Create dropdown toggle if missing
        if (!dropdownToggle) {
            const newDropdownToggle = document.createElement('li');
            newDropdownToggle.className = 'nav-item dropdown-toggle';
            newDropdownToggle.id = 'dropdownToggle';
            newDropdownToggle.style.display = 'none';
            newDropdownToggle.innerHTML = '<button onclick="toggleDropdown()">⋯ More</button><ul class="dropdown-content" id="dropdownContent"></ul>';
            menu.appendChild(newDropdownToggle);
            console.log('✅ Created dropdown toggle');
        }
    }

    function checkOverflow() {
        // Step 1: Reset all items state → to display: flex
        items.forEach(item => {
            item.style.display = 'flex';
        });

        // Step 2: Hide "More" button and clear dropdown initially
        if (dropdownToggle) {
            dropdownToggle.style.display = 'none';
            if (dropdownContent) {
                dropdownContent.innerHTML = '';
                dropdownContent.classList.remove('show');
            }
        }

        // Step 3: Wait for layout to settle, then calculate available space
        setTimeout(() => {
            // Calculate available space for menu items based on header layout
            let availableWidth = window.innerWidth;
            
            if (header && headerLeft && headerRight) {
                const headerWidth = header.offsetWidth;
                const leftWidth = headerLeft.offsetWidth;
                const rightWidth = headerRight.offsetWidth;
                const headerPadding = parseInt(window.getComputedStyle(header).paddingLeft) + 
                                     parseInt(window.getComputedStyle(header).paddingRight);
                
                // Calculate space available for menu items
                availableWidth = headerWidth - leftWidth - rightWidth - headerPadding - 30; // 30px buffer
                
                console.log('Space calculation:', {
                    headerWidth,
                    leftWidth,
                    rightWidth,
                    headerPadding,
                    availableWidth
                });
            }
            
            // HARD LIMIT: Always enforce 6-item limit regardless of available width
            const maxVisibleItems = Math.min(6, items.length);
            const hiddenItems = [];
            
            // Step 4: Strictly limit to 6 visible items → remaining items go to dropdown
            items.forEach((item, index) => {
                if (index < maxVisibleItems) {
                    // Item is within the 6-item limit, keep it visible
                    item.style.display = 'flex';
                    console.log(`✅ Item ${index + 1}: "${item.textContent.trim()}" - VISIBLE`);
                } else {
                    // Item exceeds 6-item limit, hide it and add to dropdown
                    hiddenItems.push(item);
                    item.style.display = 'none';
                    console.log(`❌ Item ${index + 1}: "${item.textContent.trim()}" - HIDDEN (moved to dropdown)`);
                }
            });
            
            // If there are hidden items that need to go in the dropdown
            if (hiddenItems.length > 0) {
                // Ensure dropdown toggle is visible when needed
                if (dropdownToggle) dropdownToggle.style.display = 'flex';
                
                if (visibleItemCount < minVisibleItems) {
                    visibleItemCount = minVisibleItems;
                    hiddenItems.length = 0;
                    
                    // Force show minimum items and hide the rest
                    items.forEach((item, index) => {
                        if (index < minVisibleItems) {
                            item.style.display = 'flex';
                        } else {
                            item.style.display = 'none';
                            hiddenItems.push(item);
                        }
                    });
                }
                
                // Add hidden items to dropdown
                if (hiddenItems.length > 0 && dropdownToggle && dropdownContent) {
                    // Show "More" button
                    dropdownToggle.style.display = 'flex';
                    
                    // Clear existing content
                    dropdownContent.innerHTML = '';
                    
                    // Add hidden items to dropdown
                    hiddenItems.forEach((item, index) => {
                        const link = item.querySelector('a');
                        if (link) {
                            const dropdownItem = document.createElement('li');
                            dropdownItem.innerHTML = `<a href="${link.href}" style="display: block; padding: 12px 16px; color: white; text-decoration: none; width: 100%; box-sizing: border-box; border-bottom: 1px solid rgba(255,255,255,0.1);">${link.textContent}</a>`;
                            dropdownContent.appendChild(dropdownItem);
                            console.log(`📋 Dropdown item ${index + 1}: "${link.textContent}" added`);
                        }
                    });
                    
                    console.log(`✅ Responsive layout: ${visibleItemCount} visible + ${hiddenItems.length} in dropdown = ${items.length} total`);
                } else {
                    console.log('✅ All items fit - no dropdown needed');
                }
            }
        }, 10);
    }

    // Initialize dropdown toggle functionality
    function initializeDropdown() {
        const button = dropdownToggle.querySelector('button');
        if (button) {
            console.log('Adding click listener to dropdown button');

            // Remove any existing onclick attribute to avoid conflicts
            button.removeAttribute('onclick');

            button.addEventListener('click', function(e) {
                console.log('Dropdown button clicked');
                e.preventDefault();
                e.stopPropagation();
                toggleDropdown();
            });
        } else {
            console.error('Dropdown button not found');
        }
    }

    // FORCE 6-ITEM LIMIT - Direct approach
    function force6ItemLimit() {
        console.log('🔥 FORCING 6-ITEM LIMIT - Direct approach');

        // Get all menu items (excluding dropdown - logo and search are now separate)
        const allMenuItems = Array.from(menu.children).filter(item => {
            return !item.classList.contains('dropdown-toggle');
        });

        console.log(`🎯 Found ${allMenuItems.length} menu items to process`);

        const maxVisible = 6;
        const dropdownToggle = menu.querySelector('.dropdown-toggle') || menu.querySelector('#dropdownToggle');

        allMenuItems.forEach((item, index) => {
            if (index < maxVisible) {
                item.style.display = 'flex';
                item.style.visibility = 'visible';
                console.log(`✅ Item ${index + 1}: "${item.textContent.trim()}" - VISIBLE`);
            } else {
                item.style.display = 'none';
                item.style.visibility = 'hidden';
                console.log(`❌ Item ${index + 1}: "${item.textContent.trim()}" - HIDDEN`);
            }
        });

        // Show dropdown if needed
        if (allMenuItems.length > maxVisible && dropdownToggle) {
            dropdownToggle.style.display = 'flex';
            console.log(`📋 Dropdown shown - ${allMenuItems.length - maxVisible} items hidden`);
        }
    }

    // Run initial setup
    checkOverflow();
    initializeDropdown();

    // Force the 6-item limit as backup
    setTimeout(force6ItemLimit, 100);
    setTimeout(force6ItemLimit, 500);
    setTimeout(force6ItemLimit, 1000);

    // Re-check on window resize
    window.addEventListener('resize', checkOverflow);
}

// Multiple initialization attempts to ensure it runs
document.addEventListener('DOMContentLoaded', initializeMenuHandler);
window.addEventListener('load', initializeMenuHandler);

// Immediate execution if DOM is already ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeMenuHandler);
} else {
    initializeMenuHandler();
}

// Step 7: Click "More" → toggle class .show to open the dropdown
function toggleDropdown() {
    console.log('toggleDropdown called');

    const content = document.getElementById('dropdownContent');

    if (!content) {
        console.error('Dropdown content not found');
        return;
    }

    const isVisible = content.classList.contains('show');
    console.log('Current state:', {
        isVisible,
        hasShowClass: content.classList.contains('show'),
        childrenCount: content.children.length
    });

    if (isVisible) {
        // Hide dropdown - simply remove .show class
        content.classList.remove('show');
        console.log('Dropdown hidden - removed .show class');
    } else {
        // Show dropdown - simply add .show class
        content.classList.add('show');
        console.log('Dropdown shown - added .show class');

        // Debug: Check if content has items
        if (content.children.length === 0) {
            console.warn('Dropdown content is empty - adding test item');
            content.innerHTML = '<li style="padding: 12px 16px; color: white; background: rgba(255, 0, 0, 0.8); margin: 0; list-style: none;">🔴 TEST ITEM - Dropdown is working!</li>';
        }
    }
}

// Close dropdown when clicking outside
document.addEventListener('click', function(event) {
    const dropdown = document.getElementById('dropdownContent');
    const dropdownToggle = document.getElementById('dropdownToggle');

    if (dropdown && dropdownToggle && !dropdownToggle.contains(event.target)) {
        dropdown.classList.remove('show');
    }
});

// Make toggleDropdown globally available for onclick attributes
window.toggleDropdown = toggleDropdown;