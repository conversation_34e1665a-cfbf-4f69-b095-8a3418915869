<?php
/**
 * Template for displaying game tag archives
 */

get_header(); ?>

<main class="main-content">
    <div class="container">
        <div class="archive-header">
            <h1 class="archive-title">🏷️ <?php single_term_title(); ?> Games</h1>
            <?php
            $term_description = term_description();
            if ($term_description) :
            ?>
                <p class="archive-description"><?php echo wp_kses_post($term_description); ?></p>
            <?php else : ?>
                <p class="archive-description">Games tagged with "<?php single_term_title(); ?>"</p>
            <?php endif; ?>
        </div>


        <?php if (have_posts()) : ?>
            
            <div class="games-filter-nav">
                <div class="filter-controls">
                    <!-- View Toggle -->
                    <div class="view-toggle">
                        <button class="view-btn active" data-view="grid" onclick="changeView('grid')">
                            <span class="icon">⊞</span> Grid
                        </button>
                        <button class="view-btn" data-view="list" onclick="changeView('list')">
                            <span class="icon">☰</span> List
                        </button>
                    </div>
                    
                    <!-- Tag Filter -->
                    <div class="tag-filter">
                        <label>Filter by tags:</label>
                        <div class="tag-buttons">
                            <button class="tag-filter-btn" data-tag="all">All</button>
                            <?php
                            $current_term = get_queried_object();
                            $game_tags = get_terms(array(
                                'taxonomy' => 'game_tag',
                                'hide_empty' => true,
                                'number' => 10,
                            ));
                            
                            if (!empty($game_tags) && !is_wp_error($game_tags)) {
                                foreach ($game_tags as $tag) {
                                    $active_class = ($current_term && $current_term->term_id === $tag->term_id) ? ' active' : '';
                                    echo '<button class="tag-filter-btn' . $active_class . '" data-tag="' . esc_attr($tag->slug) . '">' . esc_html($tag->name) . '</button>';
                                }
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>

            <div class="games-grid" id="games-container">
                <?php while (have_posts()) : the_post(); ?>
                    
                    <article id="post-<?php the_ID(); ?>" <?php post_class('game-card'); ?>>
                        <a href="<?php the_permalink(); ?>">
                            <div class="game-thumbnail">
                                <?php if (has_post_thumbnail()) : ?>
                                    <?php the_post_thumbnail('game-thumbnail'); ?>
                                <?php else : ?>
                                    <div class="no-image">
                                        <span class="game-icon">🎮</span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="game-card-content">
                                <h3><?php the_title(); ?></h3>
                                
                                <div class="game-meta">
                                    <?php
                                    $play_count = get_post_meta(get_the_ID(), '_play_count', true);
                                    $play_count = $play_count ? intval($play_count) : 0;
                                    ?>
                                    <span class="play-count">▶ <?php echo number_format($play_count); ?> plays</span>
                                </div>

                                <div class="game-tags">
                                    <?php
                                    $game_tags = get_the_terms(get_the_ID(), 'game_tag');
                                    if ($game_tags && !is_wp_error($game_tags)) {
                                        $tag_count = 0;
                                        foreach ($game_tags as $tag) {
                                            if ($tag_count >= 3) break; // Limit to 3 tags
                                            echo '<span class="tag" data-tag="' . esc_attr($tag->slug) . '">' . esc_html($tag->name) . '</span>';
                                            $tag_count++;
                                        }
                                    }
                                    ?>
                                </div>
                            </div>
                        </a>
                    </article>
                    
                <?php endwhile; ?>
            </div>
            
            <!-- Pagination -->
            <div class="pagination">
                <?php
                the_posts_pagination(array(
                    'mid_size' => 2,
                    'prev_text' => __('← Previous', 'd27-gaming'),
                    'next_text' => __('Next →', 'd27-gaming'),
                ));
                ?>
            </div>
            
        <?php else : ?>
            
            <div class="no-content">
                <h2><?php _e('No Games Found', 'd27-gaming'); ?></h2>
                <p><?php _e('No games found with this tag. Try browsing other categories or search for games.', 'd27-gaming'); ?></p>
                <?php get_search_form(); ?>
                
                <div class="browse-suggestions">
                    <h3>Browse Other Tags:</h3>
                    <div class="tag-suggestions">
                        <?php
                        $popular_tags = get_terms(array(
                            'taxonomy' => 'game_tag',
                            'hide_empty' => true,
                            'number' => 8,
                            'orderby' => 'count',
                            'order' => 'DESC',
                        ));
                        
                        if (!empty($popular_tags) && !is_wp_error($popular_tags)) {
                            foreach ($popular_tags as $tag) {
                                echo '<a href="' . esc_url(get_term_link($tag)) . '" class="tag-suggestion">' . esc_html($tag->name) . ' (' . $tag->count . ')</a>';
                            }
                        }
                        ?>
                    </div>
                </div>
            </div>
            
        <?php endif; ?>
        
    </div>
</main>

<?php get_footer(); ?>

<style>
/* Taxonomy archive specific styles */
.archive-header {
    text-align: center;
    padding: 2rem;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
    margin-bottom: 2rem;
}

/* Hide sidebar widgets on game tag archive pages */
body.tax-game_tag .widget-area,
body.tax-game_tag .sidebar,
body.tax-game_tag #secondary {
    display: none !important;
}

.archive-title {
    color: #00d4ff;
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.archive-description {
    color: #ccc;
    font-size: 1.1rem;
    margin: 0;
}

.games-filter-nav {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.filter-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.view-toggle {
    display: flex;
    gap: 0.5rem;
}

.view-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(0, 212, 255, 0.3);
    color: #fff;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-btn.active,
.view-btn:hover {
    background: #00d4ff;
    color: #000;
}

.tag-filter {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.tag-filter label {
    color: #fff;
    font-weight: bold;
}

.tag-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.tag-filter-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(0, 212, 255, 0.3);
    color: #fff;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.tag-filter-btn.active,
.tag-filter-btn:hover {
    background: #00d4ff;
    color: #000;
}

.games-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.games-grid.list-view {
    grid-template-columns: 1fr;
}

.game-card {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid rgba(0, 212, 255, 0.2);
}

.game-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 212, 255, 0.3);
}

.game-card a {
    text-decoration: none;
    color: inherit;
    display: block;
}

.game-thumbnail {
    position: relative;
    width: 100%;
    height: 150px;
    overflow: hidden;
}

.game-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-image {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1a1a1a, #333);
    display: flex;
    align-items: center;
    justify-content: center;
}

.game-icon {
    font-size: 3rem;
    opacity: 0.5;
}

.game-card-content {
    padding: 1rem;
}

.game-card-content h3 {
    color: #fff;
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
}

.game-meta {
    margin-bottom: 0.5rem;
}

.play-count {
    color: #00d4ff;
    font-size: 0.9rem;
}

.game-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.3rem;
}

.tag {
    background: rgba(0, 212, 255, 0.2);
    color: #00d4ff;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    font-size: 0.8rem;
    border: 1px solid rgba(0, 212, 255, 0.3);
}

.no-content {
    text-align: center;
    padding: 3rem;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
}

.no-content h2 {
    color: #00d4ff;
    margin-bottom: 1rem;
}

.no-content p {
    color: #ccc;
    margin-bottom: 2rem;
}

.browse-suggestions {
    margin-top: 2rem;
}

.browse-suggestions h3 {
    color: #fff;
    margin-bottom: 1rem;
}

.tag-suggestions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
}

.tag-suggestion {
    background: rgba(0, 212, 255, 0.1);
    color: #00d4ff;
    padding: 0.5rem 1rem;
    border-radius: 15px;
    text-decoration: none;
    border: 1px solid rgba(0, 212, 255, 0.3);
    transition: all 0.3s ease;
}

.tag-suggestion:hover {
    background: #00d4ff;
    color: #000;
}

/* Responsive Design */
@media (max-width: 768px) {
    .filter-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .games-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 1rem;
    }
    
    .archive-title {
        font-size: 2rem;
    }
}
</style>

<script>
// View toggle functionality
function changeView(view) {
    const container = document.getElementById('games-container');
    const buttons = document.querySelectorAll('.view-btn');
    
    buttons.forEach(btn => btn.classList.remove('active'));
    document.querySelector(`[data-view="${view}"]`).classList.add('active');
    
    if (view === 'list') {
        container.classList.add('list-view');
    } else {
        container.classList.remove('list-view');
    }
}

// Tag filtering functionality
document.addEventListener('DOMContentLoaded', function() {
    const tagButtons = document.querySelectorAll('.tag-filter-btn');
    
    tagButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tag = this.dataset.tag;
            
            if (tag === 'all') {
                window.location.href = '<?php echo esc_url(get_post_type_archive_link('game')); ?>';
            } else {
                const term = <?php echo json_encode(get_terms(array('taxonomy' => 'game_tag', 'hide_empty' => true))); ?>;
                const selectedTerm = term.find(t => t.slug === tag);
                if (selectedTerm) {
                    window.location.href = selectedTerm.link || '<?php echo esc_url(home_url('/game-tag/')); ?>' + tag + '/';
                }
            }
        });
    });
});
</script>
